version: '3.8'

services:
  # Backend FastAPI
  backend:
    build:
      context: ./back
      dockerfile: Dockerfile
    container_name: amvox_backend
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=************************************************/amvox_db
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
      - MICROSOFT_CLIENT_ID=0e7e58b3-146d-43fc-9201-d2d6a6583c00
      - MICROSOFT_CLIENT_SECRET=****************************************
      - MICROSOFT_TENANT_ID=ca682a90-2b2d-4030-b0f8-8989329705af
      - MICROSOFT_REDIRECT_URI=http://localhost:8001/api/v1/outlook/auth/callback
      # Configurações do 3CX
      - THREECX_REPORTS_URL=http://ticmobilerb.ddns.net/gdacip/apijson.php
      - THREECX_REPORTS_SERVER=http://ticmobilerb.ddns.net
      - THREECX_API_USER=amvox
      - THREECX_API_PASSWORD=super_7894
      - THREECX_ATTENDANCE_URL=https://callcentermobile.ddns.net/gdacx/gmag
      - THREECX_USER_LOGIN=teste_amvox
      - THREECX_USER_PASSWORD=1111
    env_file:
      - ./back/.env
    depends_on:
      - postgres
      - redis
    networks:
      - amvox_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Next.js
  frontend:
    build:
      context: ./front
      dockerfile: Dockerfile
    container_name: amvox_frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8001
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - amvox_network
    restart: unless-stopped

  # Banco de Dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: amvox_postgres
    environment:
      - POSTGRES_DB=amvox_db
      - POSTGRES_USER=amvox_user
      - POSTGRES_PASSWORD=amvox_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - amvox_network
    restart: unless-stopped

  # Redis para Cache
  redis:
    image: redis:7-alpine
    container_name: amvox_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - amvox_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: amvox_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - amvox_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  amvox_network:
    driver: bridge
