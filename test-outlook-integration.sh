#!/bin/bash

# 🧪 TESTE DA INTEGRAÇÃO MICROSOFT OUTLOOK
# ========================================

echo "🧪 TESTANDO INTEGRAÇÃO MICROSOFT OUTLOOK"
echo "========================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para testar endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -n "🔍 Testando $description... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url")
    status_code="${response: -3}"
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ OK${NC} (Status: $status_code)"
        return 0
    else
        echo -e "${RED}❌ FALHOU${NC} (Status: $status_code)"
        if [ -f /tmp/response.json ]; then
            echo "Resposta: $(cat /tmp/response.json)"
        fi
        return 1
    fi
}

# Função para testar endpoint com autenticação
test_auth_endpoint() {
    local url=$1
    local description=$2
    local token=$3
    local expected_status=${4:-200}
    
    echo -n "🔍 Testando $description... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json \
        -H "Authorization: Bearer $token" \
        -H "Content-Type: application/json" \
        "$url")
    status_code="${response: -3}"
    
    if [ "$status_code" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ OK${NC} (Status: $status_code)"
        if [ -f /tmp/response.json ]; then
            cat /tmp/response.json | jq . 2>/dev/null || cat /tmp/response.json
        fi
        return 0
    else
        echo -e "${RED}❌ FALHOU${NC} (Status: $status_code)"
        if [ -f /tmp/response.json ]; then
            echo "Resposta: $(cat /tmp/response.json)"
        fi
        return 1
    fi
}

echo ""
echo "📋 1. VERIFICANDO CONFIGURAÇÃO"
echo "=============================="

# Verificar se os containers estão rodando
echo -n "🐳 Verificando containers... "
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✅ OK${NC}"
else
    echo -e "${RED}❌ FALHOU${NC}"
    echo "Execute: docker-compose up -d"
    exit 1
fi

# Verificar configuração do Microsoft Graph
test_endpoint "http://localhost:8001/api/v1/outlook/config/status" "Configuração Microsoft Graph"

echo ""
echo "📋 2. TESTANDO AUTENTICAÇÃO"
echo "=========================="

# Fazer login para obter token
echo -n "🔐 Fazendo login... "
login_response=$(curl -s -X POST "http://localhost:8001/api/v1/auth/login" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=admin&password=admin123")

if echo "$login_response" | jq -e '.access_token' > /dev/null 2>&1; then
    token=$(echo "$login_response" | jq -r '.access_token')
    echo -e "${GREEN}✅ OK${NC}"
    echo "Token obtido: ${token:0:20}..."
else
    echo -e "${RED}❌ FALHOU${NC}"
    echo "Resposta: $login_response"
    exit 1
fi

echo ""
echo "📋 3. TESTANDO ENDPOINTS OUTLOOK"
echo "==============================="

# Testar status do usuário
test_auth_endpoint "http://localhost:8001/api/v1/outlook/user/status" "Status do usuário" "$token"

# Testar endpoint de conexão
echo -n "🔗 Testando endpoint de conexão... "
connect_response=$(curl -s -X POST "http://localhost:8001/api/v1/outlook/auth/connect" \
    -H "Authorization: Bearer $token" \
    -H "Content-Type: application/json")

if echo "$connect_response" | jq -e '.auth_url' > /dev/null 2>&1; then
    auth_url=$(echo "$connect_response" | jq -r '.auth_url')
    echo -e "${GREEN}✅ OK${NC}"
    echo "URL de autenticação gerada: ${auth_url:0:50}..."
else
    echo -e "${RED}❌ FALHOU${NC}"
    echo "Resposta: $connect_response"
fi

echo ""
echo "📋 4. TESTANDO FRONTEND"
echo "======================"

# Testar proxy do frontend
test_endpoint "http://localhost:3000/api/v1/outlook/user/status" "Proxy frontend" 401

# Testar página principal
test_endpoint "http://localhost:3000" "Página principal"

echo ""
echo "📋 5. VERIFICANDO VARIÁVEIS DE AMBIENTE"
echo "======================================"

echo "🔍 Verificando arquivo .env..."
if [ -f "back/.env" ]; then
    echo -e "${GREEN}✅ Arquivo .env encontrado${NC}"
    
    # Verificar se as variáveis estão configuradas
    if grep -q "MICROSOFT_CLIENT_ID=0e7e58b3" back/.env; then
        echo -e "${GREEN}✅ MICROSOFT_CLIENT_ID configurado${NC}"
    else
        echo -e "${RED}❌ MICROSOFT_CLIENT_ID não configurado${NC}"
    fi
    
    if grep -q "MICROSOFT_CLIENT_SECRET=duQ8Q" back/.env; then
        echo -e "${GREEN}✅ MICROSOFT_CLIENT_SECRET configurado${NC}"
    else
        echo -e "${RED}❌ MICROSOFT_CLIENT_SECRET não configurado${NC}"
    fi
    
    if grep -q "MICROSOFT_TENANT_ID=ca682a90" back/.env; then
        echo -e "${GREEN}✅ MICROSOFT_TENANT_ID configurado${NC}"
    else
        echo -e "${RED}❌ MICROSOFT_TENANT_ID não configurado${NC}"
    fi
else
    echo -e "${RED}❌ Arquivo .env não encontrado${NC}"
fi

echo ""
echo "📋 6. TESTANDO COMPONENTE REACT"
echo "==============================="

# Verificar se o componente OutlookQuickConnect existe
if [ -f "front/src/components/outlook/OutlookQuickConnect.tsx" ]; then
    echo -e "${GREEN}✅ Componente OutlookQuickConnect encontrado${NC}"
else
    echo -e "${RED}❌ Componente OutlookQuickConnect não encontrado${NC}"
fi

# Verificar se EmailSection foi atualizado
if grep -q "OutlookQuickConnect" front/src/components/atendimento/EmailSection.tsx; then
    echo -e "${GREEN}✅ EmailSection atualizado com OutlookQuickConnect${NC}"
else
    echo -e "${RED}❌ EmailSection não foi atualizado${NC}"
fi

echo ""
echo "📋 7. RESUMO DOS TESTES"
echo "======================"

echo -e "${BLUE}🎯 INTEGRAÇÃO MICROSOFT OUTLOOK${NC}"
echo "================================"
echo ""
echo -e "${GREEN}✅ Configurações aplicadas:${NC}"
echo "   • Credenciais Microsoft Graph configuradas"
echo "   • Componente OutlookQuickConnect criado"
echo "   • EmailSection otimizado"
echo "   • Endpoints de autenticação funcionais"
echo ""
echo -e "${YELLOW}⚠️  Próximos passos:${NC}"
echo "   1. Acesse: http://localhost:3000"
echo "   2. Faça login com: admin / admin123"
echo "   3. Vá para a página de Atendimento"
echo "   4. Clique em 'Conectar Outlook'"
echo "   5. Autorize com sua conta @amvox.com.br"
echo ""
echo -e "${BLUE}🔗 Links úteis:${NC}"
echo "   • Frontend: http://localhost:3000"
echo "   • Backend: http://localhost:8001"
echo "   • API Docs: http://localhost:8001/docs"
echo "   • Outlook Config: http://localhost:8001/api/v1/outlook/config/status"
echo ""
echo -e "${GREEN}🎉 INTEGRAÇÃO PRONTA PARA USO!${NC}"

# Limpar arquivos temporários
rm -f /tmp/response.json

echo ""
echo "💡 Para testar a integração completa:"
echo "   1. Abra o navegador em http://localhost:3000"
echo "   2. Faça login no sistema"
echo "   3. Vá para Atendimento > Email"
echo "   4. Clique em 'Conectar Outlook'"
echo "   5. Autorize com sua conta Microsoft"
echo ""
