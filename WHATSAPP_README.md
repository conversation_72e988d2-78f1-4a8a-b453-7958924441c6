# 📱 **WhatsApp Integration - Amvox Omnichannel**

## 🎯 **Status Atual**

✅ **Implementado:**
- Interface de chat funcional
- API endpoints estruturados
- Integração com Evolution API
- Proxy configurado no Next.js
- Schemas e modelos de dados
- Sistema de webhook

🔄 **Em Desenvolvimento:**
- Persistência no banco de dados
- WebSocket para tempo real
- Suporte a mídia (imagens, áudios)
- Respostas automáticas

---

## 🚀 **Início Rápido**

### **Opção 1: Configuração Automática (Recomendado)**

```bash
# Executar script de configuração
./setup_whatsapp.sh
```

### **Opção 2: Configuração Manual**

```bash
# 1. Configurar Evolution API
mkdir ~/evolution-api && cd ~/evolution-api
# [Ver QUICK_COMMANDS.md para detalhes]

# 2. Configurar banco de dados
docker cp back/migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -f /tmp/create_whatsapp_tables.sql

# 3. Configurar backend
cp back/.env.example back/.env
# Adicionar configurações da Evolution API

# 4. Reconstruir containers
docker-compose down && docker-compose build && docker-compose up -d
```

---

## 📋 **Funcionalidades**

### **✅ Implementadas**

#### **Interface de Usuário:**
- 🎨 Interface de chat moderna e responsiva
- 📱 Lista de conversas em tempo real
- 💬 Envio e recebimento de mensagens
- 📊 Estatísticas básicas (conversas, mensagens)
- 🔄 Status de conexão da instância

#### **Backend API:**
- 🔌 Integração completa com Evolution API
- 📡 Sistema de webhooks para receber mensagens
- 🏗️ Estrutura de dados bem definida
- 🔐 Autenticação via API Key
- 📝 Logs estruturados

#### **Endpoints Disponíveis:**
```
GET    /api/v1/whatsapp/stats
POST   /api/v1/whatsapp/instances
GET    /api/v1/whatsapp/instances/{name}/qr
GET    /api/v1/whatsapp/instances/{name}/status
DELETE /api/v1/whatsapp/instances/{name}
POST   /api/v1/whatsapp/messages/send
GET    /api/v1/whatsapp/conversations
GET    /api/v1/whatsapp/conversations/{id}/messages
POST   /api/v1/whatsapp/webhook
```

### **🔄 Em Desenvolvimento**

#### **Persistência:**
- 💾 Armazenamento no PostgreSQL
- 🔄 CRUD completo para conversas e mensagens
- 📈 Histórico de conversas
- 👥 Gestão de atendentes

#### **Tempo Real:**
- 🔔 WebSocket para notificações
- ⚡ Atualizações instantâneas
- 👀 Indicador de "digitando"
- 🔄 Sincronização automática

#### **Mídia:**
- 📷 Suporte a imagens
- 🎵 Suporte a áudios
- 📹 Suporte a vídeos
- 📄 Suporte a documentos

#### **Automação:**
- 🤖 Respostas automáticas
- ⏰ Mensagens fora do horário
- 🎯 Roteamento inteligente
- 📝 Templates de mensagem

---

## 🏗️ **Arquitetura**

### **Componentes Principais:**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │  Evolution API  │
│   (Next.js)     │◄──►│   (FastAPI)     │◄──►│   (WhatsApp)    │
│                 │    │                 │    │                 │
│ • WhatsAppChat  │    │ • API Routes    │    │ • QR Code       │
│ • WhatsAppSetup │    │ • Webhook       │    │ • Send/Receive  │
│ • Conversations │    │ • Integration   │    │ • Instance Mgmt │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   PostgreSQL    │◄─────────────┘
                        │   (Database)    │
                        │                 │
                        │ • Conversations │
                        │ • Messages      │
                        │ • Instances     │
                        └─────────────────┘
```

### **Fluxo de Dados:**

1. **Recebimento de Mensagem:**
   ```
   WhatsApp → Evolution API → Webhook → Backend → Database → Frontend
   ```

2. **Envio de Mensagem:**
   ```
   Frontend → Backend → Evolution API → WhatsApp
   ```

3. **Conexão de Instância:**
   ```
   Frontend → Backend → Evolution API → QR Code → WhatsApp Scan → Connected
   ```

---

## 🔧 **Configuração Detalhada**

### **Evolution API**

```yaml
# docker-compose.yml
version: '3.8'
services:
  evolution-api:
    image: atendai/evolution-api:latest
    ports:
      - "8080:8080"
    environment:
      - AUTHENTICATION_API_KEY=B6D711FCDE4D4FD5936544120E713976
      - WEBHOOK_GLOBAL_URL=http://host.docker.internal:8001/api/v1/whatsapp/webhook
```

### **Backend (.env)**

```bash
# Evolution API Configuration
EVOLUTION_API_URL=http://localhost:8080
EVOLUTION_API_KEY=B6D711FCDE4D4FD5936544120E713976
EVOLUTION_WEBHOOK_URL=http://localhost:8001/api/v1/whatsapp/webhook
```

### **Frontend (next.config.js)**

```javascript
async rewrites() {
  return [
    {
      source: '/api/v1/whatsapp/:path*',
      destination: 'http://backend:8001/api/v1/whatsapp/:path*',
    },
  ];
}
```

---

## 📊 **Banco de Dados**

### **Tabelas Principais:**

```sql
-- Instâncias WhatsApp
whatsapp_instances (
  id, name, status, qr_code, webhook_url, 
  phone_number, created_at, updated_at
)

-- Conversas
whatsapp_conversations (
  id, external_id, customer_name, customer_number,
  assigned_user_id, instance_id, status, last_message,
  unread_count, created_at, updated_at
)

-- Mensagens
whatsapp_messages (
  id, external_id, conversation_id, sender_type,
  sender_name, content, message_type, status,
  timestamp, created_at
)
```

### **Relacionamentos:**

```
whatsapp_instances (1) ──── (N) whatsapp_conversations
whatsapp_conversations (1) ──── (N) whatsapp_messages
usuarios (1) ──── (N) whatsapp_conversations (assigned_user_id)
```

---

## 🧪 **Testes**

### **Teste Manual:**

1. **Acessar:** http://localhost:3000/atendimento
2. **Clicar:** Aba "WhatsApp"
3. **Conectar:** Botão "Conectar WhatsApp"
4. **Escanear:** QR Code com WhatsApp Business
5. **Testar:** Enviar mensagem de teste

### **Teste via API:**

```bash
# Estatísticas
curl -s "http://localhost:3000/api/v1/whatsapp/stats" | jq .

# Criar instância
curl -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
  -H "Content-Type: application/json" \
  -d '{"instance_name": "test"}'

# Enviar mensagem
curl -X POST "http://localhost:3000/api/v1/whatsapp/messages/send" \
  -H "Content-Type: application/json" \
  -d '{
    "instance_name": "test",
    "number": "5511999999999",
    "text": "Teste"
  }'
```

---

## 📚 **Documentação**

### **Arquivos de Referência:**

- 📋 **WHATSAPP_IMPLEMENTATION_GUIDE.md** - Guia completo de implementação
- ⚡ **QUICK_COMMANDS.md** - Comandos rápidos e troubleshooting
- 🔧 **setup_whatsapp.sh** - Script de configuração automática

### **Código Principal:**

#### **Backend:**
- `back/app/api/v1/whatsapp.py` - Endpoints da API
- `back/app/schemas/whatsapp.py` - Modelos de dados
- `back/app/services/integrations/evolution/` - Cliente Evolution API
- `back/app/database/models/whatsapp.py` - Modelos do banco

#### **Frontend:**
- `front/src/components/atendimento/WhatsAppSection.tsx` - Container principal
- `front/src/components/atendimento/whatsapp/WhatsAppChat.tsx` - Interface de chat
- `front/src/components/atendimento/whatsapp/WhatsAppSetup.tsx` - Configuração

---

## 🆘 **Suporte e Troubleshooting**

### **Problemas Comuns:**

1. **API 404:** Verificar proxy do Next.js
2. **Evolution API não conecta:** Verificar porta 8080
3. **Webhook não funciona:** Verificar URL do webhook
4. **QR Code não aparece:** Verificar logs da Evolution API

### **Logs Úteis:**

```bash
# Logs do sistema
docker-compose logs -f

# Logs específicos
docker-compose logs backend
docker logs evolution_api

# Logs em tempo real
tail -f back/logs/whatsapp.log
```

### **Comandos de Diagnóstico:**

```bash
# Verificar serviços
docker-compose ps

# Verificar conectividade
curl -I http://localhost:3000
curl -I http://localhost:8001
curl -I http://localhost:8080

# Verificar banco
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -c "\dt whatsapp*"
```

---

## 🔮 **Roadmap**

### **Próximas Versões:**

#### **v1.1 - Persistência Completa**
- ✅ Banco de dados integrado
- ✅ CRUD completo
- ✅ Histórico de conversas

#### **v1.2 - Tempo Real**
- 🔔 WebSocket implementado
- ⚡ Notificações push
- 👀 Status de digitação

#### **v1.3 - Mídia e Automação**
- 📷 Suporte a imagens/áudios
- 🤖 Respostas automáticas
- 📝 Templates de mensagem

#### **v1.4 - Analytics**
- 📊 Dashboard de métricas
- 📈 Relatórios de performance
- 🎯 KPIs de atendimento

#### **v1.5 - Produção**
- 🔒 HTTPS e segurança
- 📦 Backup automático
- 🚀 Otimizações de performance

---

## 📞 **Contato**

- **Documentação Evolution API:** https://doc.evolution-api.com/
- **Issues GitHub:** https://github.com/EvolutionAPI/evolution-api/issues
- **Suporte Técnico:** Ver logs em `docker-compose logs -f`

---

**🎯 Status:** MVP Funcional ✅ | **Próximo:** Persistência no Banco 🔄
