# 🚀 **POC WhatsApp Webhook - Plano de Implementação**

## 🎯 **Objetivo**
Criar uma POC para receber webhooks da Evolution API e salvar conversas/mensagens no banco PostgreSQL.

## 📋 **Cenário Atual**
- ✅ Evolution API rodando com webhook configurado
- ✅ Banco PostgreSQL disponível
- ✅ Payload de exemplo fornecido
- 🔄 Webhook atual vai para automação (n8n)
- 🎯 Queremos replicar para nossa aplicação

## 🏗️ **Estrutura da POC**

### **1. Endpoint Webhook**
```
POST /api/v1/whatsapp/webhook
```

### **2. Processamento do Payload**
```json
{
  "event": "messages.upsert",
  "instance": "Priscila", 
  "data": {
    "key": {
      "remoteJid": "<EMAIL>",
      "fromMe": false,
      "id": "66684BB473B94CDAEEFB2874BF702FBC"
    },
    "pushName": "Tyandrer Boldt",
    "message": {
      "conversation": "Oi"
    },
    "messageType": "conversation",
    "messageTimestamp": 1751543933
  }
}
```

### **3. Dados a Extrair**
- **Instância**: `instance` (ex: "Priscila")
- **Cliente**: `pushName` + `remoteJid` 
- **Mensagem**: `message.conversation`
- **Timestamp**: `messageTimestamp`
- **Direção**: `fromMe` (false = cliente, true = agente)
- **ID único**: `key.id`

### **4. Estrutura do Banco**
```sql
-- Tabela simplificada para POC
CREATE TABLE poc_whatsapp_messages (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(200) UNIQUE NOT NULL,
    instance_name VARCHAR(100) NOT NULL,
    customer_name VARCHAR(200),
    customer_number VARCHAR(50) NOT NULL,
    message_content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text',
    from_me BOOLEAN DEFAULT FALSE,
    timestamp_received TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    raw_payload JSONB
);
```

## 🔧 **Implementação**

### **Passo 1: Criar Endpoint**
- Endpoint FastAPI para receber webhook
- Validação do payload
- Log de todas as mensagens recebidas

### **Passo 2: Processar Dados**
- Extrair informações do payload
- Normalizar número de telefone
- Converter timestamp Unix para datetime

### **Passo 3: Salvar no Banco**
- Inserir mensagem na tabela
- Evitar duplicatas (external_id único)
- Salvar payload completo em JSONB

### **Passo 4: Teste com Postman**
- Simular webhook com payload fornecido
- Verificar se dados são salvos corretamente
- Testar diferentes tipos de mensagem

### **Passo 5: Configurar ngrok**
- Expor webhook publicamente
- Configurar URL no Evolution API
- Testar com mensagens reais

## 📝 **Checklist da POC**

### **Backend**
- [ ] Criar endpoint `/api/v1/whatsapp/webhook`
- [ ] Implementar processamento do payload
- [ ] Criar tabela `poc_whatsapp_messages`
- [ ] Implementar inserção no banco
- [ ] Adicionar logs detalhados

### **Testes**
- [ ] Testar com Postman (payload exemplo)
- [ ] Verificar dados no banco
- [ ] Testar duplicatas
- [ ] Testar diferentes tipos de mensagem

### **Exposição**
- [ ] Configurar ngrok
- [ ] Obter URL pública
- [ ] Configurar webhook na Evolution API
- [ ] Testar com mensagens reais

## 🚀 **Próximos Passos Após POC**

1. **Expandir para conversas**: Agrupar mensagens por conversa
2. **Interface básica**: Visualizar mensagens recebidas
3. **Envio de mensagens**: Integrar com Evolution API
4. **Tempo real**: WebSocket para atualizações
5. **Interface completa**: Chat funcional

## 📞 **Informações Necessárias**

Para configurar o webhook, preciso:
- **URL da Evolution API**: Para configurar webhook
- **API Key**: Para autenticação
- **Instância**: Nome da instância (ex: "Priscila")

## 🔧 **Comandos da POC**

```bash
# 1. Criar tabela POC
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel

# 2. Iniciar ngrok
ngrok http 8001

# 3. Testar webhook
curl -X POST http://localhost:8001/api/v1/whatsapp/webhook \
  -H "Content-Type: application/json" \
  -d @payload_exemplo.json

# 4. Verificar dados
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel \
  -c "SELECT * FROM poc_whatsapp_messages ORDER BY created_at DESC LIMIT 5;"
```

---

**🎯 Foco da POC**: Receber webhook → Processar → Salvar → Verificar
**⏱️ Tempo estimado**: 2-3 horas
**🧪 Resultado**: Webhook funcional salvando mensagens no banco
