#!/bin/bash

# 🌐 Script para Configurar Evolution API na Nuvem
# Amvox Omnichannel - Cloud Evolution API Setup

set -e

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[WARNING] $1${NC}"; }
error() { echo -e "${RED}[ERROR] $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[INFO] $1${NC}"; }

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                    AMVOX OMNICHANNEL                         ║
║              Evolution API Cloud Configuration              ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

# Função para coletar informações
collect_info() {
    echo -e "\n${BLUE}📋 Vamos configurar sua Evolution API na nuvem${NC}\n"
    
    # URL da Evolution API
    read -p "🌐 URL da sua Evolution API (ex: https://api.exemplo.com): " EVOLUTION_URL
    if [[ ! "$EVOLUTION_URL" =~ ^https?:// ]]; then
        error "URL deve começar com http:// ou https://"
    fi
    
    # API Key
    read -p "🔑 API Key da Evolution API: " EVOLUTION_KEY
    if [[ -z "$EVOLUTION_KEY" ]]; then
        error "API Key é obrigatória"
    fi
    
    # Webhook URL
    echo -e "\n${YELLOW}🔗 Configuração do Webhook:${NC}"
    echo "1. Usar domínio público (recomendado para produção)"
    echo "2. Usar ngrok (para testes locais)"
    echo "3. Inserir URL manualmente"
    
    read -p "Escolha uma opção (1-3): " webhook_option
    
    case $webhook_option in
        1)
            read -p "🌐 Seu domínio público (ex: https://meusite.com.br): " DOMAIN
            WEBHOOK_URL="${DOMAIN}/api/v1/whatsapp/webhook"
            ;;
        2)
            setup_ngrok
            ;;
        3)
            read -p "🔗 URL completa do webhook: " WEBHOOK_URL
            ;;
        *)
            error "Opção inválida"
            ;;
    esac
    
    echo -e "\n${GREEN}✅ Informações coletadas:${NC}"
    echo "   Evolution API: $EVOLUTION_URL"
    echo "   API Key: ${EVOLUTION_KEY:0:10}..."
    echo "   Webhook: $WEBHOOK_URL"
    
    read -p "Confirma as informações? (y/n): " confirm
    if [[ "$confirm" != "y" ]]; then
        error "Configuração cancelada"
    fi
}

# Configurar ngrok
setup_ngrok() {
    log "Configurando ngrok..."
    
    # Verificar se ngrok está instalado
    if ! command -v ngrok &> /dev/null; then
        log "Instalando ngrok..."
        curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
        echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
        sudo apt update && sudo apt install ngrok
    fi
    
    # Iniciar ngrok em background
    log "Iniciando ngrok na porta 8001..."
    pkill ngrok || true
    ngrok http 8001 > /dev/null 2>&1 &
    sleep 3
    
    # Obter URL do ngrok
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null || echo "")
    
    if [[ -z "$NGROK_URL" || "$NGROK_URL" == "null" ]]; then
        error "Falha ao obter URL do ngrok. Verifique se está funcionando."
    fi
    
    WEBHOOK_URL="${NGROK_URL}/api/v1/whatsapp/webhook"
    log "✅ Ngrok configurado: $NGROK_URL"
}

# Testar Evolution API
test_evolution_api() {
    log "Testando conectividade com Evolution API..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/evolution_test.json \
        -X GET "$EVOLUTION_URL/manager/instance/fetchInstances" \
        -H "apikey: $EVOLUTION_KEY")
    
    if [[ "$response" == "200" ]]; then
        log "✅ Evolution API acessível"
        instances=$(cat /tmp/evolution_test.json | jq length 2>/dev/null || echo "0")
        info "Instâncias encontradas: $instances"
    else
        error "❌ Falha ao acessar Evolution API (HTTP $response)"
    fi
}

# Atualizar configurações do backend
update_backend_config() {
    log "Atualizando configurações do backend..."
    
    # Backup do .env atual
    if [[ -f "back/.env" ]]; then
        cp back/.env "back/.env.backup.$(date +%Y%m%d_%H%M%S)"
        log "Backup criado: back/.env.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Atualizar ou adicionar configurações
    if [[ -f "back/.env" ]]; then
        # Atualizar existente
        sed -i "s|EVOLUTION_API_URL=.*|EVOLUTION_API_URL=$EVOLUTION_URL|" back/.env
        sed -i "s|EVOLUTION_API_KEY=.*|EVOLUTION_API_KEY=$EVOLUTION_KEY|" back/.env
        sed -i "s|EVOLUTION_WEBHOOK_URL=.*|EVOLUTION_WEBHOOK_URL=$WEBHOOK_URL|" back/.env
        
        # Adicionar se não existir
        if ! grep -q "EVOLUTION_API_URL" back/.env; then
            echo "EVOLUTION_API_URL=$EVOLUTION_URL" >> back/.env
        fi
        if ! grep -q "EVOLUTION_API_KEY" back/.env; then
            echo "EVOLUTION_API_KEY=$EVOLUTION_KEY" >> back/.env
        fi
        if ! grep -q "EVOLUTION_WEBHOOK_URL" back/.env; then
            echo "EVOLUTION_WEBHOOK_URL=$WEBHOOK_URL" >> back/.env
        fi
    else
        # Criar novo .env
        cp back/.env.example back/.env
        cat >> back/.env << EOF

# Configurações da Evolution API (Cloud)
EVOLUTION_API_URL=$EVOLUTION_URL
EVOLUTION_API_KEY=$EVOLUTION_KEY
EVOLUTION_WEBHOOK_URL=$WEBHOOK_URL
EOF
    fi
    
    log "✅ Configurações atualizadas"
}

# Reiniciar backend
restart_backend() {
    log "Reiniciando backend..."
    
    docker-compose restart backend
    
    # Aguardar inicialização
    log "Aguardando backend inicializar..."
    sleep 15
    
    # Testar se está funcionando
    if curl -s -f "http://localhost:8001/api/v1/whatsapp/stats" > /dev/null; then
        log "✅ Backend reiniciado com sucesso"
    else
        warn "⚠️  Backend pode ainda estar inicializando"
    fi
}

# Configurar webhook na Evolution API
configure_webhook() {
    log "Configurando webhook na Evolution API..."
    
    # Listar instâncias existentes
    instances=$(curl -s "$EVOLUTION_URL/manager/instance/fetchInstances" \
        -H "apikey: $EVOLUTION_KEY" | jq -r '.[].instance.instanceName' 2>/dev/null || echo "")
    
    if [[ -z "$instances" ]]; then
        warn "Nenhuma instância encontrada. Webhook será configurado quando criar uma instância."
        return
    fi
    
    # Configurar webhook para cada instância
    while IFS= read -r instance; do
        if [[ -n "$instance" ]]; then
            log "Configurando webhook para instância: $instance"
            
            response=$(curl -s -w "%{http_code}" -o /tmp/webhook_response.json \
                -X POST "$EVOLUTION_URL/webhook/set/$instance" \
                -H "apikey: $EVOLUTION_KEY" \
                -H "Content-Type: application/json" \
                -d "{
                    \"url\": \"$WEBHOOK_URL\",
                    \"events\": [
                        \"messages.upsert\",
                        \"connection.update\",
                        \"messages.update\"
                    ]
                }")
            
            if [[ "$response" == "200" || "$response" == "201" ]]; then
                log "✅ Webhook configurado para $instance"
            else
                warn "⚠️  Falha ao configurar webhook para $instance (HTTP $response)"
            fi
        fi
    done <<< "$instances"
}

# Testar integração completa
test_integration() {
    log "Testando integração completa..."
    
    # Testar API local
    if curl -s -f "http://localhost:3000/api/v1/whatsapp/stats" > /dev/null; then
        log "✅ API local funcionando"
    else
        warn "⚠️  API local não respondeu"
    fi
    
    # Testar criação de instância de teste
    log "Testando criação de instância..."
    response=$(curl -s -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
        -H "Content-Type: application/json" \
        -d '{
            "instance_name": "amvox_test_cloud",
            "webhook_url": "'$WEBHOOK_URL'"
        }' || echo "ERROR")
    
    if [[ "$response" != "ERROR" ]] && [[ "$response" == *"success"* ]]; then
        log "✅ Criação de instância funcionando"
        
        # Testar QR Code
        sleep 3
        if curl -s -f "http://localhost:3000/api/v1/whatsapp/instances/amvox_test_cloud/qr" > /dev/null; then
            log "✅ QR Code disponível"
        else
            warn "⚠️  QR Code não disponível ainda"
        fi
    else
        warn "⚠️  Falha na criação de instância"
    fi
}

# Mostrar resultado final
show_result() {
    echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                 CONFIGURAÇÃO CONCLUÍDA                      ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo -e "\n${BLUE}📋 CONFIGURAÇÕES APLICADAS:${NC}"
    echo -e "   • Evolution API: ${GREEN}$EVOLUTION_URL${NC}"
    echo -e "   • API Key: ${GREEN}${EVOLUTION_KEY:0:10}...${NC}"
    echo -e "   • Webhook: ${GREEN}$WEBHOOK_URL${NC}"
    
    echo -e "\n${BLUE}🚀 PRÓXIMOS PASSOS:${NC}"
    echo -e "   1. Acesse: ${GREEN}http://localhost:3000/atendimento${NC}"
    echo -e "   2. Clique na aba ${YELLOW}'WhatsApp'${NC}"
    echo -e "   3. Clique em ${YELLOW}'Conectar WhatsApp'${NC}"
    echo -e "   4. Escaneie o QR Code"
    
    if [[ "$webhook_option" == "2" ]]; then
        echo -e "\n${YELLOW}⚠️  IMPORTANTE (ngrok):${NC}"
        echo -e "   • Mantenha o terminal do ngrok aberto"
        echo -e "   • URL do ngrok: ${GREEN}$NGROK_URL${NC}"
        echo -e "   • Para produção, configure um domínio fixo"
    fi
    
    echo -e "\n${BLUE}🔧 COMANDOS ÚTEIS:${NC}"
    echo -e "   • Ver logs: ${YELLOW}docker-compose logs -f backend${NC}"
    echo -e "   • Testar API: ${YELLOW}curl http://localhost:3000/api/v1/whatsapp/stats${NC}"
    echo -e "   • Reconfigurar: ${YELLOW}./configure_evolution_cloud.sh${NC}"
}

# Função principal
main() {
    collect_info
    test_evolution_api
    update_backend_config
    restart_backend
    configure_webhook
    test_integration
    show_result
}

# Executar
main
