# 📋 **G<PERSON><PERSON>: Implementação WhatsApp Omnichannel**

## 🎯 **Visão Geral**

Este documento detalha todos os passos necessários para completar a implementação do WhatsApp no sistema Omnichannel Amvox, desde a configuração básica até funcionalidades avançadas.

**Status Atual:** ✅ Interface básica implementada, API estruturada, proxy configurado
**Próximo Objetivo:** 🔧 Configurar Evolution API e persistência no banco

---

## **FASE 1: CONFIGURAÇÃO BÁSICA** 🔧

### **Passo 1.1: Configurar Evolution API**

#### **1.1.1 - Instalação via Docker (Recomendado)**

```bash
# 1. Criar diretório para Evolution API
mkdir ~/evolution-api
cd ~/evolution-api

# 2. <PERSON>riar docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  evolution-api:
    image: atendai/evolution-api:latest
    container_name: evolution_api
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SERVER_URL=http://localhost:8080
      - CORS_ORIGIN=*
      - CORS_METHODS=GET,POST,PUT,DELETE
      - CORS_CREDENTIALS=true
      - AUTHENTICATION_TYPE=apikey
      - AUTHENTICATION_API_KEY=B6D711FCDE4D4FD5936544120E713976
      - AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=true
      - WEBHOOK_GLOBAL_URL=http://host.docker.internal:8001/api/v1/whatsapp/webhook
      - WEBHOOK_GLOBAL_ENABLED=true
      - QRCODE_LIMIT=30
      - QRCODE_COLOR=#198754
      - INSTANCE_EXPIRE_TIME=false
      - DEL_INSTANCE=false
    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store
    networks:
      - evolution_network

volumes:
  evolution_instances:
  evolution_store:

networks:
  evolution_network:
    driver: bridge
EOF

# 3. Iniciar Evolution API
docker-compose up -d

# 4. Verificar se está funcionando
sleep 30
curl http://localhost:8080/manager/instance/fetchInstances \
  -H "apikey: B6D711FCDE4D4FD5936544120E713976"
```

#### **1.1.2 - Verificação da Instalação**

```bash
# Testar endpoints principais
echo "Testando Evolution API..."

# 1. Listar instâncias
curl -X GET "http://localhost:8080/manager/instance/fetchInstances" \
  -H "apikey: B6D711FCDE4D4FD5936544120E713976"

# 2. Verificar saúde da API
curl -X GET "http://localhost:8080/manager/instance/connectionState/test" \
  -H "apikey: B6D711FCDE4D4FD5936544120E713976"

echo "✅ Evolution API configurada com sucesso!"
```

---

### **Passo 1.2: Configurar Banco de Dados**

#### **1.2.1 - Executar Migração SQL**

```bash
# 1. Verificar se o arquivo de migração existe
ls -la back/migrations/create_whatsapp_tables.sql

# 2. Copiar arquivo para o container PostgreSQL
docker cp back/migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/

# 3. Executar migração
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -f /tmp/create_whatsapp_tables.sql

# 4. Verificar tabelas criadas
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -c "\dt whatsapp*"
```

#### **1.2.2 - Verificar Estrutura do Banco**

```sql
-- Conectar ao banco para verificação manual
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel

-- Verificar tabelas
\dt whatsapp*

-- Verificar estrutura
\d whatsapp_instances
\d whatsapp_conversations  
\d whatsapp_messages

-- Sair
\q
```

---

### **Passo 1.3: Atualizar Configurações**

#### **1.3.1 - Configurar Variáveis de Ambiente**

```bash
# 1. Atualizar .env do backend
cd back
cp .env.example .env

# 2. Editar configurações da Evolution API
cat >> .env << 'EOF'

# Configurações da Evolution API (WhatsApp)
EVOLUTION_API_URL=http://localhost:8080
EVOLUTION_API_KEY=B6D711FCDE4D4FD5936544120E713976
EVOLUTION_WEBHOOK_URL=http://localhost:8001/api/v1/whatsapp/webhook
EOF
```

#### **1.3.2 - Reconstruir e Reiniciar Containers**

```bash
# 1. Parar containers
docker-compose down

# 2. Reconstruir backend com novas configurações
docker-compose build backend

# 3. Iniciar todos os serviços
docker-compose up -d

# 4. Verificar logs
docker-compose logs -f backend
```

---

### **Passo 1.4: Testar Integração Básica**

#### **1.4.1 - Teste da API WhatsApp**

```bash
# 1. Testar endpoint de estatísticas
curl -s "http://localhost:3000/api/v1/whatsapp/stats" | jq .

# 2. Testar criação de instância
curl -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
  -H "Content-Type: application/json" \
  -d '{
    "instance_name": "amvox_test",
    "webhook_url": "http://localhost:8001/api/v1/whatsapp/webhook"
  }' | jq .

# 3. Obter QR Code
curl -s "http://localhost:3000/api/v1/whatsapp/instances/amvox_test/qr" | jq .
```

#### **1.4.2 - Teste da Interface**

1. **Acessar:** http://localhost:3000/atendimento
2. **Clicar na aba "WhatsApp"**
3. **Clicar em "Conectar WhatsApp"**
4. **Verificar se o QR Code aparece**
5. **Escanear com WhatsApp Business**

---

## **FASE 2: PERSISTÊNCIA E CRUD** 💾

### **Passo 2.1: Integrar CRUD com Banco de Dados**

#### **2.1.1 - Atualizar Webhook Handler**

```bash
# Arquivo: back/app/services/integrations/evolution/webhook_handler.py
# Substituir implementação em memória por persistência no banco
```

#### **2.1.2 - Implementar Serviços de Banco**

```bash
# Arquivo: back/app/crud/whatsapp.py
# Já criado - implementar integração nos endpoints
```

#### **2.1.3 - Atualizar Endpoints da API**

```bash
# Arquivo: back/app/api/v1/whatsapp.py
# Integrar com CRUD do banco de dados
```

### **Passo 2.2: Testar Persistência**

```bash
# 1. Criar instância e verificar no banco
curl -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
  -H "Content-Type: application/json" \
  -d '{"instance_name": "test_persistence"}'

# 2. Verificar no banco
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel \
  -c "SELECT * FROM whatsapp_instances;"

# 3. Enviar mensagem de teste
curl -X POST "http://localhost:3000/api/v1/whatsapp/messages/send" \
  -H "Content-Type: application/json" \
  -d '{
    "instance_name": "test_persistence",
    "number": "5511999999999",
    "text": "Teste de persistência"
  }'

# 4. Verificar mensagens no banco
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel \
  -c "SELECT * FROM whatsapp_messages ORDER BY created_at DESC LIMIT 5;"
```

---

## **FASE 3: TEMPO REAL E WEBSOCKET** 🔔

### **Passo 3.1: Implementar WebSocket**

#### **3.1.1 - Adicionar WebSocket ao Backend**

```bash
# Arquivo: back/app/websocket/whatsapp_manager.py
# Já criado - integrar com main.py
```

#### **3.1.2 - Configurar Endpoints WebSocket**

```python
# Adicionar ao back/app/main.py
from app.websocket.whatsapp_manager import whatsapp_ws_manager

@app.websocket("/ws/whatsapp/{user_id}")
async def whatsapp_websocket_endpoint(websocket: WebSocket, user_id: int):
    await whatsapp_ws_manager.connect(websocket, user_id)
    try:
        while True:
            data = await websocket.receive_text()
            # Processar mensagens do cliente
    except WebSocketDisconnect:
        whatsapp_ws_manager.disconnect(websocket, user_id)
```

### **Passo 3.2: Implementar WebSocket no Frontend**

#### **3.2.1 - Criar Hook de WebSocket**

```typescript
// Arquivo: front/src/hooks/useWhatsAppWebSocket.ts
import { useEffect, useState } from 'react';

export const useWhatsAppWebSocket = (userId: number) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [messages, setMessages] = useState<any[]>([]);

  useEffect(() => {
    const ws = new WebSocket(`ws://localhost:8001/ws/whatsapp/${userId}`);
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setMessages(prev => [...prev, data]);
    };

    setSocket(ws);

    return () => {
      ws.close();
    };
  }, [userId]);

  return { socket, messages };
};
```

#### **3.2.2 - Integrar WebSocket no Chat**

```typescript
// Atualizar: front/src/components/atendimento/whatsapp/WhatsAppChat.tsx
// Adicionar useWhatsAppWebSocket hook
```

---

## **FASE 4: FUNCIONALIDADES AVANÇADAS** 🚀

### **Passo 4.1: Implementar Suporte a Mídia**

#### **4.1.1 - Configurar Upload de Arquivos**

```bash
# 1. Criar diretório de mídia
mkdir -p back/media/whatsapp

# 2. Adicionar endpoint de upload
# Arquivo: back/app/api/v1/whatsapp.py
```

#### **4.1.2 - Implementar Download de Mídia**

```bash
# Arquivo: back/app/services/media/whatsapp_media.py
# Já criado - integrar com endpoints
```

### **Passo 4.2: Implementar Respostas Automáticas**

#### **4.2.1 - Configurar Auto-Reply**

```bash
# Arquivo: back/app/services/whatsapp/auto_reply.py
# Já criado - integrar com webhook handler
```

#### **4.2.2 - Criar Interface de Configuração**

```typescript
// Arquivo: front/src/components/admin/WhatsAppAutoReply.tsx
// Criar interface para gerenciar respostas automáticas
```

### **Passo 4.3: Implementar Analytics**

#### **4.3.1 - Configurar Métricas**

```bash
# Arquivo: back/app/services/whatsapp/analytics.py
# Já criado - criar endpoints de relatórios
```

#### **4.3.2 - Criar Dashboard de Analytics**

```typescript
// Arquivo: front/src/components/analytics/WhatsAppDashboard.tsx
// Criar dashboard com gráficos e métricas
```

---

## **FASE 5: PRODUÇÃO E OTIMIZAÇÃO** 🏭

### **Passo 5.1: Configuração para Produção**

#### **5.1.1 - Configurar HTTPS**

```bash
# 1. Obter certificado SSL
# 2. Configurar nginx com SSL
# 3. Atualizar URLs da Evolution API
```

#### **5.1.2 - Configurar Backup**

```bash
# 1. Backup automático do banco
# 2. Backup de arquivos de mídia
# 3. Configurar monitoramento
```

### **Passo 5.2: Otimização de Performance**

#### **5.2.1 - Configurar Cache**

```bash
# 1. Implementar cache Redis para conversas
# 2. Cache de mídia
# 3. Cache de estatísticas
```

#### **5.2.2 - Monitoramento**

```bash
# 1. Logs estruturados
# 2. Métricas de performance
# 3. Alertas de erro
```

---

## **📋 CHECKLIST DE IMPLEMENTAÇÃO**

### **✅ Fase 1 - Básico**
- [ ] Evolution API instalada e funcionando
- [ ] Banco de dados migrado
- [ ] Variáveis de ambiente configuradas
- [ ] Containers reconstruídos
- [ ] Teste básico de integração

### **🔄 Fase 2 - Persistência**
- [ ] CRUD integrado com banco
- [ ] Webhook handler persistindo dados
- [ ] Endpoints atualizados
- [ ] Testes de persistência

### **🔔 Fase 3 - Tempo Real**
- [ ] WebSocket implementado no backend
- [ ] WebSocket integrado no frontend
- [ ] Notificações em tempo real
- [ ] Testes de conectividade

### **🚀 Fase 4 - Avançado**
- [ ] Suporte a mídia (imagens, áudios)
- [ ] Respostas automáticas
- [ ] Analytics e relatórios
- [ ] Interface de administração

### **🏭 Fase 5 - Produção**
- [ ] HTTPS configurado
- [ ] Backup automatizado
- [ ] Cache implementado
- [ ] Monitoramento ativo

---

## **🆘 TROUBLESHOOTING**

### **Problemas Comuns:**

1. **Evolution API não conecta:**
   ```bash
   # Verificar logs
   docker logs evolution_api
   
   # Verificar portas
   netstat -tulpn | grep 8080
   ```

2. **Webhook não recebe dados:**
   ```bash
   # Verificar URL do webhook
   curl -X GET "http://localhost:8080/webhook/find/instanceName" \
     -H "apikey: B6D711FCDE4D4FD5936544120E713976"
   ```

3. **Banco de dados não conecta:**
   ```bash
   # Verificar conexão
   docker exec -it amvox_postgres pg_isready
   
   # Verificar logs
   docker logs amvox_postgres
   ```

---

## **📞 SUPORTE**

- **Documentação Evolution API:** https://doc.evolution-api.com/
- **Issues GitHub:** https://github.com/EvolutionAPI/evolution-api/issues
- **Logs do Sistema:** `docker-compose logs -f`

---

**🎯 Próximo Passo Recomendado:** Executar Fase 1 - Configuração Básica
