# 📱 **AMVOX OMNICHANNEL - WHATSAPP INTEGRATION**
## **Documentação Completa do Sistema de Atendimento**

---

### **ÍNDICE**

1. [Visão Geral](#visao-geral)
2. [Arquitetura do Sistema](#arquitetura)
3. [Fluxo de Atendimento](#fluxo)
4. [Componentes Técnicos](#componentes)
5. [Interface do Usuário](#interface)
6. [Configuração e Instalação](#configuracao)
7. [Automações e IA](#automacoes)
8. [Métricas e Relatórios](#metricas)
9. [Segurança e Compliance](#seguranca)
10. [Guia de Implementação](#implementacao)

---

## **1. VISÃO GERAL** {#visao-geral}

### **1.1 Objetivo do Sistema**

O sistema de atendimento WhatsApp da Amvox Omnichannel foi desenvolvido para proporcionar um atendimento profissional e eficiente através da plataforma WhatsApp Business, integrando-se perfeitamente ao ecossistema existente da empresa.

### **1.2 Principais Funcionalidades**

- ✅ **Chat em Tempo Real**: Interface moderna para conversas instantâneas
- ✅ **Gestão de Conversas**: Organização e distribuição inteligente
- ✅ **Respostas Automáticas**: Sistema inteligente de auto-atendimento
- ✅ **Suporte a Mídia**: Imagens, áudios, vídeos e documentos
- ✅ **Analytics Avançado**: Métricas e relatórios detalhados
- ✅ **Multi-atendente**: Gestão de equipe centralizada

### **1.3 Benefícios**

#### **Para a Empresa:**
- 📈 Aumento da eficiência no atendimento
- 💰 Redução de custos operacionais
- 📊 Métricas detalhadas de performance
- 🎯 Melhor gestão de recursos humanos

#### **Para os Atendentes:**
- 🖥️ Interface intuitiva e moderna
- ⚡ Ferramentas que agilizam o atendimento
- 📱 Acesso ao histórico completo do cliente
- 🤖 Automações que reduzem trabalho repetitivo

#### **Para os Clientes:**
- 📱 Atendimento no canal preferido (WhatsApp)
- ⚡ Respostas mais rápidas
- 🕐 Disponibilidade 24/7 (auto-resposta)
- 😊 Experiência personalizada

---

## **2. ARQUITETURA DO SISTEMA** {#arquitetura}

### **2.1 Visão Geral da Arquitetura**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   📱 Cliente    │    │  🌐 Evolution   │    │  🖥️ Backend     │    │  💻 Frontend    │
│   WhatsApp      │◄──►│     API         │◄──►│   Amvox         │◄──►│   Interface     │
│                 │    │                 │    │                 │    │                 │
│ • Envia msg     │    │ • Recebe msg    │    │ • Processa      │    │ • Atendente     │
│ • Recebe resp   │    │ • Envia resp    │    │ • Armazena      │    │ • Visualiza     │
│ • Mídia         │    │ • QR Code       │    │ • Webhook       │    │ • Responde      │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                │              ┌─────────────────┐
                                │              │  🗄️ PostgreSQL  │
                                │              │   Database      │
                                │              │                 │
                                └──────────────┤ • Conversas     │
                                               │ • Mensagens     │
                                               │ • Atendentes    │
                                               └─────────────────┘
```

### **2.2 Componentes Principais**

#### **🌐 Evolution API**
- **Função**: Ponte entre WhatsApp e o sistema
- **Responsabilidades**:
  - Conectar com WhatsApp Business API
  - Gerenciar QR Code para autenticação
  - Receber e enviar mensagens
  - Processar mídia (imagens, áudios, documentos)
  - Manter status de conexão

#### **🖥️ Backend Amvox (FastAPI)**
- **Função**: Cérebro do sistema de atendimento
- **Responsabilidades**:
  - Processar webhooks da Evolution API
  - Gerenciar conversas e mensagens
  - Aplicar regras de negócio
  - Fornecer APIs para o frontend
  - Executar automações

#### **💻 Frontend (Next.js/React)**
- **Função**: Interface para atendentes
- **Responsabilidades**:
  - Exibir conversas em tempo real
  - Permitir envio de mensagens
  - Mostrar métricas e relatórios
  - Gerenciar configurações

#### **🗄️ PostgreSQL Database**
- **Função**: Armazenamento persistente
- **Responsabilidades**:
  - Armazenar conversas e mensagens
  - Manter histórico de atendimentos
  - Guardar configurações do sistema
  - Armazenar métricas e relatórios

### **2.3 Tecnologias Utilizadas**

#### **Backend:**
- **FastAPI**: Framework web moderno e rápido
- **SQLAlchemy**: ORM para banco de dados
- **Pydantic**: Validação de dados
- **Asyncio**: Programação assíncrona
- **WebSocket**: Comunicação em tempo real

#### **Frontend:**
- **Next.js**: Framework React com SSR
- **TypeScript**: Tipagem estática
- **Tailwind CSS**: Framework CSS utilitário
- **React Query**: Gerenciamento de estado
- **WebSocket**: Atualizações em tempo real

#### **Infraestrutura:**
- **Docker**: Containerização
- **PostgreSQL**: Banco de dados relacional
- **Nginx**: Proxy reverso
- **Evolution API**: Integração WhatsApp

---

## **3. FLUXO DE ATENDIMENTO** {#fluxo}

### **3.1 Fluxo Completo de Mensagem**

#### **📥 Recebimento de Mensagem**

```
1. Cliente envia mensagem no WhatsApp
   ↓
2. Evolution API recebe a mensagem
   ↓
3. Evolution API envia webhook para backend
   ↓
4. Backend processa e identifica:
   - Cliente (nome, número)
   - Conversa (nova ou existente)
   - Tipo de mensagem (texto, mídia)
   ↓
5. Backend salva no banco de dados
   ↓
6. Sistema aplica regras de negócio:
   - Auto-resposta se configurada
   - Roteamento para atendente
   - Notificações
   ↓
7. Frontend atualiza em tempo real via WebSocket
```

#### **📤 Envio de Resposta**

```
1. Atendente digita resposta na interface
   ↓
2. Frontend envia para backend via API
   ↓
3. Backend valida e salva no banco
   ↓
4. Backend envia para Evolution API
   ↓
5. Evolution API entrega para WhatsApp
   ↓
6. Cliente recebe a mensagem
   ↓
7. Status de entrega é atualizado no sistema
```

### **3.2 Cenários Detalhados**

#### **🆕 Primeira Mensagem do Cliente**

```
Cenário: Cliente "João Silva" envia primeira mensagem

1. Mensagem: "Olá, gostaria de informações sobre produtos"

2. Sistema identifica: NOVA CONVERSA
   - Cria registro no banco
   - ID: conv_001
   - Cliente: João Silva (+5511999999999)
   - Status: AGUARDANDO_ATENDIMENTO

3. Auto-resposta (se configurada):
   "Olá João! Obrigado pelo contato. Um atendente irá responder em breve."

4. Notificação para atendentes:
   - Popup: "Nova conversa aguardando"
   - Som de notificação
   - Contador atualizado

5. Interface atualiza:
   - Conversa aparece na lista
   - Badge de "não lida"
   - Informações do cliente
```

#### **👨‍💼 Atendente Assume Conversa**

```
Cenário: Atendente "Maria" assume a conversa

1. Maria clica em "Assumir conversa"

2. Sistema atualiza:
   - Status: EM_ATENDIMENTO
   - Atendente: Maria (ID: 123)
   - Timestamp: 2024-01-15 14:30:00

3. Notificações:
   - Para Maria: "Conversa atribuída"
   - Para outros: "Conversa assumida por Maria"

4. Interface de Maria:
   - Chat ativo carregado
   - Histórico completo visível
   - Ferramentas de atendimento disponíveis

5. Cliente recebe (opcional):
   "Olá! Sou a Maria e vou ajudá-lo hoje."
```

#### **💬 Troca de Mensagens**

```
Cenário: Conversa ativa entre Maria e João

1. Maria: "Olá João! Em que posso ajudá-lo?"
   - Salva no banco com sender_type: "agent"
   - Envia via Evolution API
   - Status: "enviada" → "entregue" → "lida"

2. João: "Quero saber sobre preços de notebooks"
   - Recebida via webhook
   - Salva com sender_type: "customer"
   - Notifica Maria em tempo real

3. Maria: "Temos várias opções! Qual sua faixa de preço?"
   - Processo se repete

4. Sistema registra:
   - Tempo de resposta de Maria: 45 segundos
   - Atualiza métricas em tempo real
   - Mantém conversa ativa
```

#### **📁 Envio de Mídia**

```
Cenário: Cliente envia foto de produto

1. João envia foto via WhatsApp

2. Evolution API recebe mídia:
   - URL temporária da imagem
   - Metadados (tamanho, tipo)

3. Backend processa:
   - Baixa a imagem
   - Salva em: /media/whatsapp/conv_001/image_001.jpg
   - Cria thumbnail para preview
   - Salva referência no banco

4. Interface de Maria:
   - Mostra preview da imagem
   - Permite visualização em tamanho real
   - Opção de download

5. Maria pode responder:
   - Com texto sobre a imagem
   - Enviando imagem de resposta
   - Usando templates relacionados
```

---

## **4. COMPONENTES TÉCNICOS** {#componentes}

### **4.1 Backend - Estrutura de APIs**

#### **📡 Endpoints Principais**

```python
# Estatísticas
GET /api/v1/whatsapp/stats
Response: {
  "total_conversations": 150,
  "active_conversations": 12,
  "waiting_conversations": 3,
  "unread_messages": 8
}

# Gestão de Instâncias
POST /api/v1/whatsapp/instances
GET /api/v1/whatsapp/instances/{name}/qr
GET /api/v1/whatsapp/instances/{name}/status
DELETE /api/v1/whatsapp/instances/{name}

# Conversas
GET /api/v1/whatsapp/conversations
GET /api/v1/whatsapp/conversations/{id}/messages
POST /api/v1/whatsapp/conversations/{id}/assign

# Mensagens
POST /api/v1/whatsapp/messages/send
POST /api/v1/whatsapp/webhook
```

#### **🗄️ Modelos de Dados**

```python
# Instância WhatsApp
class WhatsAppInstance:
    id: int
    name: str
    status: InstanceStatus  # connected, disconnected, qr_code
    qr_code: Optional[str]
    phone_number: Optional[str]
    created_at: datetime

# Conversa
class WhatsAppConversation:
    id: int
    external_id: str  # instance_customer_number
    customer_name: str
    customer_number: str
    assigned_user_id: Optional[int]
    instance_id: int
    status: ConversationStatus  # open, closed, waiting, assigned
    last_message: Optional[str]
    unread_count: int
    created_at: datetime

# Mensagem
class WhatsAppMessage:
    id: int
    external_id: str  # ID da Evolution API
    conversation_id: int
    sender_type: str  # customer, agent
    sender_name: str
    content: str
    message_type: MessageType  # text, image, audio, video
    status: MessageStatus  # sent, delivered, read
    timestamp: datetime
```

### **4.2 Frontend - Componentes React**

#### **📱 WhatsAppSection (Container Principal)**

```typescript
interface WhatsAppSectionProps {
  // Estado da conexão
  isConnected: boolean;
  instanceName: string;

  // Estatísticas
  stats: {
    total_conversations: number;
    waiting_conversations: number;
    unread_messages: number;
  };

  // Funções
  onConnect: () => void;
  onStatsUpdate: (stats: any) => void;
}
```

#### **💬 WhatsAppChat (Interface de Chat)**

```typescript
interface WhatsAppChatProps {
  // Conversas e mensagens
  conversations: WhatsAppConversation[];
  selectedConversation: WhatsAppConversation | null;
  messages: WhatsAppMessage[];

  // Estados
  isLoading: boolean;
  isSending: boolean;

  // Funções
  onSelectConversation: (conv: WhatsAppConversation) => void;
  onSendMessage: (message: string) => void;
  onLoadMessages: (conversationId: string) => void;
}
```

#### **⚙️ WhatsAppSetup (Configuração)**

```typescript
interface WhatsAppSetupProps {
  instanceName: string;
  onConnect: () => void;

  // Estados do setup
  step: 'initial' | 'creating' | 'qr' | 'connecting';
  qrCode: string | null;
  error: string | null;
}
```

### **4.3 Banco de Dados - Estrutura**

#### **📊 Tabelas Principais**

```sql
-- Instâncias WhatsApp
CREATE TABLE whatsapp_instances (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    status instance_status DEFAULT 'disconnected',
    qr_code TEXT,
    webhook_url VARCHAR(500),
    phone_number VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversas
CREATE TABLE whatsapp_conversations (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(200) UNIQUE NOT NULL,
    customer_name VARCHAR(200) NOT NULL,
    customer_number VARCHAR(50) NOT NULL,
    assigned_user_id INTEGER REFERENCES usuarios(id),
    instance_id INTEGER REFERENCES whatsapp_instances(id),
    status conversation_status DEFAULT 'waiting',
    last_message TEXT,
    last_message_time TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Mensagens
CREATE TABLE whatsapp_messages (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(200) UNIQUE NOT NULL,
    conversation_id INTEGER REFERENCES whatsapp_conversations(id),
    sender_type VARCHAR(20) NOT NULL,
    sender_name VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    message_type message_type DEFAULT 'text',
    status message_status DEFAULT 'sent',
    media_url VARCHAR(500),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **🔗 Relacionamentos**

```
whatsapp_instances (1) ──── (N) whatsapp_conversations
whatsapp_conversations (1) ──── (N) whatsapp_messages
usuarios (1) ──── (N) whatsapp_conversations (assigned_user_id)
```

---

## **5. INTERFACE DO USUÁRIO** {#interface}

### **5.1 Dashboard Principal**

#### **📊 Visão Geral**

```
┌─────────────────────────────────────────────────────────────────┐
│  📱 WhatsApp Business - Dashboard                               │
├─────────────────────────────────────────────────────────────────┤
│  🔴 Conectado: amvox_instance    📊 Conversas: 25   ⏱️ 2min     │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │     25      │  │      3      │  │      8      │             │
│  │ Conversas   │  │ Pendentes   │  │ Não lidas   │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

#### **🎛️ Controles Principais**

- **🔄 Status da Conexão**: Indicador visual do status da instância
- **📊 Métricas em Tempo Real**: Conversas, pendências, não lidas
- **⚙️ Configurações**: Acesso rápido às configurações
- **📈 Relatórios**: Link para dashboard de analytics

### **5.2 Interface de Chat**

#### **📋 Lista de Conversas**

```
┌─────────────────────────────────┐
│  💬 Conversas Ativas            │
├─────────────────────────────────┤
│  🟢 João Silva                  │
│  📱 +55 11 99999-9999          │
│  💬 "Quero saber sobre..."      │
│  ⏰ 14:30                       │
│  🔴 3 não lidas                 │
├─────────────────────────────────┤
│  🟡 Maria Santos                │
│  📱 +55 11 88888-8888          │
│  💬 "Obrigada pelo atend..."     │
│  ⏰ 13:45                       │
├─────────────────────────────────┤
│  ⚪ Pedro Costa                 │
│  📱 +55 11 77777-7777          │
│  💬 "Aguardando resposta"       │
│  ⏰ 12:20                       │
└─────────────────────────────────┘
```

#### **💬 Área de Chat**

```
┌─────────────────────────────────────────────────────────────────┐
│  👤 João Silva (+55 11 99999-9999)                    🟢 Ativo  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  João Silva                                            14:25    │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ Olá, gostaria de informações sobre notebooks           │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
│                                                    Maria 14:26  │
│   ┌─────────────────────────────────────────────────────────┐  │
│   │ Olá João! Temos várias opções. Qual sua faixa de preço?│  │
│   └─────────────────────────────────────────────────────────┘  │
│                                                                 │
│  João Silva                                            14:27    │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ Entre R$ 2.000 e R$ 3.000                              │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│  💬 Digite sua mensagem...                          📎 📷 ➤    │
└─────────────────────────────────────────────────────────────────┘
```

### **5.3 Ferramentas do Atendente**

#### **🛠️ Barra de Ferramentas**

```
┌─────────────────────────────────────────────────────────────────┐
│  📝 Templates  📁 Arquivos  👥 Transferir  ✅ Resolver  📊 Info │
└─────────────────────────────────────────────────────────────────┘
```

#### **📝 Templates de Resposta**

```
┌─────────────────────────────────┐
│  📝 Templates Rápidos           │
├─────────────────────────────────┤
│  👋 Saudação                    │
│  "Olá! Como posso ajudá-lo?"    │
├─────────────────────────────────┤
│  ⏳ Aguarde                     │
│  "Um momento, vou verificar..."  │
├─────────────────────────────────┤
│  ✅ Finalização                 │
│  "Obrigado pelo contato!"       │
├─────────────────────────────────┤
│  ➕ Criar Novo Template         │
└─────────────────────────────────┘
```

#### **👤 Informações do Cliente**

```
┌─────────────────────────────────┐
│  👤 Dados do Cliente            │
├─────────────────────────────────┤
│  📛 Nome: João Silva            │
│  📱 Telefone: +55 11 99999-9999 │
│  📅 Primeiro contato: 15/01/24  │
│  💬 Total conversas: 3          │
│  ⭐ Satisfação: 4.5/5           │
├─────────────────────────────────┤
│  📋 Histórico Recente           │
│  • 15/01 - Dúvida sobre preços │
│  • 10/01 - Suporte técnico     │
│  • 05/01 - Primeira compra     │
└─────────────────────────────────┘
```

---

## **6. CONFIGURAÇÃO E INSTALAÇÃO** {#configuracao}

### **6.1 Pré-requisitos**

#### **🖥️ Requisitos do Sistema**

- **Sistema Operacional**: Linux (Ubuntu 20.04+ recomendado)
- **Docker**: Versão 20.10+
- **Docker Compose**: Versão 1.29+
- **Memória RAM**: Mínimo 4GB, recomendado 8GB
- **Armazenamento**: Mínimo 20GB livres
- **Rede**: Conexão estável com internet

#### **🌐 Requisitos de Rede**

- **Portas necessárias**:
  - 3000: Frontend (Next.js)
  - 8001: Backend (FastAPI)
  - 8080: Evolution API
  - 5432: PostgreSQL
- **Firewall**: Liberar portas para acesso externo se necessário
- **SSL**: Certificado válido para produção

### **6.2 Instalação Rápida**

#### **⚡ Configuração Automática**

```bash
# 1. Clonar repositório
git clone https://github.com/amvox/omnichannel.git
cd omnichannel

# 2. Executar configuração automática
chmod +x configure_evolution_cloud.sh
./configure_evolution_cloud.sh

# 3. Aguardar inicialização
docker-compose ps
```

#### **🔧 Configuração Manual**

```bash
# 1. Configurar Evolution API na nuvem
EVOLUTION_API_URL="https://sua-evolution-api.com"
EVOLUTION_API_KEY="sua_api_key_aqui"

# 2. Configurar webhook
WEBHOOK_URL="https://seu-dominio.com/api/v1/whatsapp/webhook"

# 3. Atualizar .env
cd back
cp .env.example .env
echo "EVOLUTION_API_URL=$EVOLUTION_API_URL" >> .env
echo "EVOLUTION_API_KEY=$EVOLUTION_API_KEY" >> .env
echo "EVOLUTION_WEBHOOK_URL=$WEBHOOK_URL" >> .env

# 4. Executar migração do banco
docker cp migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -f /tmp/create_whatsapp_tables.sql

# 5. Iniciar serviços
docker-compose up -d
```

### **6.3 Configuração da Evolution API**

#### **🔑 Obter Credenciais**

1. **Contratar serviço Evolution API** ou instalar próprio servidor
2. **Obter URL base**: `https://api.exemplo.com`
3. **Obter API Key**: Chave de autenticação
4. **Configurar webhook**: URL para receber mensagens

#### **🔗 Configurar Webhook**

```bash
# Configurar webhook para instância existente
curl -X POST "$EVOLUTION_API_URL/webhook/set/NOME_INSTANCIA" \
  -H "apikey: $EVOLUTION_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "'$WEBHOOK_URL'",
    "events": [
      "messages.upsert",
      "connection.update",
      "messages.update"
    ]
  }'
```

### **6.4 Configuração de Produção**

#### **🔒 HTTPS e SSL**

```nginx
# Configuração Nginx
server {
    listen 443 ssl;
    server_name seu-dominio.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location /api/v1/whatsapp/ {
        proxy_pass http://localhost:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### **📊 Monitoramento**

```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
```

---

## **7. AUTOMAÇÕES E IA** {#automacoes}

### **7.1 Sistema de Auto-Resposta**

#### **🤖 Regras Inteligentes**

```python
# Configuração de auto-respostas
auto_reply_rules = {
    "horario_comercial": {
        "trigger": "first_message AND business_hours",
        "response": "Olá! Como posso ajudá-lo hoje?",
        "priority": 1
    },
    "fora_horario": {
        "trigger": "first_message AND NOT business_hours",
        "response": "Olá! Estamos fora do horário de atendimento (08:00-18:00). Retornaremos em breve!",
        "priority": 1
    },
    "palavras_chave_preco": {
        "trigger": "message CONTAINS ['preço', 'valor', 'custo', 'quanto']",
        "response": "Para informações sobre preços, um consultor especializado irá atendê-lo.",
        "priority": 2
    },
    "palavras_chave_suporte": {
        "trigger": "message CONTAINS ['problema', 'erro', 'bug', 'não funciona']",
        "response": "Entendo que você está com dificuldades. Vou conectá-lo com nosso suporte técnico.",
        "priority": 2
    }
}
```

#### **⏰ Horários de Atendimento**

```python
# Configuração de horários
business_hours = {
    "monday": {"start": "08:00", "end": "18:00"},
    "tuesday": {"start": "08:00", "end": "18:00"},
    "wednesday": {"start": "08:00", "end": "18:00"},
    "thursday": {"start": "08:00", "end": "18:00"},
    "friday": {"start": "08:00", "end": "18:00"},
    "saturday": {"start": "09:00", "end": "14:00"},
    "sunday": {"closed": True}
}
```

### **7.2 Roteamento Inteligente**

#### **🎯 Distribuição de Conversas**

```python
class IntelligentRouting:
    def assign_conversation(self, conversation, available_agents):
        # 1. Verificar especialização
        specialized_agents = self.filter_by_specialization(
            available_agents, conversation.category
        )

        # 2. Verificar carga de trabalho
        balanced_agents = self.filter_by_workload(specialized_agents)

        # 3. Verificar histórico com cliente
        preferred_agent = self.check_customer_history(
            conversation.customer_number, balanced_agents
        )

        # 4. Aplicar algoritmo de distribuição
        if preferred_agent:
            return preferred_agent
        else:
            return self.round_robin_assignment(balanced_agents)
```

#### **📊 Critérios de Roteamento**

- **Especialização**: Vendas, suporte, financeiro
- **Carga de trabalho**: Número de conversas ativas
- **Histórico**: Atendente que já atendeu o cliente
- **Performance**: Tempo médio de resposta
- **Disponibilidade**: Status online/offline

### **7.3 Templates Inteligentes**

#### **📝 Templates Dinâmicos**

```python
# Templates com variáveis
templates = {
    "saudacao_personalizada": {
        "content": "Olá {customer_name}! Sou {agent_name} da Amvox. Como posso ajudá-lo hoje?",
        "variables": ["customer_name", "agent_name"]
    },
    "informacao_produto": {
        "content": "O produto {product_name} está disponível por R$ {price}. Gostaria de mais informações?",
        "variables": ["product_name", "price"]
    },
    "agendamento": {
        "content": "Perfeito! Agendei para {date} às {time}. Confirma esse horário?",
        "variables": ["date", "time"]
    }
}
```

#### **🔄 Sugestões Contextuais**

```python
class ContextualSuggestions:
    def suggest_responses(self, conversation_context):
        # Analisar contexto da conversa
        keywords = self.extract_keywords(conversation_context)
        intent = self.detect_intent(conversation_context)

        # Sugerir respostas baseadas no contexto
        suggestions = []

        if intent == "price_inquiry":
            suggestions.append("Vou verificar os preços para você...")
            suggestions.append("Temos várias opções de pagamento...")

        elif intent == "technical_support":
            suggestions.append("Vou conectá-lo com nosso suporte técnico...")
            suggestions.append("Pode me descrever o problema?")

        return suggestions
```

---

## **8. MÉTRICAS E RELATÓRIOS** {#metricas}

### **8.1 KPIs Principais**

#### **📊 Métricas de Performance**

```python
class WhatsAppMetrics:
    def get_performance_metrics(self, date_range):
        return {
            # Tempo de resposta
            "avg_response_time": "2.5 minutos",
            "first_response_time": "1.2 minutos",
            "resolution_time": "15.3 minutos",

            # Volume
            "total_conversations": 1250,
            "new_conversations": 180,
            "resolved_conversations": 165,

            # Satisfação
            "customer_satisfaction": 4.7,  # 1-5
            "resolution_rate": 92.3,      # %
            "escalation_rate": 7.7,       # %

            # Produtividade
            "conversations_per_agent": 8.5,
            "messages_per_hour": 45,
            "active_time_percentage": 78.2
        }
```

#### **👥 Métricas por Atendente**

```python
agent_metrics = {
    "maria_silva": {
        "conversations_handled": 45,
        "avg_response_time": "1.8 min",
        "customer_satisfaction": 4.9,
        "resolution_rate": 95.6,
        "online_time": "7h 45m"
    },
    "joao_santos": {
        "conversations_handled": 38,
        "avg_response_time": "2.1 min",
        "customer_satisfaction": 4.6,
        "resolution_rate": 89.5,
        "online_time": "8h 12m"
    }
}
```

### **8.2 Dashboard de Analytics**

#### **📈 Gráficos em Tempo Real**

```typescript
interface AnalyticsDashboard {
  // Gráficos principais
  conversationsOverTime: ChartData;
  responseTimeDistribution: ChartData;
  satisfactionTrend: ChartData;
  agentPerformance: ChartData;

  // Métricas instantâneas
  realTimeMetrics: {
    activeConversations: number;
    avgResponseTime: string;
    onlineAgents: number;
    queueLength: number;
  };

  // Alertas
  alerts: Alert[];
}
```

#### **📊 Tipos de Relatórios**

1. **Relatório Diário**
   - Conversas iniciadas/finalizadas
   - Tempo médio de resposta
   - Satisfação do cliente
   - Performance por atendente

2. **Relatório Semanal**
   - Tendências de volume
   - Picos de atendimento
   - Análise de eficiência
   - Comparativo com semana anterior

3. **Relatório Mensal**
   - KPIs consolidados
   - Análise de crescimento
   - ROI do atendimento
   - Planejamento de recursos

4. **Relatório de Satisfação**
   - NPS (Net Promoter Score)
   - Feedback dos clientes
   - Pontos de melhoria
   - Ações corretivas

### **8.3 Alertas e Notificações**

#### **🚨 Sistema de Alertas**

```python
alert_rules = {
    "response_time_high": {
        "condition": "avg_response_time > 5 minutes",
        "action": "notify_supervisor",
        "message": "Tempo de resposta acima do limite"
    },
    "queue_length_high": {
        "condition": "waiting_conversations > 10",
        "action": "notify_all_agents",
        "message": "Fila de atendimento alta - {count} conversas aguardando"
    },
    "satisfaction_low": {
        "condition": "customer_satisfaction < 4.0",
        "action": "notify_manager",
        "message": "Satisfação do cliente abaixo do esperado"
    },
    "agent_offline": {
        "condition": "online_agents < minimum_required",
        "action": "escalate_to_manager",
        "message": "Poucos atendentes online - {count}/{required}"
    }
}
```

---

## **9. SEGURANÇA E COMPLIANCE** {#seguranca}

### **9.1 Proteção de Dados**

#### **🔒 Criptografia**

```python
# Configurações de segurança
security_config = {
    "encryption": {
        "algorithm": "AES-256-GCM",
        "key_rotation": "monthly",
        "data_at_rest": True,
        "data_in_transit": True
    },
    "authentication": {
        "method": "JWT + API Key",
        "session_timeout": "8 hours",
        "password_policy": "strong",
        "two_factor": True
    },
    "access_control": {
        "rbac": True,  # Role-Based Access Control
        "principle": "least_privilege",
        "audit_logs": True
    }
}
```

#### **🛡️ Medidas de Proteção**

- **Criptografia end-to-end**: Todas as mensagens são criptografadas
- **Autenticação multifator**: Login seguro para atendentes
- **Controle de acesso**: Permissões baseadas em função
- **Logs de auditoria**: Registro de todas as ações
- **Backup seguro**: Cópias criptografadas dos dados

### **9.2 Compliance LGPD**

#### **📋 Conformidade Legal**

```python
lgpd_compliance = {
    "data_collection": {
        "consent": "explicit",
        "purpose": "customer_service",
        "retention": "2 years",
        "minimization": True
    },
    "customer_rights": {
        "access": True,        # Direito de acesso
        "rectification": True, # Direito de retificação
        "erasure": True,       # Direito ao esquecimento
        "portability": True,   # Direito à portabilidade
        "objection": True      # Direito de oposição
    },
    "data_processing": {
        "lawful_basis": "legitimate_interest",
        "purpose_limitation": True,
        "data_minimization": True,
        "accuracy": True,
        "storage_limitation": True,
        "security": True,
        "accountability": True
    }
}
```

#### **🔐 Implementação LGPD**

1. **Consentimento**: Solicitação clara de autorização
2. **Transparência**: Informações sobre uso dos dados
3. **Controle**: Ferramentas para exercer direitos
4. **Segurança**: Proteção adequada dos dados
5. **Responsabilização**: Documentação de conformidade

### **9.3 Backup e Recuperação**

#### **💾 Estratégia de Backup**

```bash
# Backup automático diário
#!/bin/bash
BACKUP_DIR="/backups/whatsapp"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup do banco de dados
docker exec amvox_postgres pg_dump -U postgres amvox_omnichannel > \
  "$BACKUP_DIR/db_backup_$DATE.sql"

# Backup de arquivos de mídia
tar -czf "$BACKUP_DIR/media_backup_$DATE.tar.gz" back/media/whatsapp/

# Backup de configurações
cp back/.env "$BACKUP_DIR/config_backup_$DATE.env"

# Limpeza de backups antigos (manter 30 dias)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

#### **🔄 Plano de Recuperação**

```python
disaster_recovery_plan = {
    "rto": "4 hours",      # Recovery Time Objective
    "rpo": "1 hour",       # Recovery Point Objective
    "backup_frequency": "daily",
    "backup_retention": "90 days",
    "testing_frequency": "monthly",
    "procedures": [
        "1. Identificar tipo de falha",
        "2. Ativar ambiente de contingência",
        "3. Restaurar backup mais recente",
        "4. Validar integridade dos dados",
        "5. Redirecionar tráfego",
        "6. Notificar stakeholders"
    ]
}
```

---

## **10. GUIA DE IMPLEMENTAÇÃO** {#implementacao}

### **10.1 Roadmap de Implementação**

#### **🎯 Fase 1: Configuração Básica (Semana 1)**

```
✅ Tarefas Concluídas:
- [x] Estrutura básica do backend
- [x] Interface de chat funcional
- [x] Integração com Evolution API
- [x] Proxy configurado no Next.js

🔄 Próximas Tarefas:
- [ ] Configurar Evolution API na nuvem
- [ ] Executar migração do banco de dados
- [ ] Testar integração completa
- [ ] Configurar webhook
```

#### **🎯 Fase 2: Persistência e CRUD (Semana 2)**

```
📋 Tarefas:
- [ ] Integrar CRUD com banco de dados
- [ ] Implementar webhook handler persistente
- [ ] Atualizar endpoints da API
- [ ] Criar testes de persistência
- [ ] Implementar backup automático
```

#### **🎯 Fase 3: Tempo Real (Semana 3)**

```
📋 Tarefas:
- [ ] Implementar WebSocket no backend
- [ ] Integrar WebSocket no frontend
- [ ] Criar sistema de notificações
- [ ] Implementar indicador de "digitando"
- [ ] Testes de conectividade em tempo real
```

#### **🎯 Fase 4: Funcionalidades Avançadas (Semana 4-5)**

```
📋 Tarefas:
- [ ] Suporte a mídia (imagens, áudios)
- [ ] Sistema de respostas automáticas
- [ ] Templates de mensagem
- [ ] Roteamento inteligente de conversas
- [ ] Interface de administração
```

#### **🎯 Fase 5: Analytics e Produção (Semana 6)**

```
📋 Tarefas:
- [ ] Dashboard de métricas
- [ ] Relatórios automatizados
- [ ] Sistema de alertas
- [ ] Configuração HTTPS
- [ ] Monitoramento e logs
```

### **10.2 Checklist de Implementação**

#### **✅ Pré-Implementação**

- [ ] Servidor configurado com Docker
- [ ] Domínio e SSL configurados
- [ ] Evolution API contratada/configurada
- [ ] Credenciais de acesso obtidas
- [ ] Equipe treinada nos procedimentos

#### **✅ Implementação Técnica**

- [ ] Código atualizado do repositório
- [ ] Variáveis de ambiente configuradas
- [ ] Banco de dados migrado
- [ ] Containers em execução
- [ ] APIs respondendo corretamente

#### **✅ Testes e Validação**

- [ ] Teste de criação de instância
- [ ] Teste de QR Code
- [ ] Teste de envio/recebimento de mensagens
- [ ] Teste de webhook
- [ ] Teste de interface do usuário

#### **✅ Produção**

- [ ] Monitoramento ativo
- [ ] Backup configurado
- [ ] Logs estruturados
- [ ] Alertas configurados
- [ ] Documentação atualizada

### **10.3 Comandos de Implementação**

#### **🚀 Configuração Rápida**

```bash
# 1. Configuração automática
./configure_evolution_cloud.sh

# 2. Verificar status
docker-compose ps
curl -s "http://localhost:3000/api/v1/whatsapp/stats"

# 3. Acessar interface
open http://localhost:3000/atendimento
```

#### **🔧 Configuração Manual**

```bash
# 1. Configurar Evolution API
export EVOLUTION_API_URL="https://sua-api.com"
export EVOLUTION_API_KEY="sua_chave"
export WEBHOOK_URL="https://seu-dominio.com/api/v1/whatsapp/webhook"

# 2. Atualizar backend
cd back
echo "EVOLUTION_API_URL=$EVOLUTION_API_URL" >> .env
echo "EVOLUTION_API_KEY=$EVOLUTION_API_KEY" >> .env
echo "EVOLUTION_WEBHOOK_URL=$WEBHOOK_URL" >> .env

# 3. Migrar banco
docker cp migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -f /tmp/create_whatsapp_tables.sql

# 4. Reiniciar serviços
docker-compose restart backend

# 5. Configurar webhook
curl -X POST "$EVOLUTION_API_URL/webhook/set/INSTANCIA" \
  -H "apikey: $EVOLUTION_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"url": "'$WEBHOOK_URL'", "events": ["messages.upsert"]}'
```

### **10.4 Suporte e Manutenção**

#### **📞 Canais de Suporte**

- **Documentação**: Arquivos MD no repositório
- **Logs**: `docker-compose logs -f`
- **Monitoramento**: Dashboard de métricas
- **Backup**: Verificação diária automática

#### **🔧 Manutenção Preventiva**

```bash
# Script de manutenção semanal
#!/bin/bash

# 1. Verificar saúde dos containers
docker-compose ps

# 2. Verificar logs de erro
docker-compose logs --since=7d | grep -i error

# 3. Verificar espaço em disco
df -h

# 4. Verificar backup
ls -la /backups/whatsapp/ | tail -7

# 5. Testar APIs principais
curl -s "http://localhost:3000/api/v1/whatsapp/stats"
curl -s "$EVOLUTION_API_URL/manager/instance/fetchInstances" \
  -H "apikey: $EVOLUTION_API_KEY"

# 6. Relatório de status
echo "✅ Manutenção concluída em $(date)"
```

---

## **CONCLUSÃO**

O sistema de atendimento WhatsApp da Amvox Omnichannel representa uma solução completa e moderna para atendimento ao cliente, combinando:

- **🎯 Eficiência**: Automações que reduzem tempo de resposta
- **📊 Inteligência**: Analytics para otimização contínua
- **🔒 Segurança**: Proteção de dados e compliance LGPD
- **⚡ Escalabilidade**: Arquitetura preparada para crescimento
- **😊 Experiência**: Interface intuitiva para atendentes e clientes

### **Próximos Passos Recomendados:**

1. **Executar configuração**: `./configure_evolution_cloud.sh`
2. **Testar funcionalidades**: Interface de atendimento
3. **Treinar equipe**: Uso da plataforma
4. **Monitorar métricas**: Dashboard de analytics
5. **Otimizar processos**: Baseado nos dados coletados

**Status do Projeto**: ✅ MVP Funcional | 🔄 Pronto para Produção

---

**Documentação gerada em**: $(date)
**Versão**: 1.0
**Amvox Omnichannel** - WhatsApp Integration