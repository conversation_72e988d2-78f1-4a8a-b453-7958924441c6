# Docker Compose para Produção - Amvox Omnichannel
version: '3.8'

services:
  backend:
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - NODE_ENV=production
      - CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com
      - MICROSOFT_REDIRECT_URI=https://api.yourdomain.com/api/v1/outlook/callback
      - MONITORING_ENABLED=true
      - METRICS_ENABLED=true
      - HEALTH_CHECK_ENABLED=true
    env_file:
      - ./back/.env.production
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first

  frontend:
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        order: start-first

  postgres:
    environment:
      - POSTGRES_DB=amvox_prod_db
      - POSTGRES_USER=amvox_prod_user
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
    secrets:
      - postgres_password
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  redis:
    volumes:
      - redis_prod_data:/data
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  nginx:
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local

secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt

networks:
  default:
    name: amvox_prod_network
    driver: bridge
