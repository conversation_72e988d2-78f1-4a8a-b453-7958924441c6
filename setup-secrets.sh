#!/bin/bash

# Script para configurar secrets de produção
# Uso: ./setup-secrets.sh [environment]
# Environments: staging, production

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Gerar senha segura
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Gerar chave de criptografia
generate_key() {
    openssl rand -hex 32
}

# Configurar secrets para ambiente
setup_secrets() {
    local env=${1:-production}
    
    log "Configurando secrets para ambiente: $env"
    
    # Criar diretório de secrets
    mkdir -p secrets
    
    case $env in
        staging)
            setup_staging_secrets
            ;;
        production)
            setup_production_secrets
            ;;
        *)
            error "Ambiente inválido: $env. Use: staging ou production"
            ;;
    esac
}

# Configurar secrets de staging
setup_staging_secrets() {
    log "Configurando secrets de staging..."
    
    # Senha do PostgreSQL
    if [[ ! -f "secrets/postgres_staging_password.txt" ]]; then
        generate_password > secrets/postgres_staging_password.txt
        success "Senha do PostgreSQL de staging gerada"
    else
        warning "Senha do PostgreSQL de staging já existe"
    fi
    
    # Criar arquivo .env.staging se não existir
    if [[ ! -f "back/.env.staging" ]]; then
        log "Criando arquivo .env.staging..."
        cp back/.env.production back/.env.staging
        
        # Substituir valores específicos de staging
        sed -i 's/yourdomain\.com/staging.yourdomain.com/g' back/.env.staging
        sed -i 's/amvox_prod_/amvox_staging_/g' back/.env.staging
        sed -i 's/LOG_LEVEL=INFO/LOG_LEVEL=DEBUG/g' back/.env.staging
        sed -i 's/DEBUG=false/DEBUG=true/g' back/.env.staging
        
        success "Arquivo .env.staging criado"
    fi
    
    success "Secrets de staging configurados"
}

# Configurar secrets de produção
setup_production_secrets() {
    log "Configurando secrets de produção..."
    
    # Senha do PostgreSQL
    if [[ ! -f "secrets/postgres_password.txt" ]]; then
        generate_password > secrets/postgres_password.txt
        success "Senha do PostgreSQL de produção gerada"
    else
        warning "Senha do PostgreSQL de produção já existe"
    fi
    
    # Verificar se .env.production existe
    if [[ ! -f "back/.env.production" ]]; then
        error "Arquivo back/.env.production não encontrado. Execute o script de configuração primeiro."
    fi
    
    # Verificar se as variáveis críticas foram alteradas
    check_critical_variables
    
    success "Secrets de produção configurados"
}

# Verificar variáveis críticas
check_critical_variables() {
    log "Verificando variáveis críticas..."
    
    local env_file="back/.env.production"
    local critical_vars=(
        "POSTGRES_PASSWORD=CHANGE_THIS_PASSWORD"
        "JWT_SECRET_KEY=CHANGE_THIS_TO_A_VERY_SECURE_RANDOM_STRING"
        "ENCRYPTION_KEY=CHANGE_THIS_TO_ANOTHER_SECURE_RANDOM_STRING"
        "CORS_ORIGINS=https://yourdomain.com"
        "NEXT_PUBLIC_API_URL=https://api.yourdomain.com"
    )
    
    local needs_config=false
    
    for var in "${critical_vars[@]}"; do
        if grep -q "$var" "$env_file"; then
            warning "Variável crítica não configurada: $var"
            needs_config=true
        fi
    done
    
    if [[ "$needs_config" == "true" ]]; then
        error "Configure as variáveis críticas em $env_file antes de continuar"
    fi
    
    success "Todas as variáveis críticas foram configuradas"
}

# Gerar certificados SSL auto-assinados (para desenvolvimento)
generate_ssl_certs() {
    log "Gerando certificados SSL auto-assinados..."
    
    mkdir -p nginx/ssl
    
    if [[ ! -f "nginx/ssl/cert.pem" ]] || [[ ! -f "nginx/ssl/key.pem" ]]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=BR/ST=State/L=City/O=Organization/OU=OrgUnit/CN=localhost"
        
        success "Certificados SSL gerados"
        warning "ATENÇÃO: Estes são certificados auto-assinados. Use certificados válidos em produção!"
    else
        warning "Certificados SSL já existem"
    fi
}

# Configurar permissões
set_permissions() {
    log "Configurando permissões..."
    
    # Permissões dos secrets
    chmod 600 secrets/*.txt 2>/dev/null || true
    chmod 700 secrets 2>/dev/null || true
    
    # Permissões dos certificados SSL
    chmod 600 nginx/ssl/*.pem 2>/dev/null || true
    chmod 700 nginx/ssl 2>/dev/null || true
    
    # Permissões dos scripts
    chmod +x *.sh 2>/dev/null || true
    
    success "Permissões configuradas"
}

# Mostrar resumo
show_summary() {
    local env=${1:-production}
    
    log "Resumo da configuração:"
    echo ""
    echo "Ambiente: $env"
    echo "Secrets criados:"
    ls -la secrets/ 2>/dev/null || echo "  Nenhum secret encontrado"
    echo ""
    echo "Certificados SSL:"
    ls -la nginx/ssl/ 2>/dev/null || echo "  Nenhum certificado encontrado"
    echo ""
    
    if [[ "$env" == "production" ]]; then
        warning "IMPORTANTE para produção:"
        echo "1. Configure todas as variáveis em back/.env.production"
        echo "2. Substitua os certificados SSL auto-assinados por certificados válidos"
        echo "3. Configure DNS para apontar para o servidor"
        echo "4. Configure firewall e segurança do servidor"
        echo "5. Configure backup automático"
    fi
}

# Função principal
main() {
    local command=${1:-setup}
    local environment=${2:-production}
    
    log "Configurando secrets - Amvox Omnichannel"
    
    case $command in
        setup)
            setup_secrets "$environment"
            set_permissions
            show_summary "$environment"
            ;;
        ssl)
            generate_ssl_certs
            set_permissions
            ;;
        check)
            check_critical_variables
            ;;
        permissions)
            set_permissions
            ;;
        *)
            echo "Uso: $0 [comando] [ambiente]"
            echo ""
            echo "Comandos:"
            echo "  setup      - Configurar secrets (padrão)"
            echo "  ssl        - Gerar certificados SSL auto-assinados"
            echo "  check      - Verificar variáveis críticas"
            echo "  permissions - Configurar permissões"
            echo ""
            echo "Ambientes: staging, production"
            echo ""
            echo "Exemplos:"
            echo "  $0 setup production"
            echo "  $0 ssl"
            echo "  $0 check"
            ;;
    esac
}

# Executar função principal
main "$@"
