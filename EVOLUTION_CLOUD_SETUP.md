# 🌐 **Configuração Evolution API na Nuvem**

## 🎯 **Cenário Atual**
- ✅ Backend Amvox funcionando
- ✅ API WhatsApp respondendo
- 🔧 Precisa configurar Evolution API na nuvem

---

## **Passo 1: Obter Informações da Evolution API**

### **1.1 - Identificar URL e Credenciais**

Você precisa das seguintes informações da sua Evolution API na nuvem:

```bash
# Informações necessárias:
EVOLUTION_API_URL=https://sua-evolution-api.com.br
EVOLUTION_API_KEY=sua_api_key_aqui
```

### **1.2 - Testar Conectividade**

```bash
# Testar se a Evolution API está acessível
curl -X GET "https://sua-evolution-api.com.br/manager/instance/fetchInstances" \
  -H "apikey: sua_api_key_aqui"

# Resposta esperada: [] ou lista de instâncias
```

---

## **Passo 2: Configurar Backend**

### **2.1 - Atual<PERSON>r <PERSON>á<PERSON> Ambiente**

```bash
# Editar arquivo .env do backend
cd back
nano .env

# Ou usar sed para substituir
sed -i 's|EVOLUTION_API_URL=.*|EVOLUTION_API_URL=https://sua-evolution-api.com.br|' .env
sed -i 's|EVOLUTION_API_KEY=.*|EVOLUTION_API_KEY=sua_api_key_aqui|' .env
```

### **2.2 - Configurar Webhook URL**

A Evolution API precisa saber onde enviar os webhooks. Configure com sua URL pública:

```bash
# Se você tem domínio público
EVOLUTION_WEBHOOK_URL=https://seu-dominio.com.br/api/v1/whatsapp/webhook

# Se está testando localmente, use ngrok ou similar
# Instalar ngrok
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
sudo apt update && sudo apt install ngrok

# Expor porta local
ngrok http 8001

# Usar URL do ngrok como webhook
EVOLUTION_WEBHOOK_URL=https://abc123.ngrok.io/api/v1/whatsapp/webhook
```

---

## **Passo 3: Script de Configuração Automática**

Vou criar um script para configurar automaticamente:
