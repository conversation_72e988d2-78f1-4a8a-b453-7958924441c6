#!/bin/bash

# 🚀 Script de Configuração Automática do WhatsApp
# Amvox Omnichannel - WhatsApp Integration Setup

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                    AMVOX OMNICHANNEL                         ║
║                WhatsApp Integration Setup                    ║
║                                                              ║
║  Este script irá configurar automaticamente:                ║
║  • Evolution API                                             ║
║  • Banco de Dados                                            ║
║  • Configurações do Backend                                  ║
║  • Testes de Integração                                      ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

# Verificar pré-requisitos
check_prerequisites() {
    log "Verificando pré-requisitos..."
    
    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado. Instale o Docker primeiro."
    fi
    
    # Verificar Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose não está instalado."
    fi
    
    # Verificar curl
    if ! command -v curl &> /dev/null; then
        error "curl não está instalado."
    fi
    
    # Verificar jq
    if ! command -v jq &> /dev/null; then
        warn "jq não está instalado. Instalando..."
        sudo apt-get update && sudo apt-get install -y jq
    fi
    
    log "✅ Pré-requisitos verificados"
}

# Configurar Evolution API
setup_evolution_api() {
    log "Configurando Evolution API..."
    
    # Criar diretório
    EVOLUTION_DIR="$HOME/evolution-api"
    mkdir -p "$EVOLUTION_DIR"
    cd "$EVOLUTION_DIR"
    
    # Criar docker-compose.yml
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  evolution-api:
    image: atendai/evolution-api:latest
    container_name: evolution_api
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SERVER_URL=http://localhost:8080
      - CORS_ORIGIN=*
      - CORS_METHODS=GET,POST,PUT,DELETE
      - CORS_CREDENTIALS=true
      - AUTHENTICATION_TYPE=apikey
      - AUTHENTICATION_API_KEY=B6D711FCDE4D4FD5936544120E713976
      - AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=true
      - WEBHOOK_GLOBAL_URL=http://host.docker.internal:8001/api/v1/whatsapp/webhook
      - WEBHOOK_GLOBAL_ENABLED=true
      - QRCODE_LIMIT=30
      - QRCODE_COLOR=#198754
      - INSTANCE_EXPIRE_TIME=false
      - DEL_INSTANCE=false
    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store
    networks:
      - evolution_network

volumes:
  evolution_instances:
  evolution_store:

networks:
  evolution_network:
    driver: bridge
EOF
    
    # Iniciar Evolution API
    log "Iniciando Evolution API..."
    docker-compose up -d
    
    # Aguardar inicialização
    log "Aguardando Evolution API inicializar..."
    sleep 30
    
    # Verificar se está funcionando
    if curl -s -f "http://localhost:8080/manager/instance/fetchInstances" \
       -H "apikey: B6D711FCDE4D4FD5936544120E713976" > /dev/null; then
        log "✅ Evolution API configurada com sucesso"
    else
        error "❌ Falha ao configurar Evolution API"
    fi
    
    cd - > /dev/null
}

# Configurar banco de dados
setup_database() {
    log "Configurando banco de dados..."
    
    # Verificar se o arquivo de migração existe
    if [ ! -f "back/migrations/create_whatsapp_tables.sql" ]; then
        error "Arquivo de migração não encontrado: back/migrations/create_whatsapp_tables.sql"
    fi
    
    # Copiar arquivo para container
    docker cp back/migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/
    
    # Executar migração
    log "Executando migração do banco de dados..."
    docker exec -i amvox_postgres psql -U postgres -d amvox_omnichannel -f /tmp/create_whatsapp_tables.sql
    
    # Verificar tabelas criadas
    TABLES=$(docker exec -i amvox_postgres psql -U postgres -d amvox_omnichannel -t -c "\dt whatsapp*" | wc -l)
    if [ "$TABLES" -gt 0 ]; then
        log "✅ Banco de dados configurado com sucesso"
    else
        error "❌ Falha ao criar tabelas do WhatsApp"
    fi
}

# Configurar backend
setup_backend() {
    log "Configurando backend..."
    
    # Verificar se .env existe
    if [ ! -f "back/.env" ]; then
        log "Criando arquivo .env..."
        cp back/.env.example back/.env
    fi
    
    # Adicionar configurações da Evolution API se não existirem
    if ! grep -q "EVOLUTION_API_URL" back/.env; then
        log "Adicionando configurações da Evolution API..."
        cat >> back/.env << 'EOF'

# Configurações da Evolution API (WhatsApp)
EVOLUTION_API_URL=http://localhost:8080
EVOLUTION_API_KEY=B6D711FCDE4D4FD5936544120E713976
EVOLUTION_WEBHOOK_URL=http://localhost:8001/api/v1/whatsapp/webhook
EOF
    fi
    
    log "✅ Backend configurado"
}

# Reconstruir containers
rebuild_containers() {
    log "Reconstruindo containers..."
    
    # Parar containers
    docker-compose down
    
    # Reconstruir backend
    docker-compose build backend
    
    # Iniciar todos os serviços
    docker-compose up -d
    
    # Aguardar inicialização
    log "Aguardando containers inicializarem..."
    sleep 45
    
    log "✅ Containers reconstruídos"
}

# Testar integração
test_integration() {
    log "Testando integração..."
    
    # Testar API de estatísticas
    if curl -s -f "http://localhost:3000/api/v1/whatsapp/stats" > /dev/null; then
        log "✅ API de estatísticas funcionando"
    else
        warn "⚠️  API de estatísticas não respondeu (pode ser normal se o backend ainda está inicializando)"
    fi
    
    # Testar criação de instância
    log "Testando criação de instância..."
    RESPONSE=$(curl -s -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
        -H "Content-Type: application/json" \
        -d '{
            "instance_name": "amvox_test_setup",
            "webhook_url": "http://localhost:8001/api/v1/whatsapp/webhook"
        }' || echo "ERROR")
    
    if [[ "$RESPONSE" != "ERROR" ]] && [[ "$RESPONSE" == *"success"* ]]; then
        log "✅ Criação de instância funcionando"
    else
        warn "⚠️  Criação de instância falhou (verifique logs do backend)"
    fi
    
    log "✅ Testes de integração concluídos"
}

# Mostrar status final
show_status() {
    echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    CONFIGURAÇÃO CONCLUÍDA                   ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo -e "\n${BLUE}📋 SERVIÇOS CONFIGURADOS:${NC}"
    echo -e "   • Evolution API: ${GREEN}http://localhost:8080${NC}"
    echo -e "   • Frontend: ${GREEN}http://localhost:3000${NC}"
    echo -e "   • Backend API: ${GREEN}http://localhost:8001${NC}"
    echo -e "   • WhatsApp Interface: ${GREEN}http://localhost:3000/atendimento${NC}"
    
    echo -e "\n${BLUE}🔑 CREDENCIAIS:${NC}"
    echo -e "   • Evolution API Key: ${YELLOW}B6D711FCDE4D4FD5936544120E713976${NC}"
    
    echo -e "\n${BLUE}📖 PRÓXIMOS PASSOS:${NC}"
    echo -e "   1. Acesse: ${GREEN}http://localhost:3000/atendimento${NC}"
    echo -e "   2. Clique na aba ${YELLOW}'WhatsApp'${NC}"
    echo -e "   3. Clique em ${YELLOW}'Conectar WhatsApp'${NC}"
    echo -e "   4. Escaneie o QR Code com seu WhatsApp Business"
    
    echo -e "\n${BLUE}🔧 COMANDOS ÚTEIS:${NC}"
    echo -e "   • Ver logs: ${YELLOW}docker-compose logs -f${NC}"
    echo -e "   • Reiniciar: ${YELLOW}docker-compose restart${NC}"
    echo -e "   • Parar tudo: ${YELLOW}docker-compose down${NC}"
    
    echo -e "\n${BLUE}📚 DOCUMENTAÇÃO:${NC}"
    echo -e "   • Guia completo: ${YELLOW}WHATSAPP_IMPLEMENTATION_GUIDE.md${NC}"
    echo -e "   • Evolution API: ${YELLOW}https://doc.evolution-api.com/${NC}"
}

# Menu principal
main() {
    echo -e "\n${BLUE}Escolha uma opção:${NC}"
    echo "1. Configuração completa (recomendado)"
    echo "2. Apenas Evolution API"
    echo "3. Apenas banco de dados"
    echo "4. Apenas backend"
    echo "5. Apenas testes"
    echo "6. Sair"
    
    read -p "Digite sua escolha (1-6): " choice
    
    case $choice in
        1)
            check_prerequisites
            setup_evolution_api
            setup_database
            setup_backend
            rebuild_containers
            test_integration
            show_status
            ;;
        2)
            check_prerequisites
            setup_evolution_api
            ;;
        3)
            setup_database
            ;;
        4)
            setup_backend
            rebuild_containers
            ;;
        5)
            test_integration
            ;;
        6)
            log "Saindo..."
            exit 0
            ;;
        *)
            error "Opção inválida"
            ;;
    esac
}

# Executar
main
