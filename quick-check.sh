#!/bin/bash

# Script de Verificação Rápida - Amvox Omnichannel
# Verifica se todos os serviços estão funcionando corretamente

# Cores
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 VERIFICAÇÃO RÁPIDA - AMVOX OMNICHANNEL${NC}"
echo "=================================================="

# Verificar containers
echo -e "\n${YELLOW}📦 STATUS DOS CONTAINERS:${NC}"
docker-compose ps

# Verificar endpoints
echo -e "\n${YELLOW}🌐 TESTANDO ENDPOINTS:${NC}"

# Backend Health
if curl -f http://localhost:8001/health >/dev/null 2>&1; then
    echo -e "✅ Backend Health: ${GREEN}OK${NC} - http://localhost:8001/health"
else
    echo -e "❌ Backend Health: ${RED}FALHOU${NC}"
fi

# Frontend
if curl -f http://localhost:3000 >/dev/null 2>&1; then
    echo -e "✅ Frontend: ${GREEN}OK${NC} - http://localhost:3000"
else
    echo -e "❌ Frontend: ${RED}FALHOU${NC}"
fi

# API Docs
if curl -f http://localhost:8001/docs >/dev/null 2>&1; then
    echo -e "✅ API Docs: ${GREEN}OK${NC} - http://localhost:8001/docs"
else
    echo -e "❌ API Docs: ${RED}FALHOU${NC}"
fi

# Verificar banco de dados
echo -e "\n${YELLOW}🗄️  TESTANDO BANCO DE DADOS:${NC}"
if docker-compose exec -T postgres pg_isready -U amvox_user >/dev/null 2>&1; then
    echo -e "✅ PostgreSQL: ${GREEN}OK${NC}"
else
    echo -e "❌ PostgreSQL: ${RED}FALHOU${NC}"
fi

# Verificar Redis
echo -e "\n${YELLOW}⚡ TESTANDO REDIS:${NC}"
if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
    echo -e "✅ Redis: ${GREEN}OK${NC}"
else
    echo -e "❌ Redis: ${RED}FALHOU${NC}"
fi

# Verificar APIs específicas
echo -e "\n${YELLOW}🔧 TESTANDO APIs ESPECÍFICAS:${NC}"

# Microsoft Graph API Status
echo -e "📧 Microsoft Graph API: Integração principal de email"

# Outlook Status
if curl -f http://localhost:8001/api/v1/outlook/config/status >/dev/null 2>&1; then
    echo -e "✅ Outlook API: ${GREEN}OK${NC}"
else
    echo -e "❌ Outlook API: ${RED}FALHOU${NC}"
fi

# Relatórios
if curl -f http://localhost:8001/api/v1/reports/distribution -X POST -H "Content-Type: application/json" -d '{"start_date": "2024-01-01", "end_date": "2024-12-31"}' >/dev/null 2>&1; then
    echo -e "✅ Relatórios API: ${GREEN}OK${NC}"
else
    echo -e "❌ Relatórios API: ${RED}FALHOU${NC}"
fi

echo -e "\n${BLUE}=================================================="
echo -e "🎯 LINKS RÁPIDOS:${NC}"
echo -e "🎨 Frontend: ${YELLOW}http://localhost:3000${NC}"
echo -e "🔧 Backend: ${YELLOW}http://localhost:8001${NC}"
echo -e "📚 API Docs: ${YELLOW}http://localhost:8001/docs${NC}"
echo -e "${BLUE}==================================================${NC}"
