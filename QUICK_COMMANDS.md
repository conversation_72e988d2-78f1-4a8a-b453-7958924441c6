# 🚀 **Comandos Rápidos - WhatsApp Integration**

## **⚡ Configuração Automática**

```bash
# Executar configuração completa
./setup_whatsapp.sh

# Ou executar passo a passo:
chmod +x setup_whatsapp.sh
./setup_whatsapp.sh
```

---

## **🔧 Comandos Manuais**

### **1. Evolution API**

```bash
# Criar e iniciar Evolution API
mkdir ~/evolution-api && cd ~/evolution-api

# Criar docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'
services:
  evolution-api:
    image: atendai/evolution-api:latest
    container_name: evolution_api
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SERVER_URL=http://localhost:8080
      - CORS_ORIGIN=*
      - AUTHENTICATION_TYPE=apikey
      - AUTHENTICATION_API_KEY=B6D711FCDE4D4FD5936544120E713976
      - WEBHOOK_GLOBAL_URL=http://host.docker.internal:8001/api/v1/whatsapp/webhook
      - WEBHOOK_GLOBAL_ENABLED=true
    volumes:
      - evolution_instances:/evolution/instances
volumes:
  evolution_instances:
EOF

# Iniciar
docker-compose up -d

# Testar
curl http://localhost:8080/manager/instance/fetchInstances \
  -H "apikey: B6D711FCDE4D4FD5936544120E713976"
```

### **2. Banco de Dados**

```bash
# Executar migração
docker cp back/migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -f /tmp/create_whatsapp_tables.sql

# Verificar tabelas
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel -c "\dt whatsapp*"
```

### **3. Backend**

```bash
# Configurar .env
cp back/.env.example back/.env

# Adicionar configurações WhatsApp
cat >> back/.env << 'EOF'
EVOLUTION_API_URL=http://localhost:8080
EVOLUTION_API_KEY=B6D711FCDE4D4FD5936544120E713976
EVOLUTION_WEBHOOK_URL=http://localhost:8001/api/v1/whatsapp/webhook
EOF

# Reconstruir containers
docker-compose down
docker-compose build backend
docker-compose up -d
```

---

## **🧪 Testes**

### **Testar APIs**

```bash
# 1. Estatísticas
curl -s "http://localhost:3000/api/v1/whatsapp/stats" | jq .

# 2. Criar instância
curl -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
  -H "Content-Type: application/json" \
  -d '{"instance_name": "test", "webhook_url": "http://localhost:8001/api/v1/whatsapp/webhook"}' | jq .

# 3. Obter QR Code
curl -s "http://localhost:3000/api/v1/whatsapp/instances/test/qr" | jq .

# 4. Status da instância
curl -s "http://localhost:3000/api/v1/whatsapp/instances/test/status" | jq .

# 5. Enviar mensagem
curl -X POST "http://localhost:3000/api/v1/whatsapp/messages/send" \
  -H "Content-Type: application/json" \
  -d '{
    "instance_name": "test",
    "number": "5511999999999",
    "text": "Mensagem de teste"
  }' | jq .

# 6. Listar conversas
curl -s "http://localhost:3000/api/v1/whatsapp/conversations" | jq .
```

### **Testar Evolution API Diretamente**

```bash
# API Key
API_KEY="B6D711FCDE4D4FD5936544120E713976"
BASE_URL="http://localhost:8080"

# Listar instâncias
curl -X GET "$BASE_URL/manager/instance/fetchInstances" \
  -H "apikey: $API_KEY"

# Criar instância
curl -X POST "$BASE_URL/instance/create" \
  -H "apikey: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"instanceName": "test_direct"}'

# Obter QR Code
curl -X GET "$BASE_URL/instance/connect/test_direct" \
  -H "apikey: $API_KEY"

# Status da instância
curl -X GET "$BASE_URL/instance/connectionState/test_direct" \
  -H "apikey: $API_KEY"
```

---

## **🔍 Diagnóstico**

### **Verificar Serviços**

```bash
# Status dos containers
docker-compose ps

# Logs em tempo real
docker-compose logs -f

# Logs específicos
docker-compose logs backend
docker-compose logs frontend
docker logs evolution_api

# Verificar portas
netstat -tulpn | grep -E "(3000|8001|8080|5432)"

# Verificar conectividade
curl -I http://localhost:3000
curl -I http://localhost:8001
curl -I http://localhost:8080
```

### **Verificar Banco de Dados**

```bash
# Conectar ao banco
docker exec -it amvox_postgres psql -U postgres -d amvox_omnichannel

# Verificar tabelas WhatsApp
\dt whatsapp*

# Verificar dados
SELECT * FROM whatsapp_instances;
SELECT * FROM whatsapp_conversations LIMIT 5;
SELECT * FROM whatsapp_messages ORDER BY created_at DESC LIMIT 10;

# Sair
\q
```

### **Verificar Arquivos**

```bash
# Verificar estrutura
ls -la back/app/api/v1/whatsapp.py
ls -la back/app/services/integrations/evolution/
ls -la back/migrations/create_whatsapp_tables.sql
ls -la front/src/components/atendimento/whatsapp/

# Verificar configurações
cat back/.env | grep EVOLUTION
cat front/next.config.js | grep whatsapp
```

---

## **🛠️ Manutenção**

### **Reiniciar Serviços**

```bash
# Reiniciar tudo
docker-compose restart

# Reiniciar específico
docker-compose restart backend
docker-compose restart frontend
docker restart evolution_api

# Reconstruir e reiniciar
docker-compose down
docker-compose build
docker-compose up -d
```

### **Limpar e Resetar**

```bash
# Parar tudo
docker-compose down

# Limpar volumes (CUIDADO: apaga dados)
docker-compose down -v

# Limpar imagens
docker system prune -f

# Reset completo (CUIDADO)
docker-compose down -v --rmi all
```

### **Backup**

```bash
# Backup do banco
docker exec amvox_postgres pg_dump -U postgres amvox_omnichannel > backup_whatsapp_$(date +%Y%m%d).sql

# Backup de mídia (quando implementado)
tar -czf backup_media_$(date +%Y%m%d).tar.gz back/media/whatsapp/

# Backup de configurações
cp back/.env backup_env_$(date +%Y%m%d).env
```

---

## **📱 Interface**

### **Acessar WhatsApp**

1. **URL:** http://localhost:3000/atendimento
2. **Clicar:** Aba "WhatsApp"
3. **Conectar:** Botão "Conectar WhatsApp"
4. **QR Code:** Escanear com WhatsApp Business
5. **Chat:** Interface de conversas

### **URLs Importantes**

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8001
- **Evolution API:** http://localhost:8080
- **WhatsApp Interface:** http://localhost:3000/atendimento
- **API Docs:** http://localhost:8001/docs

---

## **🆘 Problemas Comuns**

### **Evolution API não inicia**

```bash
# Verificar logs
docker logs evolution_api

# Verificar porta
sudo netstat -tulpn | grep 8080

# Reiniciar
cd ~/evolution-api
docker-compose restart
```

### **Webhook não funciona**

```bash
# Verificar URL do webhook
curl -X GET "http://localhost:8080/webhook/find/instanceName" \
  -H "apikey: B6D711FCDE4D4FD5936544120E713976"

# Reconfigurar webhook
curl -X POST "http://localhost:8080/webhook/set/instanceName" \
  -H "apikey: B6D711FCDE4D4FD5936544120E713976" \
  -H "Content-Type: application/json" \
  -d '{"url": "http://host.docker.internal:8001/api/v1/whatsapp/webhook"}'
```

### **API 404 Not Found**

```bash
# Verificar proxy do Next.js
cat front/next.config.js | grep -A 10 rewrites

# Verificar se backend está rodando
curl http://localhost:8001/api/v1/whatsapp/stats

# Reiniciar frontend
docker-compose restart frontend
```

---

## **📞 Suporte**

- **Documentação:** `WHATSAPP_IMPLEMENTATION_GUIDE.md`
- **Evolution API:** https://doc.evolution-api.com/
- **Logs:** `docker-compose logs -f`
