#!/bin/bash

# 📄 Script para Gerar PDF da Documentação WhatsApp
# Amvox Omnichannel - PDF Generator

set -e

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[WARNING] $1${NC}"; }
error() { echo -e "${RED}[ERROR] $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[INFO] $1${NC}"; }

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                    AMVOX OMNICHANNEL                         ║
║                  PDF Documentation Generator                ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

# Verificar dependências
check_dependencies() {
    log "Verificando dependências..."
    
    # Verificar pandoc
    if ! command -v pandoc &> /dev/null; then
        warn "Pandoc não encontrado. Instalando..."
        
        # Detectar sistema operacional
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            sudo apt-get update
            sudo apt-get install -y pandoc texlive-latex-base texlive-fonts-recommended texlive-latex-extra
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            if command -v brew &> /dev/null; then
                brew install pandoc basictex
            else
                error "Homebrew não encontrado. Instale o Homebrew primeiro."
            fi
        else
            error "Sistema operacional não suportado. Instale o pandoc manualmente."
        fi
    fi
    
    # Verificar wkhtmltopdf (alternativa)
    if ! command -v wkhtmltopdf &> /dev/null; then
        info "wkhtmltopdf não encontrado. Usando pandoc com LaTeX."
    fi
    
    log "✅ Dependências verificadas"
}

# Preparar documento
prepare_document() {
    log "Preparando documento..."
    
    # Criar diretório de saída
    mkdir -p output
    
    # Verificar se arquivo fonte existe
    if [[ ! -f "WHATSAPP_COMPLETE_DOCUMENTATION.md" ]]; then
        error "Arquivo WHATSAPP_COMPLETE_DOCUMENTATION.md não encontrado"
    fi
    
    # Criar cópia temporária com ajustes para PDF
    cp WHATSAPP_COMPLETE_DOCUMENTATION.md temp_doc.md
    
    # Ajustar formatação para PDF
    sed -i 's/```/\n```/g' temp_doc.md
    sed -i 's/{#[^}]*}//g' temp_doc.md  # Remover âncoras
    
    log "✅ Documento preparado"
}

# Gerar PDF com pandoc
generate_pdf_pandoc() {
    log "Gerando PDF com pandoc..."
    
    # Configurações do pandoc
    TITLE="Amvox Omnichannel - WhatsApp Integration"
    AUTHOR="Amvox Tecnologia"
    DATE=$(date +"%d/%m/%Y")
    
    # Gerar PDF
    pandoc temp_doc.md \
        --pdf-engine=pdflatex \
        --variable=geometry:margin=2cm \
        --variable=fontsize:11pt \
        --variable=documentclass:article \
        --variable=title:"$TITLE" \
        --variable=author:"$AUTHOR" \
        --variable=date:"$DATE" \
        --toc \
        --toc-depth=3 \
        --number-sections \
        --highlight-style=tango \
        --include-in-header=<(echo '\usepackage{fancyhdr}') \
        --include-in-header=<(echo '\pagestyle{fancy}') \
        --include-in-header=<(echo '\fancyhead[L]{Amvox Omnichannel}') \
        --include-in-header=<(echo '\fancyhead[R]{WhatsApp Integration}') \
        --include-in-header=<(echo '\usepackage{xcolor}') \
        --include-in-header=<(echo '\definecolor{amvoxblue}{RGB}{0,123,191}') \
        -o "output/Amvox_WhatsApp_Documentation.pdf" \
        2>/dev/null || {
            warn "Falha com configurações avançadas. Tentando versão simples..."
            
            pandoc temp_doc.md \
                --pdf-engine=pdflatex \
                --toc \
                --number-sections \
                -o "output/Amvox_WhatsApp_Documentation.pdf"
        }
    
    log "✅ PDF gerado com pandoc"
}

# Gerar PDF com wkhtmltopdf (alternativa)
generate_pdf_wkhtmltopdf() {
    log "Gerando PDF com wkhtmltopdf..."
    
    # Converter MD para HTML primeiro
    pandoc temp_doc.md -t html5 -s --toc --css=styles.css -o temp_doc.html
    
    # Criar CSS básico
    cat > styles.css << 'EOF'
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    margin-top: 30px;
}

h1 {
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
}

h2 {
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 5px;
}

code {
    background-color: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    overflow-x: auto;
}

blockquote {
    border-left: 4px solid #3498db;
    margin: 0;
    padding-left: 20px;
    color: #7f8c8d;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 20px 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}
EOF
    
    # Gerar PDF
    wkhtmltopdf \
        --page-size A4 \
        --margin-top 20mm \
        --margin-bottom 20mm \
        --margin-left 15mm \
        --margin-right 15mm \
        --header-center "Amvox Omnichannel - WhatsApp Integration" \
        --header-font-size 9 \
        --footer-center "Página [page] de [topage]" \
        --footer-font-size 9 \
        temp_doc.html \
        "output/Amvox_WhatsApp_Documentation_HTML.pdf"
    
    log "✅ PDF gerado com wkhtmltopdf"
}

# Limpar arquivos temporários
cleanup() {
    log "Limpando arquivos temporários..."
    
    rm -f temp_doc.md temp_doc.html styles.css
    
    log "✅ Limpeza concluída"
}

# Mostrar resultado
show_result() {
    echo -e "\n${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                    PDF GERADO COM SUCESSO                   ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    echo -e "\n${BLUE}📄 ARQUIVOS GERADOS:${NC}"
    
    if [[ -f "output/Amvox_WhatsApp_Documentation.pdf" ]]; then
        SIZE=$(du -h "output/Amvox_WhatsApp_Documentation.pdf" | cut -f1)
        echo -e "   • ${GREEN}Amvox_WhatsApp_Documentation.pdf${NC} (${SIZE})"
    fi
    
    if [[ -f "output/Amvox_WhatsApp_Documentation_HTML.pdf" ]]; then
        SIZE=$(du -h "output/Amvox_WhatsApp_Documentation_HTML.pdf" | cut -f1)
        echo -e "   • ${GREEN}Amvox_WhatsApp_Documentation_HTML.pdf${NC} (${SIZE})"
    fi
    
    echo -e "\n${BLUE}📂 LOCALIZAÇÃO:${NC}"
    echo -e "   • Diretório: ${YELLOW}$(pwd)/output/${NC}"
    
    echo -e "\n${BLUE}🔧 COMANDOS ÚTEIS:${NC}"
    echo -e "   • Abrir PDF: ${YELLOW}xdg-open output/Amvox_WhatsApp_Documentation.pdf${NC}"
    echo -e "   • Listar arquivos: ${YELLOW}ls -la output/${NC}"
    echo -e "   • Regenerar: ${YELLOW}./generate_pdf.sh${NC}"
}

# Menu principal
main() {
    echo -e "\n${BLUE}Escolha o método de geração:${NC}"
    echo "1. Pandoc + LaTeX (recomendado)"
    echo "2. wkhtmltopdf (alternativo)"
    echo "3. Ambos"
    echo "4. Apenas verificar dependências"
    echo "5. Sair"
    
    read -p "Digite sua escolha (1-5): " choice
    
    case $choice in
        1)
            check_dependencies
            prepare_document
            generate_pdf_pandoc
            cleanup
            show_result
            ;;
        2)
            check_dependencies
            prepare_document
            generate_pdf_wkhtmltopdf
            cleanup
            show_result
            ;;
        3)
            check_dependencies
            prepare_document
            generate_pdf_pandoc
            generate_pdf_wkhtmltopdf
            cleanup
            show_result
            ;;
        4)
            check_dependencies
            ;;
        5)
            log "Saindo..."
            exit 0
            ;;
        *)
            error "Opção inválida"
            ;;
    esac
}

# Executar
main
