#!/bin/bash

# 🧪 Script para testar POC WhatsApp Webhook

set -e

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[WARNING] $1${NC}"; }
error() { echo -e "${RED}[ERROR] $1${NC}"; exit 1; }
info() { echo -e "${BLUE}[INFO] $1${NC}"; }

# Banner
echo -e "${BLUE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                    TESTE POC WHATSAPP                       ║
║                  Webhook + PostgreSQL                       ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

# Verificar se backend está rodando
check_backend() {
    log "Verificando se backend está rodando..."
    
    if ! curl -s http://localhost:8001/health > /dev/null; then
        error "Backend não está rodando. Execute: docker-compose up -d backend"
    fi
    
    log "✅ Backend está rodando"
}

# Verificar banco de dados
check_database() {
    log "Verificando banco de dados..."
    
    # Verificar se tabelas WhatsApp existem
    TABLES=$(docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -t -c "\dt whatsapp*" | grep -c "whatsapp" || echo "0")
    
    if [ "$TABLES" -eq "0" ]; then
        warn "Tabelas WhatsApp não encontradas. Criando..."
        docker cp back/migrations/create_whatsapp_tables.sql amvox_postgres:/tmp/
        docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -f /tmp/create_whatsapp_tables.sql
        log "✅ Tabelas criadas"
    else
        log "✅ Tabelas WhatsApp encontradas"
    fi
}

# Testar webhook com payload de exemplo
test_webhook() {
    log "Testando webhook com payload de exemplo..."
    
    # Extrair apenas o body do payload para o teste
    WEBHOOK_PAYLOAD=$(cat test_webhook_payload.json | jq '.body')
    
    echo "$WEBHOOK_PAYLOAD" > temp_payload.json
    
    # Enviar webhook
    RESPONSE=$(curl -s -X POST http://localhost:8001/api/v1/whatsapp/webhook \
        -H "Content-Type: application/json" \
        -d @temp_payload.json)
    
    rm temp_payload.json
    
    if echo "$RESPONSE" | grep -q '"status":"ok"'; then
        log "✅ Webhook processado com sucesso"
    else
        error "❌ Erro no webhook: $RESPONSE"
    fi
}

# Verificar dados no banco
check_database_data() {
    log "Verificando dados salvos no banco..."
    
    # Verificar instâncias
    INSTANCES=$(docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -t -c "SELECT COUNT(*) FROM whatsapp_instances;" | tr -d ' \n\r')
    log "📊 Instâncias: $INSTANCES"
    
    # Verificar conversas
    CONVERSATIONS=$(docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -t -c "SELECT COUNT(*) FROM whatsapp_conversations;" | tr -d ' \n\r')
    log "📊 Conversas: $CONVERSATIONS"
    
    # Verificar mensagens
    MESSAGES=$(docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -t -c "SELECT COUNT(*) FROM whatsapp_messages;" | tr -d ' \n\r')
    log "📊 Mensagens: $MESSAGES"
    
    if [ "$MESSAGES" -gt "0" ]; then
        log "✅ Dados salvos com sucesso!"
        
        # Mostrar última mensagem
        log "📝 Última mensagem:"
        docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -c "
            SELECT 
                sender_name,
                content,
                timestamp,
                created_at
            FROM whatsapp_messages 
            ORDER BY created_at DESC 
            LIMIT 1;
        "
    else
        warn "⚠️ Nenhuma mensagem encontrada no banco"
    fi
}

# Testar API de conversas
test_conversations_api() {
    log "Testando API de conversas..."
    
    RESPONSE=$(curl -s http://localhost:8001/api/v1/whatsapp/conversations)
    
    if echo "$RESPONSE" | jq -e '. | length' > /dev/null 2>&1; then
        COUNT=$(echo "$RESPONSE" | jq '. | length')
        log "✅ API de conversas funcionando: $COUNT conversas"
    else
        warn "⚠️ Erro na API de conversas: $RESPONSE"
    fi
}

# Testar API de estatísticas
test_stats_api() {
    log "Testando API de estatísticas..."
    
    RESPONSE=$(curl -s http://localhost:8001/api/v1/whatsapp/stats)
    
    if echo "$RESPONSE" | jq -e '.total_conversations' > /dev/null 2>&1; then
        TOTAL=$(echo "$RESPONSE" | jq '.total_conversations')
        WAITING=$(echo "$RESPONSE" | jq '.waiting_conversations')
        UNREAD=$(echo "$RESPONSE" | jq '.unread_messages')
        
        log "✅ API de estatísticas funcionando:"
        log "   📊 Total: $TOTAL conversas"
        log "   ⏳ Aguardando: $WAITING"
        log "   📬 Não lidas: $UNREAD"
    else
        warn "⚠️ Erro na API de estatísticas: $RESPONSE"
    fi
}

# Limpar dados de teste (opcional)
cleanup_test_data() {
    read -p "Deseja limpar os dados de teste? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Limpando dados de teste..."
        
        docker exec -it amvox_postgres psql -U amvox_user -d amvox_db -c "
            DELETE FROM whatsapp_messages;
            DELETE FROM whatsapp_conversations;
            DELETE FROM whatsapp_instances;
        "
        
        log "✅ Dados limpos"
    fi
}

# Mostrar logs do backend
show_backend_logs() {
    log "Últimos logs do backend:"
    docker-compose logs --tail=20 backend
}

# Menu principal
main() {
    echo -e "\n${BLUE}Escolha uma opção:${NC}"
    echo "1. Teste completo (recomendado)"
    echo "2. Apenas verificar backend"
    echo "3. Apenas verificar banco"
    echo "4. Apenas testar webhook"
    echo "5. Apenas verificar dados"
    echo "6. Testar APIs"
    echo "7. Limpar dados de teste"
    echo "8. Mostrar logs do backend"
    echo "9. Sair"
    
    read -p "Digite sua escolha (1-9): " choice
    
    case $choice in
        1)
            check_backend
            check_database
            test_webhook
            check_database_data
            test_conversations_api
            test_stats_api
            cleanup_test_data
            ;;
        2)
            check_backend
            ;;
        3)
            check_database
            ;;
        4)
            test_webhook
            ;;
        5)
            check_database_data
            ;;
        6)
            test_conversations_api
            test_stats_api
            ;;
        7)
            cleanup_test_data
            ;;
        8)
            show_backend_logs
            ;;
        9)
            log "Saindo..."
            exit 0
            ;;
        *)
            error "Opção inválida"
            ;;
    esac
}

# Verificar dependências
if ! command -v jq &> /dev/null; then
    error "jq não encontrado. Instale com: sudo apt install jq"
fi

if ! command -v curl &> /dev/null; then
    error "curl não encontrado. Instale com: sudo apt install curl"
fi

# Executar
main
