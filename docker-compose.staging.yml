# Docker Compose para Staging - Amvox Omnichannel
version: '3.8'

services:
  backend:
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - NODE_ENV=staging
      - CORS_ORIGINS=https://staging.yourdomain.com
      - NEXT_PUBLIC_API_URL=https://staging-api.yourdomain.com
      - MICROSOFT_REDIRECT_URI=https://staging-api.yourdomain.com/api/v1/outlook/callback
    env_file:
      - ./back/.env.staging
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.8'
          memory: 800M
        reservations:
          cpus: '0.4'
          memory: 400M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  frontend:
    environment:
      - NODE_ENV=staging
      - NEXT_TELEMETRY_DISABLED=1
      - NEXT_PUBLIC_API_URL=https://staging-api.yourdomain.com
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.4'
          memory: 400M
        reservations:
          cpus: '0.2'
          memory: 200M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  postgres:
    environment:
      - POSTGRES_DB=amvox_staging_db
      - POSTGRES_USER=amvox_staging_user
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_staging_password
    secrets:
      - postgres_staging_password
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
      - ./backups:/backups
    deploy:
      resources:
        limits:
          cpus: '0.8'
          memory: 1G
        reservations:
          cpus: '0.4'
          memory: 512M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  redis:
    volumes:
      - redis_staging_data:/data
    deploy:
      resources:
        limits:
          cpus: '0.4'
          memory: 400M
        reservations:
          cpus: '0.2'
          memory: 200M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  nginx:
    volumes:
      - ./nginx/nginx.staging.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    deploy:
      resources:
        limits:
          cpus: '0.4'
          memory: 200M
        reservations:
          cpus: '0.2'
          memory: 100M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

volumes:
  postgres_staging_data:
    driver: local
  redis_staging_data:
    driver: local

secrets:
  postgres_staging_password:
    file: ./secrets/postgres_staging_password.txt

networks:
  default:
    name: amvox_staging_network
    driver: bridge
