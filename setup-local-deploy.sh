#!/bin/bash

# Script de Setup Automático - Amvox Omnichannel Local Deploy
# Este script configura e executa o deploy completo na máquina local

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Banner
show_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                                                              ║"
    echo "║           🚀 AMVOX OMNICHANNEL - LOCAL DEPLOY 🚀             ║"
    echo "║                                                              ║"
    echo "║              Setup Automático para Desenvolvimento          ║"
    echo "║                                                              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Verificar se está na branch correta
check_branch() {
    step "Verificando branch atual..."

    current_branch=$(git branch --show-current 2>/dev/null || echo "unknown")

    if [[ "$current_branch" != "b_deploy" ]]; then
        warning "Você não está na branch b_deploy. Mudando para b_deploy..."

        # Verificar se a branch existe
        if git show-ref --verify --quiet refs/heads/b_deploy; then
            git checkout b_deploy
        elif git show-ref --verify --quiet refs/remotes/origin/b_deploy; then
            git checkout -b b_deploy origin/b_deploy
        else
            error "Branch b_deploy não encontrada. Certifique-se de estar no repositório correto."
        fi
    fi

    success "Branch b_deploy ativa"
}

# Verificar dependências
check_dependencies() {
    step "Verificando dependências do sistema..."

    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado. Instale o Docker primeiro: https://docs.docker.com/get-docker/"
    fi

    # Verificar Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose não está instalado. Instale o Docker Compose primeiro."
    fi

    # Verificar se Docker está rodando
    if ! docker info &> /dev/null; then
        error "Docker não está rodando. Inicie o Docker primeiro."
    fi

    # Verificar Git
    if ! command -v git &> /dev/null; then
        error "Git não está instalado."
    fi

    # Verificar curl
    if ! command -v curl &> /dev/null; then
        warning "curl não está instalado. Instalando..."
        if command -v apt &> /dev/null; then
            sudo apt update && sudo apt install -y curl
        elif command -v yum &> /dev/null; then
            sudo yum install -y curl
        elif command -v brew &> /dev/null; then
            brew install curl
        else
            error "Não foi possível instalar curl automaticamente. Instale manualmente."
        fi
    fi

    success "Todas as dependências estão instaladas"
}

# Verificar portas disponíveis
check_ports() {
    step "Verificando portas disponíveis..."

    ports=(3000 8001 5432 6379 80)

    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            warning "Porta $port está em uso. Tentando liberar..."

            case $port in
                80)
                    # Tentar parar Apache/Nginx
                    sudo systemctl stop apache2 2>/dev/null || true
                    sudo systemctl stop nginx 2>/dev/null || true
                    ;;
                3000|8001)
                    # Matar processos Node/Python na porta
                    sudo lsof -ti:$port | xargs sudo kill -9 2>/dev/null || true
                    ;;
                5432)
                    # PostgreSQL local
                    sudo systemctl stop postgresql 2>/dev/null || true
                    ;;
                6379)
                    # Redis local
                    sudo systemctl stop redis 2>/dev/null || true
                    sudo systemctl stop redis-server 2>/dev/null || true
                    ;;
            esac

            sleep 2

            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                error "Não foi possível liberar a porta $port. Pare manualmente o serviço que está usando esta porta."
            fi
        fi
    done

    success "Todas as portas estão disponíveis"
}

# Configurar arquivo .env
setup_env() {
    step "Configurando arquivo de ambiente..."

    if [[ ! -f "back/.env.example" ]]; then
        error "Arquivo back/.env.example não encontrado. Certifique-se de estar no diretório correto."
    fi

    # Copiar .env.example para .env se não existir
    if [[ ! -f "back/.env" ]]; then
        cp back/.env.example back/.env
        info "Arquivo back/.env criado a partir do exemplo"
    else
        info "Arquivo back/.env já existe"
    fi

    # Configurar valores padrão para desenvolvimento local
    cat > back/.env << EOF
# Configuração Automática para Deploy Local - Amvox Omnichannel
# Gerado automaticamente em $(date)

# Aplicação
APP_NAME="Amvox Omnichannel API"
APP_VERSION="0.1.0"
DEBUG=true
LOG_LEVEL=DEBUG

# Servidor
HOST=0.0.0.0
PORT=8001

# CORS
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Banco de Dados (Docker)
DATABASE_URL=************************************************/amvox_db

# Redis (Docker)
REDIS_URL=redis://redis:6379

# Relatórios (valores padrão)
REPORTS_USER=amvox
REPORTS_PASSWORD=super_7894

# Microsoft Graph (configurar depois se necessário)
MICROSOFT_CLIENT_ID=
MICROSOFT_CLIENT_SECRET=
MICROSOFT_TENANT_ID=
MICROSOFT_REDIRECT_URI=http://localhost:8001/api/v1/outlook/callback

# 3CX (configurar depois se necessário)
THREECX_BASE_URL=
THREECX_API_KEY=
THREECX_TENANT_ID=
EOF

    success "Arquivo .env configurado para desenvolvimento local"
}

# Limpar ambiente Docker anterior
cleanup_docker() {
    step "Limpando ambiente Docker anterior..."

    # Parar containers se estiverem rodando
    docker-compose down 2>/dev/null || true

    # Remover containers órfãos
    docker-compose down --remove-orphans 2>/dev/null || true

    # Limpar imagens antigas (opcional)
    if [[ "$1" == "--clean" ]]; then
        warning "Removendo imagens antigas..."
        docker system prune -f
        docker-compose down -v  # Remove volumes também
    fi

    success "Ambiente Docker limpo"
}

# Fazer build das imagens
build_images() {
    step "Fazendo build das imagens Docker..."

    info "Isso pode levar alguns minutos na primeira vez..."

    # Build com output detalhado
    docker-compose build --no-cache --progress=plain

    success "Build das imagens concluído"
}

# Subir serviços
start_services() {
    step "Iniciando serviços..."

    # Subir serviços em background
    docker-compose up -d

    info "Aguardando serviços ficarem prontos..."

    # Aguardar PostgreSQL ficar pronto
    info "Aguardando PostgreSQL..."
    timeout=60
    while ! docker-compose exec -T postgres pg_isready -U amvox_user >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            error "PostgreSQL não ficou pronto a tempo"
        fi
    done

    # Aguardar Redis ficar pronto
    info "Aguardando Redis..."
    timeout=30
    while ! docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            error "Redis não ficou pronto a tempo"
        fi
    done

    # Aguardar Backend ficar pronto
    info "Aguardando Backend..."
    timeout=60
    while ! curl -f http://localhost:8001/health >/dev/null 2>&1; do
        sleep 3
        timeout=$((timeout - 3))
        if [[ $timeout -le 0 ]]; then
            error "Backend não ficou pronto a tempo"
        fi
    done

    # Aguardar Frontend ficar pronto
    info "Aguardando Frontend..."
    timeout=60
    while ! curl -f http://localhost:3000 >/dev/null 2>&1; do
        sleep 3
        timeout=$((timeout - 3))
        if [[ $timeout -le 0 ]]; then
            warning "Frontend pode estar ainda carregando..."
            break
        fi
    done

    success "Todos os serviços estão rodando"
}

# Verificar saúde dos serviços
health_check() {
    step "Verificando saúde dos serviços..."

    # Verificar containers
    info "Status dos containers:"
    docker-compose ps

    echo ""

    # Verificar endpoints
    info "Testando endpoints..."

    # Backend
    if curl -f http://localhost:8001/health >/dev/null 2>&1; then
        success "✅ Backend: http://localhost:8001 - OK"
    else
        error "❌ Backend não está respondendo"
    fi

    # Frontend
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        success "✅ Frontend: http://localhost:3000 - OK"
    else
        warning "⚠️  Frontend pode estar ainda carregando"
    fi

    # API Docs
    if curl -f http://localhost:8001/docs >/dev/null 2>&1; then
        success "✅ API Docs: http://localhost:8001/docs - OK"
    else
        warning "⚠️  API Docs não acessível"
    fi
}

# Mostrar informações finais
show_final_info() {
    echo ""
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}║                    🎉 DEPLOY CONCLUÍDO! 🎉                   ║${NC}"
    echo -e "${GREEN}║                                                              ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${CYAN}📋 INFORMAÇÕES DE ACESSO:${NC}"
    echo ""
    echo -e "🎨 ${YELLOW}Frontend (Interface):${NC}     http://localhost:3000"
    echo -e "🔧 ${YELLOW}Backend API:${NC}              http://localhost:8001"
    echo -e "📚 ${YELLOW}Documentação API:${NC}         http://localhost:8001/docs"
    echo -e "🗄️  ${YELLOW}PostgreSQL:${NC}               localhost:5432"
    echo -e "⚡ ${YELLOW}Redis:${NC}                    localhost:6379"
    echo ""
    echo -e "${CYAN}🛠️  COMANDOS ÚTEIS:${NC}"
    echo ""
    echo -e "📊 Ver logs:                  ${GREEN}docker-compose logs -f${NC}"
    echo -e "🔄 Reiniciar serviços:        ${GREEN}docker-compose restart${NC}"
    echo -e "⏹️  Parar serviços:            ${GREEN}docker-compose down${NC}"
    echo -e "🔍 Status dos containers:     ${GREEN}docker-compose ps${NC}"
    echo -e "💾 Backup do banco:           ${GREEN}./deploy.sh backup${NC}"
    echo -e "🏥 Health check:              ${GREEN}./deploy.sh health${NC}"
    echo ""
    echo -e "${CYAN}📁 ESTRUTURA DO PROJETO:${NC}"
    echo ""
    echo -e "├── 🎨 Frontend (Next.js)     → http://localhost:3000"
    echo -e "├── 🔧 Backend (FastAPI)      → http://localhost:8001"
    echo -e "├── 🗄️  PostgreSQL            → localhost:5432"
    echo -e "├── ⚡ Redis                  → localhost:6379"
    echo -e "└── 🔀 Nginx (Reverse Proxy) → http://localhost:80"
    echo ""
    echo -e "${YELLOW}⚠️  PRÓXIMOS PASSOS:${NC}"
    echo ""
    echo -e "1. Configure credenciais de email em: ${GREEN}back/.env${NC}"
    echo -e "2. Configure integração 3CX se necessário"
    echo -e "3. Configure Microsoft Graph se necessário"
    echo -e "4. Acesse http://localhost:3000 para usar a aplicação"
    echo ""
    echo -e "${GREEN}🚀 Aplicação pronta para uso!${NC}"
}

# Função principal
main() {
    show_banner

    log "Iniciando setup automático do Amvox Omnichannel..."

    # Verificar se estamos no diretório correto
    if [[ ! -f "docker-compose.yml" ]]; then
        error "Arquivo docker-compose.yml não encontrado. Execute este script no diretório raiz do projeto."
    fi

    # Executar etapas
    check_branch
    check_dependencies
    check_ports
    setup_env
    cleanup_docker "$1"
    build_images
    start_services
    health_check
    show_final_info

    success "Setup automático concluído com sucesso!"
}

# Verificar argumentos
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Uso: $0 [--clean]"
    echo ""
    echo "Opções:"
    echo "  --clean    Remove imagens e volumes Docker antigos"
    echo "  --help     Mostra esta ajuda"
    echo ""
    echo "Este script configura e executa o deploy completo do Amvox Omnichannel localmente."
    exit 0
fi

# Executar função principal
main "$@"
