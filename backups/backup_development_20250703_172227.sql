--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 15.13

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: conversationstatus; Type: TYPE; Schema: public; Owner: amvox_user
--

CREATE TYPE public.conversationstatus AS ENUM (
    'OPEN',
    'CLOSED',
    'WAITING',
    'ASSIGNED'
);


ALTER TYPE public.conversationstatus OWNER TO amvox_user;

--
-- Name: instancestatus; Type: TYPE; Schema: public; Owner: amvox_user
--

CREATE TYPE public.instancestatus AS ENUM (
    'CONNECTED',
    'DISCONNECTED',
    'CONNECTING',
    'QR_CODE'
);


ALTER TYPE public.instancestatus OWNER TO amvox_user;

--
-- Name: messagestatus; Type: TYPE; Schema: public; Owner: amvox_user
--

CREATE TYPE public.messagestatus AS ENUM (
    'SENT',
    'DELIVERED',
    'READ',
    'FAILED'
);


ALTER TYPE public.messagestatus OWNER TO amvox_user;

--
-- Name: messagetype; Type: TYPE; Schema: public; Owner: amvox_user
--

CREATE TYPE public.messagetype AS ENUM (
    'TEXT',
    'IMAGE',
    'AUDIO',
    'VIDEO',
    'DOCUMENT',
    'STICKER',
    'LOCATION',
    'CONTACT'
);


ALTER TYPE public.messagetype OWNER TO amvox_user;

--
-- Name: nivelusuario; Type: TYPE; Schema: public; Owner: amvox_user
--

CREATE TYPE public.nivelusuario AS ENUM (
    'ADMINISTRADOR',
    'AGENTE'
);


ALTER TYPE public.nivelusuario OWNER TO amvox_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: configuracoes_sistema; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.configuracoes_sistema (
    id integer NOT NULL,
    chave character varying(100) NOT NULL,
    valor text,
    descricao character varying(255),
    categoria character varying(50) NOT NULL,
    criado_em timestamp with time zone DEFAULT now() NOT NULL,
    atualizado_em timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.configuracoes_sistema OWNER TO amvox_user;

--
-- Name: COLUMN configuracoes_sistema.chave; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.chave IS 'Chave única da configuração';


--
-- Name: COLUMN configuracoes_sistema.valor; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.valor IS 'Valor da configuração (pode ser JSON)';


--
-- Name: COLUMN configuracoes_sistema.descricao; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.descricao IS 'Descrição da configuração';


--
-- Name: COLUMN configuracoes_sistema.categoria; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.categoria IS 'Categoria da configuração';


--
-- Name: COLUMN configuracoes_sistema.criado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.criado_em IS 'Data e hora de criação';


--
-- Name: COLUMN configuracoes_sistema.atualizado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.atualizado_em IS 'Data e hora da última atualização';


--
-- Name: configuracoes_sistema_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.configuracoes_sistema_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.configuracoes_sistema_id_seq OWNER TO amvox_user;

--
-- Name: configuracoes_sistema_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.configuracoes_sistema_id_seq OWNED BY public.configuracoes_sistema.id;


--
-- Name: outlook_tokens; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.outlook_tokens (
    id integer NOT NULL,
    user_id integer,
    outlook_user_id character varying(255),
    outlook_email character varying(255),
    outlook_name character varying(255),
    access_token text NOT NULL,
    refresh_token text,
    token_type character varying(50),
    scope text,
    expires_at timestamp with time zone,
    criado_em timestamp with time zone NOT NULL,
    atualizado_em timestamp with time zone NOT NULL,
    is_active boolean,
    last_used timestamp without time zone
);


ALTER TABLE public.outlook_tokens OWNER TO amvox_user;

--
-- Name: outlook_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.outlook_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.outlook_tokens_id_seq OWNER TO amvox_user;

--
-- Name: outlook_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.outlook_tokens_id_seq OWNED BY public.outlook_tokens.id;


--
-- Name: usuarios; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.usuarios (
    id integer NOT NULL,
    nome character varying(100) NOT NULL,
    sobrenome character varying(100) NOT NULL,
    login character varying(50) NOT NULL,
    senha character varying(255) NOT NULL,
    nivel_usuario public.nivelusuario NOT NULL,
    email_corporativo character varying(255) NOT NULL,
    senha_email_corporativo character varying(255) NOT NULL,
    ativo boolean NOT NULL,
    criado_em timestamp with time zone DEFAULT now() NOT NULL,
    atualizado_em timestamp with time zone DEFAULT now() NOT NULL,
    ultimo_login timestamp with time zone
);


ALTER TABLE public.usuarios OWNER TO amvox_user;

--
-- Name: COLUMN usuarios.nome; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.nome IS 'Nome do usuário';


--
-- Name: COLUMN usuarios.sobrenome; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.sobrenome IS 'Sobrenome do usuário';


--
-- Name: COLUMN usuarios.login; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.login IS 'Login único do usuário';


--
-- Name: COLUMN usuarios.senha; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.senha IS 'Senha criptografada do usuário';


--
-- Name: COLUMN usuarios.nivel_usuario; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.nivel_usuario IS 'Nível de acesso do usuário';


--
-- Name: COLUMN usuarios.email_corporativo; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.email_corporativo IS 'Email corporativo para integração Outlook';


--
-- Name: COLUMN usuarios.senha_email_corporativo; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.senha_email_corporativo IS 'Senha do email corporativo para integração Outlook';


--
-- Name: COLUMN usuarios.ativo; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.ativo IS 'Se o usuário está ativo';


--
-- Name: COLUMN usuarios.criado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.criado_em IS 'Data e hora de criação';


--
-- Name: COLUMN usuarios.atualizado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.atualizado_em IS 'Data e hora da última atualização';


--
-- Name: COLUMN usuarios.ultimo_login; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.ultimo_login IS 'Data e hora do último login';


--
-- Name: usuarios_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.usuarios_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.usuarios_id_seq OWNER TO amvox_user;

--
-- Name: usuarios_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.usuarios_id_seq OWNED BY public.usuarios.id;


--
-- Name: whatsapp_auto_replies; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.whatsapp_auto_replies (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    trigger_type character varying(50) NOT NULL,
    trigger_value character varying(500),
    response_message text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.whatsapp_auto_replies OWNER TO amvox_user;

--
-- Name: whatsapp_auto_replies_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.whatsapp_auto_replies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.whatsapp_auto_replies_id_seq OWNER TO amvox_user;

--
-- Name: whatsapp_auto_replies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.whatsapp_auto_replies_id_seq OWNED BY public.whatsapp_auto_replies.id;


--
-- Name: whatsapp_conversations; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.whatsapp_conversations (
    id integer NOT NULL,
    external_id character varying(200) NOT NULL,
    customer_name character varying(200) NOT NULL,
    customer_number character varying(50) NOT NULL,
    assigned_user_id integer,
    instance_id integer,
    status character varying(20) DEFAULT 'waiting'::character varying,
    last_message text,
    last_message_time timestamp with time zone,
    unread_count integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.whatsapp_conversations OWNER TO amvox_user;

--
-- Name: whatsapp_conversations_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.whatsapp_conversations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.whatsapp_conversations_id_seq OWNER TO amvox_user;

--
-- Name: whatsapp_conversations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.whatsapp_conversations_id_seq OWNED BY public.whatsapp_conversations.id;


--
-- Name: whatsapp_instances; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.whatsapp_instances (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    status character varying(20) DEFAULT 'disconnected'::character varying,
    qr_code text,
    webhook_url character varying(500),
    phone_number character varying(20),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.whatsapp_instances OWNER TO amvox_user;

--
-- Name: whatsapp_instances_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.whatsapp_instances_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.whatsapp_instances_id_seq OWNER TO amvox_user;

--
-- Name: whatsapp_instances_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.whatsapp_instances_id_seq OWNED BY public.whatsapp_instances.id;


--
-- Name: whatsapp_messages; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.whatsapp_messages (
    id integer NOT NULL,
    external_id character varying(200) NOT NULL,
    conversation_id integer,
    sender_type character varying(20) NOT NULL,
    sender_name character varying(200) NOT NULL,
    sender_number character varying(50),
    content text NOT NULL,
    message_type character varying(20) DEFAULT 'text'::character varying,
    status character varying(20) DEFAULT 'sent'::character varying,
    media_url character varying(500),
    "timestamp" timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.whatsapp_messages OWNER TO amvox_user;

--
-- Name: whatsapp_messages_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.whatsapp_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.whatsapp_messages_id_seq OWNER TO amvox_user;

--
-- Name: whatsapp_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.whatsapp_messages_id_seq OWNED BY public.whatsapp_messages.id;


--
-- Name: configuracoes_sistema id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.configuracoes_sistema ALTER COLUMN id SET DEFAULT nextval('public.configuracoes_sistema_id_seq'::regclass);


--
-- Name: outlook_tokens id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.outlook_tokens ALTER COLUMN id SET DEFAULT nextval('public.outlook_tokens_id_seq'::regclass);


--
-- Name: usuarios id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.usuarios ALTER COLUMN id SET DEFAULT nextval('public.usuarios_id_seq'::regclass);


--
-- Name: whatsapp_auto_replies id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_auto_replies ALTER COLUMN id SET DEFAULT nextval('public.whatsapp_auto_replies_id_seq'::regclass);


--
-- Name: whatsapp_conversations id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_conversations ALTER COLUMN id SET DEFAULT nextval('public.whatsapp_conversations_id_seq'::regclass);


--
-- Name: whatsapp_instances id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_instances ALTER COLUMN id SET DEFAULT nextval('public.whatsapp_instances_id_seq'::regclass);


--
-- Name: whatsapp_messages id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_messages ALTER COLUMN id SET DEFAULT nextval('public.whatsapp_messages_id_seq'::regclass);


--
-- Data for Name: configuracoes_sistema; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.configuracoes_sistema (id, chave, valor, descricao, categoria, criado_em, atualizado_em) FROM stdin;
\.


--
-- Data for Name: outlook_tokens; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.outlook_tokens (id, user_id, outlook_user_id, outlook_email, outlook_name, access_token, refresh_token, token_type, scope, expires_at, criado_em, atualizado_em, is_active, last_used) FROM stdin;
\.


--
-- Data for Name: usuarios; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.usuarios (id, nome, sobrenome, login, senha, nivel_usuario, email_corporativo, senha_email_corporativo, ativo, criado_em, atualizado_em, ultimo_login) FROM stdin;
2	Admin	Sistema	admin	$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5e	ADMINISTRADOR	<EMAIL>	senha123	t	2025-07-03 15:58:32.376825+00	2025-07-03 15:58:32.376825+00	\N
\.


--
-- Data for Name: whatsapp_auto_replies; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.whatsapp_auto_replies (id, name, trigger_type, trigger_value, response_message, is_active, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: whatsapp_conversations; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.whatsapp_conversations (id, external_id, customer_name, customer_number, assigned_user_id, instance_id, status, last_message, last_message_time, unread_count, created_at, updated_at) FROM stdin;
1	Priscila_557391035014	Tyandrer Boldt	557391035014	\N	1	WAITING	Oi, teste da POC WhatsApp!	2025-07-03 11:58:53+00	1	2025-07-03 15:50:18.727869+00	2025-07-03 15:50:18.748+00
\.


--
-- Data for Name: whatsapp_instances; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.whatsapp_instances (id, name, status, qr_code, webhook_url, phone_number, created_at, updated_at) FROM stdin;
1	Priscila	DISCONNECTED	\N	\N	\N	2025-07-03 15:50:18.719292+00	2025-07-03 15:50:18.719292+00
\.


--
-- Data for Name: whatsapp_messages; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.whatsapp_messages (id, external_id, conversation_id, sender_type, sender_name, sender_number, content, message_type, status, media_url, "timestamp", created_at) FROM stdin;
1	66684BB473B94CDAEEFB2874BF702FBC	1	customer	Tyandrer Boldt	557391035014	Oi, teste da POC WhatsApp!	TEXT	SENT	\N	2025-07-03 11:58:53+00	2025-07-03 15:50:18.738438+00
\.


--
-- Name: configuracoes_sistema_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.configuracoes_sistema_id_seq', 1, false);


--
-- Name: outlook_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.outlook_tokens_id_seq', 1, false);


--
-- Name: usuarios_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.usuarios_id_seq', 3, true);


--
-- Name: whatsapp_auto_replies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.whatsapp_auto_replies_id_seq', 1, false);


--
-- Name: whatsapp_conversations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.whatsapp_conversations_id_seq', 1, true);


--
-- Name: whatsapp_instances_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.whatsapp_instances_id_seq', 1, true);


--
-- Name: whatsapp_messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.whatsapp_messages_id_seq', 1, true);


--
-- Name: configuracoes_sistema configuracoes_sistema_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.configuracoes_sistema
    ADD CONSTRAINT configuracoes_sistema_pkey PRIMARY KEY (id);


--
-- Name: outlook_tokens outlook_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.outlook_tokens
    ADD CONSTRAINT outlook_tokens_pkey PRIMARY KEY (id);


--
-- Name: usuarios usuarios_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.usuarios
    ADD CONSTRAINT usuarios_pkey PRIMARY KEY (id);


--
-- Name: whatsapp_auto_replies whatsapp_auto_replies_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_auto_replies
    ADD CONSTRAINT whatsapp_auto_replies_pkey PRIMARY KEY (id);


--
-- Name: whatsapp_conversations whatsapp_conversations_external_id_key; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_conversations
    ADD CONSTRAINT whatsapp_conversations_external_id_key UNIQUE (external_id);


--
-- Name: whatsapp_conversations whatsapp_conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_conversations
    ADD CONSTRAINT whatsapp_conversations_pkey PRIMARY KEY (id);


--
-- Name: whatsapp_instances whatsapp_instances_name_key; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_instances
    ADD CONSTRAINT whatsapp_instances_name_key UNIQUE (name);


--
-- Name: whatsapp_instances whatsapp_instances_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_instances
    ADD CONSTRAINT whatsapp_instances_pkey PRIMARY KEY (id);


--
-- Name: whatsapp_messages whatsapp_messages_external_id_key; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_messages
    ADD CONSTRAINT whatsapp_messages_external_id_key UNIQUE (external_id);


--
-- Name: whatsapp_messages whatsapp_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_messages
    ADD CONSTRAINT whatsapp_messages_pkey PRIMARY KEY (id);


--
-- Name: idx_whatsapp_conversations_external_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_whatsapp_conversations_external_id ON public.whatsapp_conversations USING btree (external_id);


--
-- Name: idx_whatsapp_conversations_status; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_whatsapp_conversations_status ON public.whatsapp_conversations USING btree (status);


--
-- Name: idx_whatsapp_instances_name; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_whatsapp_instances_name ON public.whatsapp_instances USING btree (name);


--
-- Name: idx_whatsapp_messages_conversation_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_whatsapp_messages_conversation_id ON public.whatsapp_messages USING btree (conversation_id);


--
-- Name: idx_whatsapp_messages_timestamp; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_whatsapp_messages_timestamp ON public.whatsapp_messages USING btree ("timestamp");


--
-- Name: ix_configuracoes_sistema_chave; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE UNIQUE INDEX ix_configuracoes_sistema_chave ON public.configuracoes_sistema USING btree (chave);


--
-- Name: ix_configuracoes_sistema_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_configuracoes_sistema_id ON public.configuracoes_sistema USING btree (id);


--
-- Name: ix_outlook_tokens_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_outlook_tokens_id ON public.outlook_tokens USING btree (id);


--
-- Name: ix_outlook_tokens_outlook_email; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_outlook_tokens_outlook_email ON public.outlook_tokens USING btree (outlook_email);


--
-- Name: ix_outlook_tokens_outlook_user_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE UNIQUE INDEX ix_outlook_tokens_outlook_user_id ON public.outlook_tokens USING btree (outlook_user_id);


--
-- Name: ix_outlook_tokens_user_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE UNIQUE INDEX ix_outlook_tokens_user_id ON public.outlook_tokens USING btree (user_id);


--
-- Name: ix_usuarios_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_usuarios_id ON public.usuarios USING btree (id);


--
-- Name: ix_usuarios_login; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE UNIQUE INDEX ix_usuarios_login ON public.usuarios USING btree (login);


--
-- Name: outlook_tokens outlook_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.outlook_tokens
    ADD CONSTRAINT outlook_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.usuarios(id);


--
-- Name: whatsapp_conversations whatsapp_conversations_instance_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_conversations
    ADD CONSTRAINT whatsapp_conversations_instance_id_fkey FOREIGN KEY (instance_id) REFERENCES public.whatsapp_instances(id);


--
-- Name: whatsapp_messages whatsapp_messages_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.whatsapp_messages
    ADD CONSTRAINT whatsapp_messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.whatsapp_conversations(id);


--
-- PostgreSQL database dump complete
--

