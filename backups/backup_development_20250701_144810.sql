--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 15.13

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: nivelusuario; Type: TYPE; Schema: public; Owner: amvox_user
--

CREATE TYPE public.nivelusuario AS ENUM (
    'ADMINISTRADOR',
    'AGENTE'
);


ALTER TYPE public.nivelusuario OWNER TO amvox_user;

--
-- Name: update_outlook_tokens_atualizado_em(); Type: FUNCTION; Schema: public; Owner: amvox_user
--

CREATE FUNCTION public.update_outlook_tokens_atualizado_em() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.atualizado_em = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_outlook_tokens_atualizado_em() OWNER TO amvox_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: configuracoes_sistema; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.configuracoes_sistema (
    id integer NOT NULL,
    chave character varying(100) NOT NULL,
    valor text,
    descricao character varying(255),
    categoria character varying(50) NOT NULL,
    criado_em timestamp with time zone DEFAULT now() NOT NULL,
    atualizado_em timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.configuracoes_sistema OWNER TO amvox_user;

--
-- Name: COLUMN configuracoes_sistema.chave; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.chave IS 'Chave única da configuração';


--
-- Name: COLUMN configuracoes_sistema.valor; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.valor IS 'Valor da configuração (pode ser JSON)';


--
-- Name: COLUMN configuracoes_sistema.descricao; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.descricao IS 'Descrição da configuração';


--
-- Name: COLUMN configuracoes_sistema.categoria; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.categoria IS 'Categoria da configuração';


--
-- Name: COLUMN configuracoes_sistema.criado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.criado_em IS 'Data e hora de criação';


--
-- Name: COLUMN configuracoes_sistema.atualizado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.configuracoes_sistema.atualizado_em IS 'Data e hora da última atualização';


--
-- Name: configuracoes_sistema_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.configuracoes_sistema_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.configuracoes_sistema_id_seq OWNER TO amvox_user;

--
-- Name: configuracoes_sistema_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.configuracoes_sistema_id_seq OWNED BY public.configuracoes_sistema.id;


--
-- Name: outlook_tokens; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.outlook_tokens (
    id integer NOT NULL,
    user_id integer NOT NULL,
    outlook_user_id character varying(255) NOT NULL,
    outlook_email character varying(255) NOT NULL,
    outlook_name character varying(255),
    access_token text NOT NULL,
    refresh_token text,
    token_type character varying(50) NOT NULL,
    scope text,
    expires_at timestamp with time zone,
    criado_em timestamp with time zone DEFAULT now() NOT NULL,
    atualizado_em timestamp with time zone DEFAULT now() NOT NULL,
    is_active boolean DEFAULT true,
    last_used timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.outlook_tokens OWNER TO amvox_user;

--
-- Name: TABLE outlook_tokens; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON TABLE public.outlook_tokens IS 'Armazena tokens OAuth2 do Microsoft Graph para integração com Outlook';


--
-- Name: COLUMN outlook_tokens.user_id; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.user_id IS 'ID do usuário do sistema';


--
-- Name: COLUMN outlook_tokens.outlook_user_id; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.outlook_user_id IS 'ID único do usuário no Microsoft Graph';


--
-- Name: COLUMN outlook_tokens.outlook_email; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.outlook_email IS 'Email do usuário no Outlook';


--
-- Name: COLUMN outlook_tokens.outlook_name; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.outlook_name IS 'Nome de exibição do usuário no Outlook';


--
-- Name: COLUMN outlook_tokens.access_token; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.access_token IS 'Token de acesso OAuth2';


--
-- Name: COLUMN outlook_tokens.refresh_token; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.refresh_token IS 'Token de renovação OAuth2';


--
-- Name: COLUMN outlook_tokens.token_type; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.token_type IS 'Tipo do token';


--
-- Name: COLUMN outlook_tokens.scope; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.scope IS 'Escopos de permissão em formato JSON';


--
-- Name: COLUMN outlook_tokens.expires_at; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.expires_at IS 'Data/hora de expiração do access_token';


--
-- Name: COLUMN outlook_tokens.criado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.criado_em IS 'Data e hora de criação';


--
-- Name: COLUMN outlook_tokens.atualizado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.outlook_tokens.atualizado_em IS 'Data e hora da última atualização';


--
-- Name: outlook_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.outlook_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.outlook_tokens_id_seq OWNER TO amvox_user;

--
-- Name: outlook_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.outlook_tokens_id_seq OWNED BY public.outlook_tokens.id;


--
-- Name: usuarios; Type: TABLE; Schema: public; Owner: amvox_user
--

CREATE TABLE public.usuarios (
    id integer NOT NULL,
    nome character varying(100) NOT NULL,
    sobrenome character varying(100) NOT NULL,
    login character varying(50) NOT NULL,
    senha character varying(255) NOT NULL,
    nivel_usuario public.nivelusuario NOT NULL,
    email_corporativo character varying(255) NOT NULL,
    senha_email_corporativo character varying(255) NOT NULL,
    ativo boolean NOT NULL,
    criado_em timestamp with time zone DEFAULT now() NOT NULL,
    atualizado_em timestamp with time zone DEFAULT now() NOT NULL,
    ultimo_login timestamp with time zone,
    usuario_3cx character varying(100),
    senha_3cx character varying(255),
    token_whatsapp character varying(500),
    phone_number_whatsapp character varying(20)
);


ALTER TABLE public.usuarios OWNER TO amvox_user;

--
-- Name: COLUMN usuarios.nome; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.nome IS 'Nome do usuário';


--
-- Name: COLUMN usuarios.sobrenome; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.sobrenome IS 'Sobrenome do usuário';


--
-- Name: COLUMN usuarios.login; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.login IS 'Login único do usuário';


--
-- Name: COLUMN usuarios.senha; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.senha IS 'Senha criptografada do usuário';


--
-- Name: COLUMN usuarios.nivel_usuario; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.nivel_usuario IS 'Nível de acesso do usuário';


--
-- Name: COLUMN usuarios.email_corporativo; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.email_corporativo IS 'Email corporativo para integração Outlook';


--
-- Name: COLUMN usuarios.senha_email_corporativo; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.senha_email_corporativo IS 'Senha do email corporativo para integração Outlook';


--
-- Name: COLUMN usuarios.ativo; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.ativo IS 'Se o usuário está ativo';


--
-- Name: COLUMN usuarios.criado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.criado_em IS 'Data e hora de criação';


--
-- Name: COLUMN usuarios.atualizado_em; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.atualizado_em IS 'Data e hora da última atualização';


--
-- Name: COLUMN usuarios.ultimo_login; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.ultimo_login IS 'Data e hora do último login';


--
-- Name: COLUMN usuarios.usuario_3cx; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.usuario_3cx IS 'Usuário do 3CX para login automático';


--
-- Name: COLUMN usuarios.senha_3cx; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.senha_3cx IS 'Senha do 3CX criptografada';


--
-- Name: COLUMN usuarios.token_whatsapp; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.token_whatsapp IS 'Token de acesso do WhatsApp Business';


--
-- Name: COLUMN usuarios.phone_number_whatsapp; Type: COMMENT; Schema: public; Owner: amvox_user
--

COMMENT ON COLUMN public.usuarios.phone_number_whatsapp IS 'Número de telefone do WhatsApp Business';


--
-- Name: usuarios_id_seq; Type: SEQUENCE; Schema: public; Owner: amvox_user
--

CREATE SEQUENCE public.usuarios_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.usuarios_id_seq OWNER TO amvox_user;

--
-- Name: usuarios_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: amvox_user
--

ALTER SEQUENCE public.usuarios_id_seq OWNED BY public.usuarios.id;


--
-- Name: configuracoes_sistema id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.configuracoes_sistema ALTER COLUMN id SET DEFAULT nextval('public.configuracoes_sistema_id_seq'::regclass);


--
-- Name: outlook_tokens id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.outlook_tokens ALTER COLUMN id SET DEFAULT nextval('public.outlook_tokens_id_seq'::regclass);


--
-- Name: usuarios id; Type: DEFAULT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.usuarios ALTER COLUMN id SET DEFAULT nextval('public.usuarios_id_seq'::regclass);


--
-- Data for Name: configuracoes_sistema; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.configuracoes_sistema (id, chave, valor, descricao, categoria, criado_em, atualizado_em) FROM stdin;
1	microsoft_graph_client_id	0e7e58b3-146d-43fc-9201-d2d6a6583c00	Client ID do Microsoft Graph (Azure AD)	microsoft_graph	2025-06-15 22:10:53.241128+00	2025-06-15 22:10:53.241128+00
3	microsoft_graph_tenant_id	ca682a90-2b2d-4030-b0f8-8989329705af	Tenant ID do Microsoft Graph (Azure AD)	microsoft_graph	2025-06-15 22:10:53.274518+00	2025-06-15 22:10:53.274518+00
2	microsoft_graph_client_secret	****************************************	Client Secret do Microsoft Graph (Azure AD)	microsoft_graph	2025-06-15 22:10:53.262396+00	2025-06-15 22:42:50.21085+00
\.


--
-- Data for Name: outlook_tokens; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.outlook_tokens (id, user_id, outlook_user_id, outlook_email, outlook_name, access_token, refresh_token, token_type, scope, expires_at, criado_em, atualizado_em, is_active, last_used) FROM stdin;
3	2	2c206148-f55c-4053-ad61-0f4445a963f8	<EMAIL>	Protheus Posvenda	eyJ0eXAiOiJKV1QiLCJub25jZSI6IklERVJ6VXNrOEl6bFZnTHZyZ0twU0JYY0RQZVZ3clVkN19Ccjg0clIzM00iLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aa-mQl1YPqdwDXwjA_kBMtnjr9TXkLavCM9bxci0LZusaQeoYdtErTTF-aE0R7tQcZ7rSoWVFYSpXgCFBTGJF7Hen8DK3_4HmCawBrMWfQEZ_jk41EZ1vdeT_5GlDN2oIIggWqieJO-DsBLC8xInzMi9HDeI6tUa_Rlc3pgXwumvm7nw0rbrfrnmSD5yCUTs3bBfjQ1HUyVyrgX85MknZ1or755RSGpA2e2VJJOkrls4q0xG5jz4IDUUgQ_hcrcO_vBRlSZVFhU_ZU71SM3QVfYTIQKxY7pYI7a0eqPQpuFPhxLIDFDNKpF_yu9oacgkJPHF2Xw3WdWrB9FOJmweCg	*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	Bearer	["openid", "profile", "email", "https://graph.microsoft.com/Mail.Read", "https://graph.microsoft.com/Mail.ReadWrite", "https://graph.microsoft.com/Mail.Send", "https://graph.microsoft.com/User.Read"]	2025-06-27 13:27:43.978494+00	2025-06-27 11:58:51.538486+00	2025-06-27 11:58:56.017055+00	f	2025-06-27 11:58:51.533663
2	1	2c206148-f55c-4053-ad61-0f4445a963f8	<EMAIL>	Protheus Posvenda	*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************	Bearer	["openid", "profile", "email", "https://graph.microsoft.com/Mail.Read", "https://graph.microsoft.com/Mail.ReadWrite", "https://graph.microsoft.com/Mail.Send", "https://graph.microsoft.com/User.Read"]	2025-07-01 14:27:49.006636+00	2025-06-23 16:40:40.091577+00	2025-07-01 13:05:01.655369+00	t	2025-07-01 13:05:01.657448
\.


--
-- Data for Name: usuarios; Type: TABLE DATA; Schema: public; Owner: amvox_user
--

COPY public.usuarios (id, nome, sobrenome, login, senha, nivel_usuario, email_corporativo, senha_email_corporativo, ativo, criado_em, atualizado_em, ultimo_login, usuario_3cx, senha_3cx, token_whatsapp, phone_number_whatsapp) FROM stdin;
2	Filipe	Amorim	lipesamorim	$2b$12$gRmhVy6TsE25F4lOMEQ8KORIN1hGzmt7XUne3Tcs02oyWKl8/5qsa	AGENTE	<EMAIL>	bf2caf697aa771f6968ff797f5de97f741be25710378981864330470317201df	t	2025-06-27 11:58:19.027719+00	2025-07-01 11:26:22.478582+00	2025-07-01 11:26:22.719717+00	\N	\N	\N	\N
1	Administrador	Teste	admin	$2b$12$GJSQ9b1GdkWFSET8.OSQ1eDJZw7Mmv.0IINwMbchL0fJp26Aco9e2	ADMINISTRADOR	<EMAIL>	bf2caf697aa771f6968ff797f5de97f741be25710378981864330470317201df	t	2025-06-15 20:30:57.607856+00	2025-07-01 13:03:41.125129+00	2025-07-01 13:03:41.526624+00	teste_amvox	0ffe1abd1a08215353c233d6e009613e95eec4253832a761af28ff37ac5a150c	\N	\N
\.


--
-- Name: configuracoes_sistema_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.configuracoes_sistema_id_seq', 3, true);


--
-- Name: outlook_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.outlook_tokens_id_seq', 3, true);


--
-- Name: usuarios_id_seq; Type: SEQUENCE SET; Schema: public; Owner: amvox_user
--

SELECT pg_catalog.setval('public.usuarios_id_seq', 2, true);


--
-- Name: configuracoes_sistema configuracoes_sistema_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.configuracoes_sistema
    ADD CONSTRAINT configuracoes_sistema_pkey PRIMARY KEY (id);


--
-- Name: outlook_tokens outlook_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.outlook_tokens
    ADD CONSTRAINT outlook_tokens_pkey PRIMARY KEY (id);


--
-- Name: usuarios usuarios_pkey; Type: CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.usuarios
    ADD CONSTRAINT usuarios_pkey PRIMARY KEY (id);


--
-- Name: idx_outlook_tokens_expires_at; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_outlook_tokens_expires_at ON public.outlook_tokens USING btree (expires_at);


--
-- Name: idx_outlook_tokens_is_active; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_outlook_tokens_is_active ON public.outlook_tokens USING btree (is_active);


--
-- Name: idx_outlook_tokens_outlook_email; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_outlook_tokens_outlook_email ON public.outlook_tokens USING btree (outlook_email);


--
-- Name: idx_outlook_tokens_outlook_user_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_outlook_tokens_outlook_user_id ON public.outlook_tokens USING btree (outlook_user_id);


--
-- Name: idx_outlook_tokens_user_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX idx_outlook_tokens_user_id ON public.outlook_tokens USING btree (user_id);


--
-- Name: ix_configuracoes_sistema_chave; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE UNIQUE INDEX ix_configuracoes_sistema_chave ON public.configuracoes_sistema USING btree (chave);


--
-- Name: ix_configuracoes_sistema_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_configuracoes_sistema_id ON public.configuracoes_sistema USING btree (id);


--
-- Name: ix_outlook_tokens_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_outlook_tokens_id ON public.outlook_tokens USING btree (id);


--
-- Name: ix_outlook_tokens_outlook_user_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_outlook_tokens_outlook_user_id ON public.outlook_tokens USING btree (outlook_user_id);


--
-- Name: ix_outlook_tokens_user_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_outlook_tokens_user_id ON public.outlook_tokens USING btree (user_id);


--
-- Name: ix_usuarios_id; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE INDEX ix_usuarios_id ON public.usuarios USING btree (id);


--
-- Name: ix_usuarios_login; Type: INDEX; Schema: public; Owner: amvox_user
--

CREATE UNIQUE INDEX ix_usuarios_login ON public.usuarios USING btree (login);


--
-- Name: outlook_tokens trigger_outlook_tokens_atualizado_em; Type: TRIGGER; Schema: public; Owner: amvox_user
--

CREATE TRIGGER trigger_outlook_tokens_atualizado_em BEFORE UPDATE ON public.outlook_tokens FOR EACH ROW EXECUTE FUNCTION public.update_outlook_tokens_atualizado_em();


--
-- Name: outlook_tokens outlook_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: amvox_user
--

ALTER TABLE ONLY public.outlook_tokens
    ADD CONSTRAINT outlook_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.usuarios(id);


--
-- PostgreSQL database dump complete
--

