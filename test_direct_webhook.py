#!/usr/bin/env python3
"""
Teste direto do webhook handler
"""

import json
import sys
import os
import asyncio
from datetime import datetime

# Adicionar o diretório back ao path
sys.path.append('back')

from app.services.integrations.evolution.webhook_handler import webhook_handler

# Payload de teste
test_payload = {
    "event": "messages.upsert",
    "instance": "<PERSON><PERSON>cila",
    "data": {
        "key": {
            "remoteJid": "<EMAIL>",
            "fromMe": False,
            "id": "66684BB473B94CDAEEFB2874BF702FBC"
        },
        "pushName": "Tyandrer Boldt",
        "status": "DELIVERY_ACK",
        "message": {
            "conversation": "Oi",
            "messageContextInfo": {
                "deviceListMetadata": {
                    "senderKeyHash": "UZxKsElj7qGuTg==",
                    "senderTimestamp": "1751050623",
                    "recipientKeyHash": "r9HFifnluj6mmw==",
                    "recipientTimestamp": "1750341552"
                },
                "deviceListMetadataVersion": 2,
                "messageSecret": "OGVC6R20czwEorG5DNgDtq08kbXuAMiHFlUnTrU52CM="
            }
        },
        "messageType": "conversation",
        "messageTimestamp": 1751543933,
        "instanceId": "2d60b42e-8d05-4138-8031-2f626e9ef55e",
        "source": "android",
        "chatwootMessageId": 246,
        "chatwootInboxId": 1,
        "chatwootConversationId": 2
    },
    "destination": "https://n8n.aplopes.com/webhook/odonto",
    "date_time": "2025-07-03T08:58:53.101Z",
    "sender": "<EMAIL>",
    "server_url": "http://localhost:8080",
    "apikey": "19248C6D2CAB-4249-A5AC-5FCFE48DD932"
}

async def test_webhook():
    """Testar webhook diretamente"""
    print("🧪 Testando webhook handler diretamente...")
    
    try:
        result = await webhook_handler.process_webhook(test_payload)
        print(f"✅ Resultado: {result}")
        
        if result:
            print("✅ Webhook processado com sucesso!")
        else:
            print("❌ Webhook falhou")
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_webhook())
