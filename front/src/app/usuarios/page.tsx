'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { AdminRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { Users, UserPlus, Edit, Trash2, MoreVertical, Shield, User } from 'lucide-react';
import UserModal from '@/components/usuarios/UserModal';
import UserFilters from '@/components/usuarios/UserFilters';
import { useToast, ToastContainer } from '@/components/ui/Toast';

interface Usuario {
  id: number;
  nome: string;
  sobrenome: string;
  login: string;
  nivel_usuario: 'administrador' | 'agente';
  email_corporativo: string;
  ativo: boolean;
  data_criacao: string;
  ultimo_login?: string;
}

export default function UsuariosPage() {
  const [usuarios, setUsuarios] = useState<Usuario[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [selectedUser, setSelectedUser] = useState<Usuario | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [levelFilter, setLevelFilter] = useState<'all' | 'administrador' | 'agente'>('all');
  const { user } = useAuth();
  const { toasts, removeToast, success, error, warning } = useToast();

  // Filtrar usuários
  const filteredUsers = useMemo(() => {
    return usuarios.filter(usuario => {
      // Filtro de busca
      const matchesSearch = !searchTerm ||
        usuario.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        usuario.sobrenome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        usuario.login.toLowerCase().includes(searchTerm.toLowerCase()) ||
        usuario.email_corporativo.toLowerCase().includes(searchTerm.toLowerCase());

      // Filtro de status
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && usuario.ativo) ||
        (statusFilter === 'inactive' && !usuario.ativo);

      // Filtro de nível
      const matchesLevel = levelFilter === 'all' || usuario.nivel_usuario === levelFilter;

      return matchesSearch && matchesStatus && matchesLevel;
    });
  }, [usuarios, searchTerm, statusFilter, levelFilter]);

  // Carregar usuários
  const carregarUsuarios = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/v1/usuarios', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsuarios(data);
      } else {
        error('Erro ao carregar usuários', 'Verifique sua conexão e tente novamente');
      }
    } catch (err) {
      console.error('Erro:', err);
      error('Erro ao conectar com o servidor', 'Verifique sua conexão de rede');
    } finally {
      setIsLoading(false);
    }
  };

  // Salvar usuário (criar ou editar)
  const salvarUsuario = async (userData: any) => {
    try {
      setIsLoading(true);

      let url, method, body;

      if (modalMode === 'create') {
        url = '/api/v1/usuarios';
        method = 'POST';
        body = JSON.stringify(userData);
      } else {
        // Usar endpoint de ação para contornar problema com PUT
        url = '/api/v1/usuarios/action';
        method = 'POST';
        body = JSON.stringify({
          action: 'update',
          userId: selectedUser?.id,
          ...userData
        });
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json',
        },
        body,
      });

      if (response.ok) {
        const message = modalMode === 'create' ? 'Usuário criado com sucesso!' : 'Usuário atualizado com sucesso!';
        success(message);
        setShowModal(false);
        setSelectedUser(null);
        carregarUsuarios();
      } else {
        const errorData = await response.json();
        error('Erro ao salvar usuário', errorData.detail || 'Verifique os dados e tente novamente');
      }
    } catch (err) {
      console.error('Erro:', err);
      error('Erro ao conectar com o servidor', 'Verifique sua conexão de rede');
    } finally {
      setIsLoading(false);
    }
  };

  // Excluir usuário
  const excluirUsuario = async (usuario: Usuario) => {
    if (!confirm(`Tem certeza que deseja excluir o usuário ${usuario.nome} ${usuario.sobrenome}?`)) return;

    try {
      // Usar endpoint de ação para contornar problema com DELETE
      const response = await fetch('/api/v1/usuarios/action', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'delete',
          userId: usuario.id
        }),
      });

      if (response.ok) {
        success('Usuário excluído com sucesso!');
        carregarUsuarios();
      } else {
        const errorData = await response.json();
        error('Erro ao excluir usuário', errorData.detail || 'Tente novamente');
      }
    } catch (err) {
      console.error('Erro:', err);
      error('Erro ao conectar com o servidor', 'Verifique sua conexão de rede');
    }
  };

  // Abrir modal para criar usuário
  const abrirModalCriar = () => {
    setModalMode('create');
    setSelectedUser(null);
    setShowModal(true);
  };

  // Abrir modal para editar usuário
  const abrirModalEditar = (usuario: Usuario) => {
    setModalMode('edit');
    setSelectedUser(usuario);
    setShowModal(true);
  };

  // Fechar modal
  const fecharModal = () => {
    setShowModal(false);
    setSelectedUser(null);
  };

  // Limpar filtros
  const limparFiltros = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setLevelFilter('all');
  };

  // Carregar ao montar
  useEffect(() => {
    carregarUsuarios();
  }, []);

  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-50 p-4 lg:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Users className="mr-3 h-8 w-8 text-blue-600" />
                Gerenciamento de Usuários
              </h1>
              <p className="text-gray-600 mt-2">
                Gerencie usuários do sistema com controle total de acesso
              </p>
            </div>
            <button
              onClick={abrirModalCriar}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center transition-colors shadow-sm"
            >
              <UserPlus className="mr-2 h-5 w-5" />
              Novo Usuário
            </button>
          </div>

          {/* Filtros */}
          <UserFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            levelFilter={levelFilter}
            onLevelFilterChange={setLevelFilter}
            onClearFilters={limparFiltros}
            totalUsers={usuarios.length}
            filteredUsers={filteredUsers.length}
          />

          {/* Loading */}
          {isLoading && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Carregando usuários...</p>
              </div>
            </div>
          )}

          {/* Lista de Usuários */}
          {!isLoading && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {filteredUsers.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum usuário encontrado</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {usuarios.length === 0
                      ? 'Comece criando um novo usuário.'
                      : 'Tente ajustar os filtros de busca.'}
                  </p>
                  {usuarios.length === 0 && (
                    <div className="mt-6">
                      <button
                        onClick={abrirModalCriar}
                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <UserPlus className="mr-2 h-4 w-4" />
                        Criar primeiro usuário
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Usuário
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Login
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Nível
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Último Login
                        </th>
                        <th className="relative px-6 py-3">
                          <span className="sr-only">Ações</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredUsers.map((usuario) => (
                        <tr key={usuario.id} className="hover:bg-gray-50 transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                                  usuario.nivel_usuario === 'administrador'
                                    ? 'bg-purple-100 text-purple-600'
                                    : 'bg-blue-100 text-blue-600'
                                }`}>
                                  {usuario.nivel_usuario === 'administrador' ? (
                                    <Shield className="h-5 w-5" />
                                  ) : (
                                    <User className="h-5 w-5" />
                                  )}
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {usuario.nome} {usuario.sobrenome}
                                </div>
                                <div className="text-sm text-gray-500">{usuario.email_corporativo}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900 font-mono">{usuario.login}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              usuario.nivel_usuario === 'administrador'
                                ? 'bg-purple-100 text-purple-800'
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {usuario.nivel_usuario === 'administrador' ? (
                                <>
                                  <Shield className="mr-1 h-3 w-3" />
                                  Administrador
                                </>
                              ) : (
                                <>
                                  <User className="mr-1 h-3 w-3" />
                                  Agente
                                </>
                              )}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              usuario.ativo
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              <span className={`mr-1.5 h-2 w-2 rounded-full ${
                                usuario.ativo ? 'bg-green-400' : 'bg-red-400'
                              }`}></span>
                              {usuario.ativo ? 'Ativo' : 'Inativo'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {usuario.ultimo_login
                              ? new Date(usuario.ultimo_login).toLocaleDateString('pt-BR', {
                                  day: '2-digit',
                                  month: '2-digit',
                                  year: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })
                              : 'Nunca'
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center justify-end space-x-2">
                              <button
                                onClick={() => abrirModalEditar(usuario)}
                                className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                                title="Editar usuário"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              {usuario.id !== user?.id && (
                                <button
                                  onClick={() => excluirUsuario(usuario)}
                                  className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                                  title="Excluir usuário"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}

          {/* Modal de Usuário */}
          <UserModal
            isOpen={showModal}
            onClose={fecharModal}
            onSave={salvarUsuario}
            user={selectedUser}
            isLoading={isLoading}
            mode={modalMode}
          />

          {/* Toast Container */}
          <ToastContainer toasts={toasts} onClose={removeToast} />
        </div>
      </div>
    </AdminRoute>
  );
}
