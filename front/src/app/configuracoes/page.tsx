'use client';

import React, { useState, useEffect } from 'react';
import { AdminRoute } from '@/components/auth/ProtectedRoute';
import { Settings, Mail, Server, Users, Shield, Database, Bell, FileText, Zap, Phone, MessageSquare } from 'lucide-react';
import OutlookConfigSection from '@/components/configuracoes/OutlookConfigSection';
import SystemConfigSection from '@/components/configuracoes/SystemConfigSection';
import SecurityConfigSection from '@/components/configuracoes/SecurityConfigSection';
import IntegrationsConfigSection from '@/components/configuracoes/IntegrationsConfigSection';
import { useToast, ToastContainer } from '@/components/ui/Toast';

interface ConfigStatus {
  outlook_graph: {
    is_configured: boolean;
    message: string;
    required_env_vars?: string[];
  };
}

type ConfigTab = 'integracoes' | 'sistema' | 'seguranca' | 'notificacoes';

export default function ConfiguracoesPage() {
  const [configStatus, setConfigStatus] = useState<ConfigStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<ConfigTab>('integracoes');
  const { toasts, removeToast, success, error } = useToast();

  useEffect(() => {
    loadConfigStatus();
  }, []);

  const loadConfigStatus = async () => {
    try {
      setIsLoading(true);

      // Microsoft Graph API
      const graphResponse = await fetch('http://localhost:8001/api/v1/outlook/config/status');
      const graphData = await graphResponse.json();

      setConfigStatus({
        outlook_graph: graphData
      });
    } catch (error) {
      console.error('Erro ao carregar status das configurações:', error);
      setConfigStatus({
        outlook_graph: { is_configured: false, message: 'Erro ao carregar configuração' }
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600">Carregando configurações...</p>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    {
      id: 'integracoes' as ConfigTab,
      name: 'Integrações',
      icon: Zap,
      description: 'Configure integrações externas'
    },
    {
      id: 'sistema' as ConfigTab,
      name: 'Sistema',
      icon: Server,
      description: 'Configurações gerais do sistema'
    },
    {
      id: 'seguranca' as ConfigTab,
      name: 'Segurança',
      icon: Shield,
      description: 'Políticas de segurança e acesso'
    },
    {
      id: 'notificacoes' as ConfigTab,
      name: 'Notificações',
      icon: Bell,
      description: 'Configurações de alertas e notificações'
    }
  ];

  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-50 p-4 lg:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Cabeçalho */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Settings className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">Configurações do Sistema</h1>
            </div>
            <p className="text-gray-600">
              Configure integrações, parâmetros de sistema e políticas de segurança do Amvox Omnichannel.
            </p>
          </div>

          {/* Tabs de Navegação */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8 overflow-x-auto">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className={`mr-2 h-5 w-5 ${
                        activeTab === tab.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                      }`} />
                      <div className="text-left">
                        <div>{tab.name}</div>
                        <div className="text-xs text-gray-400 hidden lg:block">{tab.description}</div>
                      </div>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Conteúdo das Tabs */}
          <div className="mt-8">
            {activeTab === 'integracoes' && (
              <IntegrationsConfigSection
                configStatus={configStatus}
                onConfigUpdate={loadConfigStatus}
                onSuccess={success}
                onError={error}
              />
            )}

            {activeTab === 'sistema' && (
              <SystemConfigSection
                onSuccess={success}
                onError={error}
              />
            )}

            {activeTab === 'seguranca' && (
              <SecurityConfigSection
                onSuccess={success}
                onError={error}
              />
            )}

            {activeTab === 'notificacoes' && (
              <div className="space-y-6">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center mb-4">
                    <Bell className="h-6 w-6 text-blue-600 mr-3" />
                    <h3 className="text-lg font-medium text-gray-900">Configurações de Notificações</h3>
                  </div>
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <FileText className="h-5 w-5 text-yellow-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-800">
                          <strong>Em Desenvolvimento:</strong> As configurações de notificações serão implementadas em uma próxima versão.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Toast Container */}
          <ToastContainer toasts={toasts} onClose={removeToast} />
        </div>
      </div>
    </AdminRoute>
  );
}
