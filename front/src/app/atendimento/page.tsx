'use client';

import React, { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import EmailSection from '@/components/atendimento/EmailSection';
import WhatsAppSection from '@/components/atendimento/WhatsAppSection';
import ThreeCXSection from '@/components/atendimento/ThreeCXSection';

export default function AtendimentoPage() {
  const [activeSection, setActiveSection] = useState<'email' | 'whatsapp' | '3cx'>('email');
  const [apiTest, setApiTest] = useState<any>(null);
  const [systemsInitialized, setSystemsInitialized] = useState(false);

  // Verificar parâmetros da URL para abrir aba específica
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');
    const outlookAuth = urlParams.get('outlook_auth');

    // Se veio do callback do Outlook, abrir aba do email
    if (tabParam === 'email' || outlookAuth === 'success') {
      setActiveSection('email');

      // Limpar parâmetros da URL após processar
      if (outlookAuth) {
        const newUrl = window.location.pathname + '?tab=email';
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, []);

  // Inicializar todos os sistemas uma única vez
  useEffect(() => {
    const initializeSystems = async () => {
      try {
        console.log('🚀 Inicializando todos os sistemas de atendimento...');

        // Teste de configuração do Outlook
        const configResponse = await fetch('http://localhost:8001/api/v1/outlook/config/status');
        const configData = await configResponse.json();
        console.log('📊 Config Outlook:', configData);

        setApiTest({
          config: configData,
          timestamp: new Date().toLocaleTimeString()
        });

        // Marcar sistemas como inicializados
        setSystemsInitialized(true);
        console.log('✅ Todos os sistemas inicializados');

      } catch (error) {
        console.error('❌ Erro ao inicializar sistemas:', error);
        setApiTest({ error: error.toString() });
        setSystemsInitialized(true); // Mesmo com erro, permitir uso
      }
    };

    initializeSystems();
  }, []);

  return (
    <ProtectedRoute>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Cabeçalho */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Central de Atendimento</h1>
            <p className="mt-2 text-gray-600">
              Gerencie todos os canais de comunicação: Email, WhatsApp e Chamadas em um só lugar.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Indicadores de status dos sistemas */}
            <div className="flex items-center space-x-3">
              {/* Status Outlook */}
              {apiTest && (
                <div className="flex items-center space-x-2 bg-blue-50 px-3 py-1 rounded-lg">
                  <div className={`w-2 h-2 rounded-full ${apiTest.error ? 'bg-red-500' : 'bg-green-500'}`}></div>
                  <span className="text-xs text-blue-700 font-medium">
                    Outlook: {apiTest.error ? 'Erro' : 'OK'}
                  </span>
                </div>
              )}

              {/* Status WhatsApp */}
              <div className="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-lg">
                <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                <span className="text-xs text-green-700 font-medium">
                  WhatsApp: Em breve
                </span>
              </div>

              {/* Status 3CX */}
              <div className="flex items-center space-x-2 bg-purple-50 px-3 py-1 rounded-lg">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="text-xs text-purple-700 font-medium">
                  3CX: Ativo
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navegação por Seções */}
      <div className="mb-8">
        <nav className="flex space-x-8 border-b border-gray-200">
          <button
            onClick={() => setActiveSection('email')}
            className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors rounded-t-lg ${
              activeSection === 'email'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span>Email - Microsoft Outlook</span>
              <div className={`w-2 h-2 rounded-full ${apiTest?.error ? 'bg-red-500' : 'bg-green-500'}`}></div>
            </div>
          </button>
          <button
            onClick={() => setActiveSection('whatsapp')}
            className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors rounded-t-lg ${
              activeSection === 'whatsapp'
                ? 'border-green-500 text-green-600 bg-green-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
              </svg>
              <span>WhatsApp Business</span>
              <span className="bg-yellow-100 text-yellow-700 text-xs rounded-full px-2 py-1 font-medium">
                Em breve
              </span>
            </div>
          </button>
          <button
            onClick={() => setActiveSection('3cx')}
            className={`py-4 px-4 border-b-2 font-medium text-sm transition-colors rounded-t-lg ${
              activeSection === '3cx'
                ? 'border-purple-500 text-purple-600 bg-purple-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              <span>3CX - Sistema de Chamadas</span>
              <span className="bg-green-100 text-green-700 text-xs rounded-full px-2 py-1 font-medium">
                Ativo
              </span>
            </div>
          </button>
        </nav>
      </div>

      {/* Conteúdo das Seções - Todos os componentes são renderizados mas apenas o ativo é visível */}
      <div className="mt-8">
        {!systemsInitialized ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Inicializando sistemas de atendimento...</p>
          </div>
        ) : (
          <>
            {/* Email - Outlook */}
            <div className={`${activeSection === 'email' ? 'block' : 'hidden'}`}>
              <EmailSection />
            </div>

            {/* WhatsApp */}
            <div className={`${activeSection === 'whatsapp' ? 'block' : 'hidden'}`}>
              <WhatsAppSection />
            </div>

            {/* 3CX - Chamadas */}
            <div className={`${activeSection === '3cx' ? 'block' : 'hidden'}`}>
              <ThreeCXSection />
            </div>
          </>
        )}
      </div>
      </div>
    </ProtectedRoute>
  );
}
