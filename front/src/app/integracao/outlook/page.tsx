'use client';

import React, { useState, useEffect } from 'react';
import { OutlookIntegrationCard } from '@/components/integrations/OutlookIntegrationCard';
import { OutlookEmailList } from '@/components/integrations/OutlookEmailList';
import { OutlookSendEmail } from '@/components/integrations/OutlookSendEmail';

interface OutlookUser {
  id: string;
  display_name: string;
  email: string;
  user_principal_name: string;
  given_name?: string;
  surname?: string;
  job_title?: string;
  office_location?: string;
}

interface OutlookIntegrationStatus {
  is_connected: boolean;
  user_info?: OutlookUser;
  last_sync?: string;
  sync_status: string;
  error_message?: string;
  folders_count: number;
  messages_count: number;
}

interface OutlookConfigStatus {
  is_configured: boolean;
  message: string;
  required_env_vars: string[];
}

export default function OutlookIntegrationPage() {
  const [integrationStatus, setIntegrationStatus] = useState<OutlookIntegrationStatus | null>(null);
  const [configStatus, setConfigStatus] = useState<OutlookConfigStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'emails' | 'send'>('overview');

  // Usar apenas ID do usuário autenticado (sem fallback hardcoded)
  const userId = integrationStatus?.user_info?.id;

  useEffect(() => {
    loadConfigStatus();
  }, []);

  const loadConfigStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('http://localhost:8001/api/v1/outlook/config/status');

      if (!response.ok) {
        throw new Error('Erro ao verificar configuração da integração');
      }

      const config = await response.json();
      setConfigStatus(config);

      // Se estiver configurado, carregar status da integração
      if (config.is_configured) {
        await loadIntegrationStatus();
      }
    } catch (err) {
      console.error('Erro ao verificar configuração:', err);
      setError('Erro ao verificar configuração da integração');
    } finally {
      setIsLoading(false);
    }
  };

  const loadIntegrationStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // CORREÇÃO: Limpar localStorage antigo e usar API correta
      localStorage.removeItem('outlook_user_id');
      localStorage.removeItem('outlook_user_email');
      localStorage.removeItem('outlook_user_name');
      localStorage.removeItem('outlook_authenticated');
      localStorage.removeItem('outlook_status');

      // Fazer requisição para o endpoint correto
      const response = await fetch('/api/v1/outlook/user/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const status = await response.json();
        if (status.authenticated && status.user_info) {
          setIntegrationStatus({
            is_connected: true,
            user_info: {
              id: status.user_id.toString(), // Usar user_id do sistema
              display_name: status.user_info.name,
              email: status.user_info.email,
              user_principal_name: status.user_info.email
            },
            last_sync: null,
            sync_status: 'connected',
            folders_count: 0,
            messages_count: 0
          });
        } else {
          setIntegrationStatus({
            is_connected: false,
            user_info: null,
            last_sync: null,
            sync_status: 'disconnected',
            error_message: 'Usuário não autenticado',
            folders_count: 0,
            messages_count: 0
          });
        }
      } else {
        setIntegrationStatus({
          is_connected: false,
          user_info: null,
          last_sync: null,
          sync_status: 'error',
          error_message: 'Erro ao verificar status',
          folders_count: 0,
          messages_count: 0
        });
      }
    } catch (err) {
      console.error('Erro ao carregar status:', err);
      setError('Erro ao carregar status da integração');
      setIntegrationStatus({
        is_connected: false,
        user_info: null,
        last_sync: null,
        sync_status: 'error',
        error_message: 'Erro de conexão',
        folders_count: 0,
        messages_count: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/outlook/auth/login');

      if (!response.ok) {
        throw new Error('Erro ao iniciar autenticação');
      }

      const data = await response.json();

      // Abrir em nova aba para evitar problemas de popup
      const authWindow = window.open(
        data.auth_url,
        'outlook-auth',
        'width=600,height=700,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
      );

      if (!authWindow) {
        // Se popup foi bloqueado, usar redirecionamento
        window.location.href = data.auth_url;
        return;
      }

      // Monitorar a janela de autenticação
      const checkAuth = setInterval(async () => {
        try {
          // Verificar se a janela ainda está aberta
          if (authWindow.closed) {
            clearInterval(checkAuth);

            // Aguardar um pouco e verificar se a autenticação foi bem-sucedida
            setTimeout(async () => {
              await loadIntegrationStatus();
            }, 2000);
            return;
          }

          // Tentar verificar se chegou na página de callback (pode dar erro de CORS)
          try {
            const url = authWindow.location.href;
            if (url.includes('localhost:8001/api/v1/outlook/auth/callback')) {
              clearInterval(checkAuth);
              authWindow.close();
              setTimeout(async () => {
                await loadIntegrationStatus();
              }, 2000);
            }
          } catch (e) {
            // Erro de CORS é esperado, continuar monitorando
          }
        } catch (error) {
          console.log('Erro ao monitorar janela:', error);
        }
      }, 1000);

      // Timeout de segurança (5 minutos)
      setTimeout(() => {
        clearInterval(checkAuth);
        if (authWindow && !authWindow.closed) {
          authWindow.close();
        }
        loadIntegrationStatus();
      }, 300000);

    } catch (err) {
      console.error('Erro ao conectar:', err);
      setError('Erro ao conectar com Outlook');
    }
  };

  const handleDisconnect = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/v1/outlook/disconnect?user_id=${userId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Erro ao desconectar');
      }

      await loadIntegrationStatus();
    } catch (err) {
      console.error('Erro ao desconectar:', err);
      setError('Erro ao desconectar do Outlook');
    }
  };

  const handleSync = async () => {
    try {
      const response = await fetch(`http://localhost:8001/api/v1/outlook/sync?user_id=${userId}`, {
        method: 'POST'
      });

      if (!response.ok) {
        throw new Error('Erro ao sincronizar');
      }

      await loadIntegrationStatus();
    } catch (err) {
      console.error('Erro ao sincronizar:', err);
      setError('Erro ao sincronizar dados');
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600">Carregando integração Outlook...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Cabeçalho */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Integração Microsoft Outlook</h1>
        <p className="mt-1 text-sm text-gray-500">
          Conecte e sincronize seus emails do Microsoft Outlook com o sistema Amvox.
        </p>
      </div>

      {/* Mensagem de erro */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Card de Status da Configuração */}
      {configStatus && !configStatus.is_configured && (
        <div className="mb-8 bg-yellow-50 border border-yellow-200 rounded-md p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Configuração Necessária
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p className="mb-3">{configStatus.message}</p>
                <p className="font-medium mb-2">Variáveis de ambiente necessárias:</p>
                <ul className="list-disc list-inside space-y-1">
                  {configStatus.required_env_vars.map((envVar) => (
                    <li key={envVar} className="font-mono text-xs">{envVar}</li>
                  ))}
                </ul>
                <div className="mt-4 p-3 bg-yellow-100 rounded-md">
                  <p className="text-xs font-medium mb-2">Como configurar:</p>
                  <ol className="text-xs space-y-1 list-decimal list-inside">
                    <li>Acesse o <a href="https://portal.azure.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 underline">Azure Portal</a></li>
                    <li>Vá para "Azure Active Directory" → "App registrations"</li>
                    <li>Clique em "New registration"</li>
                    <li>Configure o nome e redirect URI</li>
                    <li>Copie o Application (client) ID e Directory (tenant) ID</li>
                    <li>Vá para "Certificates & secrets" e crie um client secret</li>
                    <li>Configure as variáveis de ambiente no arquivo .env</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Card de Status da Integração */}
      {configStatus?.is_configured && (
        <div className="mb-8">
          <OutlookIntegrationCard
            status={integrationStatus}
            onConnect={handleConnect}
            onDisconnect={handleDisconnect}
            onSync={handleSync}
          />
        </div>
      )}

      {/* Tabs de Navegação */}
      {configStatus?.is_configured && integrationStatus?.is_connected && (
        <>
          <div className="mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Visão Geral
              </button>
              <button
                onClick={() => setActiveTab('emails')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'emails'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Emails
              </button>
              <button
                onClick={() => setActiveTab('send')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'send'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Enviar Email
              </button>
            </nav>
          </div>

          {/* Conteúdo das Tabs */}
          <div className="mt-6">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Estatísticas</h3>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">
                      Pastas: <span className="font-medium">{integrationStatus.folders_count}</span>
                    </p>
                    <p className="text-sm text-gray-600">
                      Mensagens: <span className="font-medium">{integrationStatus.messages_count}</span>
                    </p>
                    <p className="text-sm text-gray-600">
                      Status: <span className="font-medium capitalize">{integrationStatus.sync_status}</span>
                    </p>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Última Sincronização</h3>
                  <p className="text-sm text-gray-600">
                    {integrationStatus.last_sync
                      ? new Date(integrationStatus.last_sync).toLocaleString('pt-BR')
                      : 'Nunca sincronizado'
                    }
                  </p>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Usuário Conectado</h3>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">
                      {integrationStatus.user_info?.display_name}
                    </p>
                    <p className="text-sm text-gray-600">
                      {integrationStatus.user_info?.email}
                    </p>
                    {integrationStatus.user_info?.job_title && (
                      <p className="text-sm text-gray-600">
                        {integrationStatus.user_info.job_title}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'emails' && (
              <OutlookEmailList userId={userId} />
            )}

            {activeTab === 'send' && (
              <OutlookSendEmail userId={userId} />
            )}
          </div>
        </>
      )}
    </div>
  );
}
