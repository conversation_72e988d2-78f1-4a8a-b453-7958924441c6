'use client';

import React, { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import StatCard from '@/components/dashboard/StatCard';
import RecentActivities from '@/components/dashboard/RecentActivities';
import EmailWidget from '@/components/dashboard/EmailWidget';
import MetricsChart from '@/components/dashboard/MetricsChart';

// Interfaces para tipagem
interface DashboardStats {
  statistics: {
    threecx: {
      total_calls: number;
      active_calls: number;
      answered_calls: number;
      missed_calls: number;
      answer_rate: number;
    };
    whatsapp: {
      total_chats: number;
      active_chats: number;
      waiting_chats: number;
      total_unread_messages: number;
    };
    total_active_interactions: number;
    total_pending: number;
  };
  recent_activity: {
    calls: any[];
    whatsapp_chats: any[];
  };
  agent_status: any[];
}

interface SystemHealth {
  backend: boolean;
  database: boolean;
  redis: boolean;
  integrations: {
    outlook: boolean;
    threecx: boolean;
    whatsapp: boolean;
  };
}

export default function DashboardPage() {
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Função para buscar dados do dashboard
  const fetchDashboardData = async () => {
    try {
      console.log('🔄 Buscando dados do dashboard...');
      const response = await fetch('http://localhost:8001/api/v1/mock/unified/dashboard');
      console.log('📡 Resposta da API:', response.status, response.ok);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Dados recebidos:', data);
        setDashboardData(data);
      } else {
        console.error('❌ Erro na resposta da API:', response.status);
      }
    } catch (error) {
      console.error('❌ Erro ao buscar dados do dashboard:', error);
    }
  };

  // Função para verificar saúde do sistema
  const checkSystemHealth = async () => {
    try {
      const [backendHealth, mockHealth] = await Promise.all([
        fetch('http://localhost:8001/health'),
        fetch('http://localhost:8001/api/v1/mock/health')
      ]);

      setSystemHealth({
        backend: backendHealth.ok,
        database: backendHealth.ok, // Assumindo que se o backend está ok, o DB também está
        redis: backendHealth.ok,
        integrations: {
          outlook: true, // Baseado na configuração
          threecx: true,
          whatsapp: mockHealth.ok
        }
      });
    } catch (error) {
      console.error('Erro ao verificar saúde do sistema:', error);
      setSystemHealth({
        backend: false,
        database: false,
        redis: false,
        integrations: {
          outlook: false,
          threecx: false,
          whatsapp: false
        }
      });
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchDashboardData(),
        checkSystemHealth()
      ]);
      setLoading(false);
      setLastUpdate(new Date());
    };

    loadData();

    // Atualizar dados a cada 30 segundos
    const interval = setInterval(() => {
      fetchDashboardData();
      checkSystemHealth();
      setLastUpdate(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Função para formatar tempo
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Carregando dashboard...</span>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Cabeçalho */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard Omnichannel</h1>
              <p className="mt-2 text-gray-600">
                Visão geral em tempo real do sistema Amvox Omnichannel
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                Última atualização: {formatTime(lastUpdate)}
              </div>
              <button
                onClick={() => {
                  fetchDashboardData();
                  checkSystemHealth();
                  setLastUpdate(new Date());
                }}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Atualizar
              </button>
            </div>
          </div>
        </div>

        {/* Métricas Principais */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Interações Ativas"
            value={dashboardData?.statistics.total_active_interactions || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            }
            change={{
              value: "+12%",
              type: "increase"
            }}
          />

          <StatCard
            title="Chamadas Ativas"
            value={dashboardData?.statistics.threecx.active_calls || 0}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            }
            change={{
              value: `${dashboardData?.statistics.threecx.answer_rate.toFixed(1) || 0}%`,
              type: "increase"
            }}
          />

          <StatCard
            title="WhatsApp Aguardando"
            value={dashboardData?.statistics.whatsapp.waiting_chats || 0}
            icon={
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
              </svg>
            }
            change={{
              value: `${dashboardData?.statistics.whatsapp.total_unread_messages || 0} não lidas`,
              type: "increase"
            }}
          />

          <StatCard
            title="Taxa de Resposta"
            value={`${dashboardData?.statistics.threecx.answer_rate.toFixed(1) || 0}%`}
            icon={
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
            change={{
              value: "+5.2%",
              type: "increase"
            }}
          />
        </div>

        {/* Layout Principal */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Coluna Principal - Gráficos e Status */}
          <div className="lg:col-span-2 space-y-8">
            {/* Status dos Agentes */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Status dos Agentes</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {dashboardData?.agent_status.map((agent, index) => (
                  <div key={agent.id} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{agent.name}</p>
                        <p className="text-sm text-gray-500">Ramal {agent.extension}</p>
                      </div>
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full mr-2 ${
                          agent.status === 'available' ? 'bg-green-400' :
                          agent.status === 'on_call' ? 'bg-blue-400' :
                          agent.status === 'busy' ? 'bg-yellow-400' :
                          'bg-gray-400'
                        }`}></div>
                        <span className={`text-sm font-medium ${
                          agent.status === 'available' ? 'text-green-600' :
                          agent.status === 'on_call' ? 'text-blue-600' :
                          agent.status === 'busy' ? 'text-yellow-600' :
                          'text-gray-600'
                        }`}>
                          {agent.status === 'available' ? 'Disponível' :
                           agent.status === 'on_call' ? 'Em Chamada' :
                           agent.status === 'busy' ? 'Ocupado' :
                           'Ausente'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Gráfico de Métricas */}
            {dashboardData && (
              <MetricsChart
                data={{
                  threecx: dashboardData.statistics.threecx,
                  whatsapp: dashboardData.statistics.whatsapp
                }}
              />
            )}

            {/* Status dos Serviços */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Status dos Serviços</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className={`p-4 rounded-lg border-2 ${
                  systemHealth?.backend ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        systemHealth?.backend ? 'bg-green-400' : 'bg-red-400'
                      }`}></div>
                      <div>
                        <p className="font-medium text-gray-900">Backend API</p>
                        <p className="text-sm text-gray-600">FastAPI + PostgreSQL</p>
                      </div>
                    </div>
                    <span className={`text-sm font-medium ${
                      systemHealth?.backend ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {systemHealth?.backend ? 'Online' : 'Offline'}
                    </span>
                  </div>
                </div>

                <div className={`p-4 rounded-lg border-2 ${
                  systemHealth?.integrations.outlook ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        systemHealth?.integrations.outlook ? 'bg-green-400' : 'bg-yellow-400'
                      }`}></div>
                      <div>
                        <p className="font-medium text-gray-900">Microsoft Graph</p>
                        <p className="text-sm text-gray-600">Integração Email</p>
                      </div>
                    </div>
                    <span className={`text-sm font-medium ${
                      systemHealth?.integrations.outlook ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {systemHealth?.integrations.outlook ? 'Configurado' : 'Pendente'}
                    </span>
                  </div>
                </div>

                <div className={`p-4 rounded-lg border-2 ${
                  systemHealth?.integrations.threecx ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        systemHealth?.integrations.threecx ? 'bg-green-400' : 'bg-yellow-400'
                      }`}></div>
                      <div>
                        <p className="font-medium text-gray-900">3CX Telefonia</p>
                        <p className="text-sm text-gray-600">Sistema de Chamadas</p>
                      </div>
                    </div>
                    <span className={`text-sm font-medium ${
                      systemHealth?.integrations.threecx ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {systemHealth?.integrations.threecx ? 'Operacional' : 'Configurando'}
                    </span>
                  </div>
                </div>

                <div className={`p-4 rounded-lg border-2 ${
                  systemHealth?.integrations.whatsapp ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        systemHealth?.integrations.whatsapp ? 'bg-blue-400' : 'bg-gray-400'
                      }`}></div>
                      <div>
                        <p className="font-medium text-gray-900">WhatsApp Business</p>
                        <p className="text-sm text-gray-600">Mock Ativo</p>
                      </div>
                    </div>
                    <span className={`text-sm font-medium ${
                      systemHealth?.integrations.whatsapp ? 'text-blue-600' : 'text-gray-600'
                    }`}>
                      {systemHealth?.integrations.whatsapp ? 'Demo' : 'Inativo'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Coluna Lateral - Atividades e Widgets */}
          <div className="space-y-8">
            {/* Atividades Recentes */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Atividades Recentes</h3>
              <div className="space-y-4">
                {dashboardData?.recent_activity.calls.slice(0, 3).map((call, index) => (
                  <div key={call.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-gray-900">{call.caller_name}</p>
                      <p className="text-xs text-gray-500">
                        {call.direction === 'inbound' ? 'Chamada recebida' : 'Chamada realizada'} • {call.agent_name}
                      </p>
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(call.start_time).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                    </div>
                  </div>
                ))}

                {dashboardData?.recent_activity.whatsapp_chats.slice(0, 2).map((chat, index) => (
                  <div key={chat.id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0">
                      <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                      </svg>
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-gray-900">{chat.contact_name}</p>
                      <p className="text-xs text-gray-500">
                        {chat.unread_count} mensagem{chat.unread_count > 1 ? 's' : ''} não lida{chat.unread_count > 1 ? 's' : ''}
                      </p>
                    </div>
                    <div className="flex items-center">
                      {chat.unread_count > 0 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          {chat.unread_count}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <a
                  href="/atendimento"
                  className="text-sm font-medium text-blue-600 hover:text-blue-500"
                >
                  Ver todas as atividades →
                </a>
              </div>
            </div>

            {/* Widget de Email */}
            <EmailWidget />

            {/* Ações Rápidas */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Ações Rápidas</h3>
              <div className="space-y-3">
                <a
                  href="/atendimento"
                  className="flex items-center w-full p-3 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200 transition-colors"
                >
                  <svg className="w-5 h-5 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <div>
                    <p className="font-medium">Workspace Unificado</p>
                    <p className="text-xs text-gray-500">Atender todos os canais</p>
                  </div>
                </a>

                <a
                  href="/email"
                  className="flex items-center w-full p-3 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200 transition-colors"
                >
                  <svg className="w-5 h-5 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <div>
                    <p className="font-medium">Gerenciar Emails</p>
                    <p className="text-xs text-gray-500">Outlook integrado</p>
                  </div>
                </a>

                <a
                  href="/relatorios"
                  className="flex items-center w-full p-3 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200 transition-colors"
                >
                  <svg className="w-5 h-5 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <div>
                    <p className="font-medium">Relatórios</p>
                    <p className="text-xs text-gray-500">Análises e métricas</p>
                  </div>
                </a>

                <a
                  href="/configuracoes"
                  className="flex items-center w-full p-3 text-left text-sm text-gray-700 hover:bg-gray-50 rounded-lg border border-gray-200 transition-colors"
                >
                  <svg className="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <div>
                    <p className="font-medium">Configurações</p>
                    <p className="text-xs text-gray-500">Sistema e integrações</p>
                  </div>
                </a>
              </div>
            </div>

            {/* Notificações em Tempo Real */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Notificações</h3>
              <div className="space-y-3">
                <div className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium text-blue-900">Sistema Operacional</p>
                    <p className="text-xs text-blue-700">Todos os serviços funcionando normalmente</p>
                  </div>
                </div>

                {dashboardData?.statistics.total_pending > 0 && (
                  <div className="flex items-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex-shrink-0">
                      <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-yellow-900">Atendimentos Pendentes</p>
                      <p className="text-xs text-yellow-700">
                        {dashboardData?.statistics.total_pending} interação{dashboardData?.statistics.total_pending > 1 ? 'ões' : ''} aguardando resposta
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}