@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Removido o tema escuro para manter sempre o tema claro */
/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

html {
  background-color: #ffffff !important;
}

body {
  background: #ffffff !important;
  background-color: #ffffff !important;
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Garantir que todos os elementos principais tenham background branco */
#__next {
  background-color: #ffffff !important;
}

main {
  background-color: #ffffff !important;
}

/* Garantir que o container principal tenha background branco */
.max-w-7xl {
  background-color: transparent !important;
}

/* Forçar background branco no root do Next.js */
#__next > * {
  background-color: transparent !important;
}
