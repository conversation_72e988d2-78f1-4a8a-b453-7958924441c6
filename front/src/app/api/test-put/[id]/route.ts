import { NextRequest, NextResponse } from 'next/server';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🔄 TEST PUT: Recebendo requisição PUT para ID:', params.id);
    
    const body = await request.json();
    console.log('🔄 TEST PUT: Body recebido:', body);
    
    return NextResponse.json({
      message: `PUT funcionando para ID: ${params.id}`,
      method: 'PUT',
      body: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ TEST PUT: Erro:', error);
    return NextResponse.json(
      { error: 'Erro no teste PUT' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return NextResponse.json({
    message: `GET funcionando para ID: ${params.id}`,
    method: 'GET',
    timestamp: new Date().toISOString()
  });
}
