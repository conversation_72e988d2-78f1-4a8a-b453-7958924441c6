import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição POST /reports/satisfaction-survey');
    
    // Obter dados do corpo da requisição
    const body = await request.json();
    
    console.log('🔄 API Route: Dados do relatório:', body);
    
    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/reports/satisfaction-survey';
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Relatório de pesquisa de satisfação obtido com sucesso');
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      return NextResponse.json(
        { detail: 'Erro ao obter relatório de pesquisa de satisfação' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
