import { NextRequest, NextResponse } from 'next/server';

export async function PUT(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição PUT /usuarios/update');

    // Obter ID do usuário dos parâmetros de query
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return NextResponse.json(
        { detail: 'ID do usuário é obrigatório' },
        { status: 400 }
      );
    }

    // Obter token de autorização
    const authorization = request.headers.get('authorization');

    if (!authorization) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // Obter dados do corpo da requisição
    const body = await request.json();

    console.log('🔄 API Route: Dados de atualização:', body.nome, body.login);

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/usuarios/${userId}`;

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'PUT',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log('🔄 API Route: Resposta do backend:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Route: Usuário atualizado com sucesso');
      return NextResponse.json(data);
    } else {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        const errorText = await response.text();
        console.error('❌ API Route: Erro do backend (texto):', errorText);
        errorData = { detail: errorText || 'Erro desconhecido' };
      }
      console.error('❌ API Route: Erro do backend:', errorData);
      return NextResponse.json(errorData, { status: response.status });
    }
  } catch (error) {
    console.error('❌ API Route: Erro interno:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição DELETE /usuarios/update');

    // Obter ID do usuário dos parâmetros de query
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return NextResponse.json(
        { detail: 'ID do usuário é obrigatório' },
        { status: 400 }
      );
    }

    // Obter token de autorização
    const authorization = request.headers.get('authorization');

    if (!authorization) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/usuarios/${userId}`;

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'DELETE',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      console.log('🔄 API Route: Usuário excluído com sucesso');
      return NextResponse.json({ message: 'Usuário excluído com sucesso' });
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      return NextResponse.json(
        { detail: 'Erro ao excluir usuário' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
