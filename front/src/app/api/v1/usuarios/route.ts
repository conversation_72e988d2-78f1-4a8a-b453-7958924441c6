import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição GET /usuarios');
    
    // Obter token de autorização
    const authorization = request.headers.get('authorization');
    
    if (!authorization) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }
    
    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/usuarios';
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Usuários carregados:', data.length);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      return NextResponse.json(
        { detail: 'Erro ao carregar usuários' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição POST /usuarios');
    
    // Obter token de autorização
    const authorization = request.headers.get('authorization');
    
    if (!authorization) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }
    
    // Obter dados do corpo da requisição
    const body = await request.json();
    
    console.log('🔄 API Route: Dados do usuário:', body.nome, body.login);
    
    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/usuarios';
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Usuário criado com sucesso');
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      return NextResponse.json(
        { detail: 'Erro ao criar usuário' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
