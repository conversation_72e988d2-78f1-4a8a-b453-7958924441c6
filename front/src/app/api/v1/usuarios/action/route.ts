import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição POST /usuarios/action');

    // Obter dados do corpo da requisição
    const body = await request.json();
    const { action, userId, ...userData } = body;

    console.log('🔄 API Route: Action:', action, 'UserID:', userId);

    if (!action || !userId) {
      return NextResponse.json(
        { detail: 'Action e userId são obrigatórios' },
        { status: 400 }
      );
    }

    // Obter token de autorização
    const authorization = request.headers.get('authorization');

    if (!authorization) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/usuarios/${userId}`;

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    let response;

    if (action === 'update') {
      // Fazer requisição PUT para o backend
      response = await fetch(backendUrl, {
        method: 'PUT',
        headers: {
          'Authorization': authorization,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });
    } else if (action === 'delete') {
      // Fazer requisição DELETE para o backend
      response = await fetch(backendUrl, {
        method: 'DELETE',
        headers: {
          'Authorization': authorization,
          'Content-Type': 'application/json',
        },
      });
    } else {
      return NextResponse.json(
        { detail: 'Action inválida. Use "update" ou "delete"' },
        { status: 400 }
      );
    }

    console.log('🔄 API Route: Resposta do backend:', response.status);

    if (response.ok) {
      if (action === 'update') {
        const data = await response.json();
        console.log('✅ API Route: Usuário atualizado com sucesso');
        return NextResponse.json(data);
      } else {
        console.log('✅ API Route: Usuário excluído com sucesso');
        return NextResponse.json({ message: 'Usuário excluído com sucesso' });
      }
    } else {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        const errorText = await response.text();
        console.error('❌ API Route: Erro do backend (texto):', errorText);
        errorData = { detail: errorText || 'Erro desconhecido' };
      }
      console.error('❌ API Route: Erro do backend:', errorData);
      return NextResponse.json(errorData, { status: response.status });
    }
  } catch (error) {
    console.error('❌ API Route: Erro interno:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
