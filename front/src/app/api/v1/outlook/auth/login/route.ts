import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição GET /outlook/auth/login');
    
    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/outlook/auth/login';
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: URL de autenticação obtida com sucesso');
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      
      return NextResponse.json(
        { detail: 'Erro ao obter URL de autenticação do Outlook' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
