import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Callback: Recebendo callback do Microsoft OAuth');

    // Obter parâmetros da URL
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const error_description = searchParams.get('error_description');

    console.log('🔄 Callback: Parâmetros recebidos:', {
      code: code ? 'Presente' : 'Ausente',
      state,
      error,
      error_description
    });

    // Se houve erro na autenticação
    if (error) {
      console.log('❌ Callback: Erro na autenticação:', error, error_description);

      const errorHtml = `
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><PERSON>rro na Autenticação</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 20px;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .container {
                    background: white;
                    border-radius: 12px;
                    padding: 2rem;
                    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                    text-align: center;
                    max-width: 400px;
                    width: 90%;
                }
                .error-icon {
                    width: 60px;
                    height: 60px;
                    background: #EF4444;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                }
                .x-mark {
                    width: 30px;
                    height: 30px;
                    stroke: white;
                    stroke-width: 3;
                    fill: none;
                }
                h1 {
                    color: #1F2937;
                    margin-bottom: 0.5rem;
                    font-size: 1.5rem;
                }
                .error-details {
                    background: #FEF2F2;
                    padding: 1rem;
                    border-radius: 8px;
                    margin: 1rem 0;
                    text-align: left;
                }
                .error-details p {
                    margin: 0.25rem 0;
                    color: #991B1B;
                    font-size: 0.9rem;
                }
                .back-button {
                    background: #3B82F6;
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 1rem;
                    margin-top: 1rem;
                }
                .back-button:hover {
                    background: #2563EB;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="error-icon">
                    <svg class="x-mark" viewBox="0 0 24 24">
                        <path d="M6 6l12 12M6 18L18 6"/>
                    </svg>
                </div>
                <h1>Erro na Autenticação</h1>
                <p>Ocorreu um erro durante a autenticação com o Microsoft Outlook.</p>

                <div class="error-details">
                    <p><strong>Erro:</strong> ${error}</p>
                    ${error_description ? `<p><strong>Descrição:</strong> ${error_description}</p>` : ''}
                </div>

                <button class="back-button" onclick="window.location.href='/configuracoes'">
                    Voltar às Configurações
                </button>
            </div>
        </body>
        </html>
      `;

      return new Response(errorHtml, {
        status: 400,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      });
    }

    // Se não há código de autorização
    if (!code) {
      console.log('❌ Callback: Código de autorização não encontrado');
      return NextResponse.json(
        { error: 'Código de autorização não encontrado' },
        { status: 400 }
      );
    }

    // Redirecionar para o backend para processar o callback
    const backendCallbackUrl = `http://backend:8001/api/v1/outlook/auth/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state || '')}`;

    console.log('🔄 Callback: Redirecionando para backend:', backendCallbackUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendCallbackUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('🔄 Callback: Resposta do backend:', response.status);

    if (response.ok) {
      // Se o backend retornou HTML, retornar diretamente
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('text/html')) {
        const html = await response.text();
        return new Response(html, {
          status: 200,
          headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });
      } else {
        // Se retornou JSON, processar
        const data = await response.json();
        return NextResponse.json(data);
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Callback: Erro do backend:', errorText);

      return NextResponse.json(
        { error: 'Erro ao processar callback' },
        { status: response.status }
      );
    }

  } catch (error) {
    console.error('❌ Callback: Erro:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
