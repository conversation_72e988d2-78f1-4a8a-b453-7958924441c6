import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição para salvar configuração Microsoft Graph');

    // Obter dados do corpo da requisição
    const body = await request.json();
    const { client_id, client_secret, tenant_id } = body;

    console.log('🔄 API Route: Client ID:', client_id);
    console.log('🔄 API Route: Tenant ID:', tenant_id);

    // Validações básicas
    if (!client_id || !client_secret || !tenant_id) {
      return NextResponse.json(
        { detail: 'Client ID, Client Secret e Tenant ID são obrigatórios' },
        { status: 400 }
      );
    }

    // Validar formato UUID para client_id e tenant_id
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(client_id)) {
      return NextResponse.json(
        { detail: 'Client ID deve estar no formato UUID válido' },
        { status: 400 }
      );
    }

    if (!uuidRegex.test(tenant_id)) {
      return NextResponse.json(
        { detail: 'Tenant ID deve estar no formato UUID válido' },
        { status: 400 }
      );
    }

    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/outlook/graph-config';

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Obter token de autorização do cabeçalho
    const authHeader = request.headers.get('authorization');

    console.log('🔄 API Route: Authorization header:', authHeader ? 'Presente' : 'Ausente');
    console.log('🔄 API Route: Headers recebidos:', Object.fromEntries(request.headers.entries()));

    if (!authHeader) {
      console.log('❌ API Route: Token de autorização não encontrado');
      return NextResponse.json(
        { detail: 'Token de autorização não encontrado' },
        { status: 401 }
      );
    }

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader && { 'Authorization': authHeader }),
      },
      body: JSON.stringify({
        client_id: client_id,
        client_secret: client_secret,
        tenant_id: tenant_id,
      }),
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Configuração Microsoft Graph salva com sucesso');
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { detail: 'Erro ao salvar configuração Microsoft Graph' };
      }

      return NextResponse.json(
        errorData,
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição para verificar configuração Microsoft Graph');

    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/outlook/graph-config';

    // Obter token de autorização do cabeçalho
    const authHeader = request.headers.get('authorization');

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader && { 'Authorization': authHeader }),
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { detail: 'Erro ao verificar configuração Microsoft Graph' };
      }

      return NextResponse.json(
        errorData,
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição para remover configuração Microsoft Graph');

    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/outlook/graph-config';

    // Obter token de autorização do cabeçalho
    const authHeader = request.headers.get('authorization');

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader && { 'Authorization': authHeader }),
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { detail: 'Erro ao remover configuração Microsoft Graph' };
      }

      return NextResponse.json(
        errorData,
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
