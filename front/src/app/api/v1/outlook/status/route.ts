import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição GET /outlook/status');
    
    // Obter parâmetros da URL
    const { searchParams } = new URL(request.url);
    const user_id = searchParams.get('user_id');
    
    console.log('🔄 API Route: Parâmetros:', { user_id });
    
    if (!user_id) {
      return NextResponse.json(
        { detail: 'user_id é obrigatório' },
        { status: 400 }
      );
    }
    
    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/status?user_id=${user_id}`;
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Status obtido com sucesso:', data);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      
      return NextResponse.json(
        { detail: 'Erro ao obter status do Outlook' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
