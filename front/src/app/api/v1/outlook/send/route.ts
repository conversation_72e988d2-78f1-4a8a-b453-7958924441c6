import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Enviando email via Outlook');

    // Obter token de autorização do header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // Obter parâmetros da URL
    const { searchParams } = new URL(request.url);
    const user_id = searchParams.get('user_id');
    
    if (!user_id) {
      return NextResponse.json(
        { detail: 'user_id é obrigatório' },
        { status: 400 }
      );
    }

    // Obter dados do corpo da requisição
    const emailData = await request.json();
    console.log('📧 API Route: Dados do email:', emailData);

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/send?user_id=${user_id}`;
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify(emailData),
    });

    const data = await response.json();
    console.log('📧 API Route: Resposta do backend:', data);

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('❌ API Route: Erro ao enviar email:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
