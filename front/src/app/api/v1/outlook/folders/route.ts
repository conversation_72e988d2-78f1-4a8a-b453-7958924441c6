import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição GET /outlook/folders');

    // Obter token de autorização do header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // Obter parâmetros da URL
    const { searchParams } = new URL(request.url);
    const user_id = searchParams.get('user_id');

    console.log('🔄 API Route: Parâmetros:', { user_id });

    if (!user_id) {
      return NextResponse.json(
        { detail: 'user_id é obrigatório' },
        { status: 400 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/folders?user_id=${user_id}`;

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Pastas obtidas com sucesso:', data.length || 0);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);

      // Se for erro de autenticação, retornar status correto
      if (response.status === 500 && errorText.includes('não autenticado')) {
        return NextResponse.json(
          { detail: 'Usuário não autenticado no Microsoft Graph. Faça login primeiro.' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { detail: 'Erro ao obter pastas do Outlook' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
