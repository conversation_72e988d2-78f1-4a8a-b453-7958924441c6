import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Iniciando login com nova conta Outlook');

    // URL do backend com parâmetro para forçar seleção de conta
    const backendUrl = `http://backend:8001/api/v1/outlook/auth/login?force_account_selection=true`;

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Route: URL de autorização gerada:', data);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('❌ API Route: Erro do backend:', errorText);

      return NextResponse.json(
        { error: 'Erro ao gerar URL de autorização' },
        { status: response.status }
      );
    }

  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
