import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Rota de teste funcionando',
    method: 'GET',
    timestamp: new Date().toISOString()
  });
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição DELETE /outlook/messages/delete');

    // Obter parâmetros da URL
    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const user_id = searchParams.get('user_id');

    console.log('🔄 API Route: Parâmetros:', { messageId, user_id });

    if (!messageId) {
      return NextResponse.json(
        { detail: 'messageId é obrigatório' },
        { status: 400 }
      );
    }

    if (!user_id) {
      return NextResponse.json(
        { detail: 'user_id é obrigatório' },
        { status: 400 }
      );
    }

    // Obter token de autorização do header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/messages/${messageId}?user_id=${user_id}`;

    console.log('🔄 API Route: Fazendo requisição DELETE para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Email excluído com sucesso:', data);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);

      // Se for erro de autenticação, retornar status correto
      if (response.status === 401) {
        return NextResponse.json(
          { detail: 'Usuário não autenticado no Microsoft Graph. Faça login primeiro.' },
          { status: 401 }
        );
      }

      return NextResponse.json(
        { detail: 'Erro ao excluir email' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
