import { NextRequest, NextResponse } from 'next/server';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    console.log('🔄 API Route: Recebendo requisição DELETE /outlook/messages/' + params.messageId);

    // Obter parâmetros da URL
    const { searchParams } = new URL(request.url);
    const user_id = searchParams.get('user_id');

    console.log('🔄 API Route: Parâmetros:', { messageId: params.messageId, user_id });

    if (!user_id) {
      return NextResponse.json(
        { detail: 'user_id é obrigatório' },
        { status: 400 }
      );
    }

    // Obter token de autorização
    const authorization = request.headers.get('authorization');

    if (!authorization) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/messages/${params.messageId}?user_id=${user_id}`;

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'DELETE',
      headers: {
        'Authorization': authorization,
        'Content-Type': 'application/json',
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Dados recebidos:', data);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.error('🔄 API Route: Erro do backend:', errorText);
      
      try {
        const errorData = JSON.parse(errorText);
        return NextResponse.json(errorData, { status: response.status });
      } catch {
        return NextResponse.json(
          { detail: errorText || 'Erro interno do servidor' },
          { status: response.status }
        );
      }
    }

  } catch (error) {
    console.error('🔄 API Route: Erro na requisição:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
