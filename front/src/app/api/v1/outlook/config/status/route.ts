import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Fazer proxy para o backend
    const backendUrl = 'http://backend:8001/api/v1/outlook/config/status';
    
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json(data);
    } else {
      const errorData = await response.text();
      console.error('Backend error:', errorData);
      return NextResponse.json(
        { error: 'Failed to get Outlook config status' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('Outlook config status error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check Outlook configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
