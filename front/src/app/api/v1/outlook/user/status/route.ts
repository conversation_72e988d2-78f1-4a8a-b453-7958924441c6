import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Verificando status do usuário Outlook');

    // Obter token de autorização do header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/user/status`;
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
    });

    console.log('🔄 API Route: Resposta do backend:', response.status);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Route: Status obtido:', data);
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('❌ API Route: Erro do backend:', errorText);
      
      return NextResponse.json(
        { error: 'Erro ao verificar status' },
        { status: response.status }
      );
    }

  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
