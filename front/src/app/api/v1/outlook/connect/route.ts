import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Conectando ao Outlook');

    // Obter token de autorização do header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }

    // URL do backend
    const backendUrl = `http://backend:8001/api/v1/outlook/connect`;
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
    });

    const data = await response.json();
    console.log('📧 API Route: Resposta do backend:', data);

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('❌ API Route: Erro ao conectar Outlook:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
