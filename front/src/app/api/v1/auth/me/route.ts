import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição GET /auth/me');
    
    // Obter o token de autorização do header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ API Route: Token de autorização não encontrado');
      return NextResponse.json(
        { detail: 'Token de autorização necessário' },
        { status: 401 }
      );
    }
    
    const token = authHeader.substring(7); // Remove "Bearer "
    
    console.log('🔄 API Route: Token encontrado, verificando com backend...');
    
    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/auth/me';
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Authorization': `<PERSON><PERSON> ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      const userData = await response.json();
      console.log('✅ API Route: Dados do usuário obtidos com sucesso:', userData.login);
      return NextResponse.json(userData);
    } else {
      const errorText = await response.text();
      console.log('❌ API Route: Erro do backend:', errorText);
      
      return NextResponse.json(
        { detail: 'Token inválido ou expirado' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
