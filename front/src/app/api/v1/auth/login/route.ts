import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição de login');

    // Obter dados do corpo da requisição
    const body = await request.text();
    console.log('🔄 API Route: Body recebido:', body);

    // Parse dos dados URL-encoded
    const params = new URLSearchParams(body);
    const username = params.get('username') as string;
    const password = params.get('password') as string;

    console.log('🔄 API Route: Username:', username);

    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/auth/login';

    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);

    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        username: username,
        password: password,
      }),
    });

    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);

    if (response.ok) {
      const data = await response.json();
      console.log('🔄 API Route: Login bem-sucedido');
      return NextResponse.json(data);
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      return NextResponse.json(
        { detail: 'Credenciais inválidas' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
