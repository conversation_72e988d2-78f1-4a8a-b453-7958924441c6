import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 API Route: Recebendo requisição POST /pdf-reports/dashboard');
    
    // Obter dados do corpo da requisição
    const body = await request.json();
    
    console.log('🔄 API Route: Dados do relatório PDF dashboard:', body);
    
    // URL do backend
    const backendUrl = 'http://backend:8001/api/v1/pdf-reports/dashboard';
    
    console.log('🔄 API Route: Fazendo requisição para:', backendUrl);
    
    // Fazer requisição para o backend
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });
    
    console.log('🔄 API Route: Resposta do backend:', response.status, response.statusText);
    
    if (response.ok) {
      // Para PDFs, retornar o stream diretamente
      const pdfBuffer = await response.arrayBuffer();
      
      console.log('🔄 API Route: PDF de dashboard gerado com sucesso');
      
      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': response.headers.get('Content-Disposition') || 'attachment; filename=dashboard_executivo.pdf',
        },
      });
    } else {
      const errorText = await response.text();
      console.log('🔄 API Route: Erro do backend:', errorText);
      return NextResponse.json(
        { detail: 'Erro ao gerar PDF de dashboard' },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error('❌ API Route: Erro:', error);
    return NextResponse.json(
      { detail: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
