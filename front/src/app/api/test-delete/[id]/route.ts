import { NextRequest, NextResponse } from 'next/server';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return NextResponse.json({
    message: `DELETE funcionando para ID: ${params.id}`,
    method: 'DELETE',
    timestamp: new Date().toISOString()
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return NextResponse.json({
    message: `GET funcionando para ID: ${params.id}`,
    method: 'GET',
    timestamp: new Date().toISOString()
  });
}
