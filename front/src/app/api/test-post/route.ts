import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  console.log('🔄 TEST POST: Recebendo requisição POST');
  
  return NextResponse.json({
    message: 'POST funcionando!',
    method: 'POST',
    timestamp: new Date().toISOString()
  });
}

export async function GET(request: NextRequest) {
  console.log('🔄 TEST GET: Recebendo requisição GET');
  
  return NextResponse.json({
    message: 'GET funcionando!',
    method: 'GET',
    timestamp: new Date().toISOString()
  });
}
