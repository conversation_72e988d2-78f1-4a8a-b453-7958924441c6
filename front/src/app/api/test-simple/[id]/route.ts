import { NextRequest, NextResponse } from 'next/server';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return NextResponse.json({
    message: `PUT funcionando para ID: ${params.id}`,
    method: 'PUT',
    timestamp: new Date().toISOString()
  });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return NextResponse.json({
    message: `DELETE funcionando para ID: ${params.id}`,
    method: 'DELETE',
    timestamp: new Date().toISOString()
  });
}
