'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from "next/link";
import Image from "next/image";

// Interfaces para dados do dashboard
interface DashboardStats {
  statistics: {
    threecx: {
      total_calls: number;
      active_calls: number;
      answered_calls: number;
      missed_calls: number;
      answer_rate: number;
    };
    whatsapp: {
      total_chats: number;
      active_chats: number;
      waiting_chats: number;
      total_unread_messages: number;
    };
    total_active_interactions: number;
    total_pending: number;
  };
  recent_activity: {
    calls: any[];
    whatsapp_chats: any[];
  };
  agent_status: any[];
}

interface SystemHealth {
  backend: boolean;
  database: boolean;
  redis: boolean;
  integrations: {
    outlook: boolean;
    threecx: boolean;
    whatsapp: boolean;
  };
}

export default function Home() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Função para buscar dados do dashboard
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/mock/unified/dashboard');
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      }
    } catch (error) {
      console.error('Erro ao buscar dados do dashboard:', error);
    }
  };

  // Função para verificar saúde do sistema
  const checkSystemHealth = async () => {
    try {
      const [backendHealth, mockHealth] = await Promise.all([
        fetch('http://localhost:8001/health'),
        fetch('http://localhost:8001/api/v1/mock/health')
      ]);

      setSystemHealth({
        backend: backendHealth.ok,
        database: backendHealth.ok,
        redis: backendHealth.ok,
        integrations: {
          outlook: true,
          threecx: true,
          whatsapp: mockHealth.ok
        }
      });
    } catch (error) {
      console.error('Erro ao verificar saúde do sistema:', error);
      setSystemHealth({
        backend: false,
        database: false,
        redis: false,
        integrations: {
          outlook: false,
          threecx: false,
          whatsapp: false
        }
      });
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([
        fetchDashboardData(),
        checkSystemHealth()
      ]);
      setLoading(false);
      setLastUpdate(new Date());
    };

    loadData();

    // Atualizar dados a cada 30 segundos
    const interval = setInterval(() => {
      fetchDashboardData();
      checkSystemHealth();
      setLastUpdate(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Função para formatar tempo
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Se estiver carregando autenticação ou dados, mostrar loading
  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <p className="text-gray-600">Carregando sistema...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header completamente removido */}

      {/* Conteúdo Principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Seção de Boas-vindas e Ações Rápidas */}
        {isAuthenticated && user ? (
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bem-vindo, {user.nome}!
                  </h2>
                  <p className="text-gray-600 mt-1">
                    Aqui está um resumo das atividades do sistema Amvox Omnichannel
                  </p>
                </div>
                <div className="flex space-x-3">
                  <Link
                    href="/atendimento"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    Iniciar Atendimento
                  </Link>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Acesso ao Sistema</h2>
              <p className="text-gray-600 mb-6">Faça login para acessar o sistema e todas as funcionalidades</p>
              <Link
                href="/login"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                Fazer Login
              </Link>
            </div>
          </div>
        )}

        {/* Métricas removidas conforme solicitado */}

        {/* Layout principal removido conforme solicitado */}
      </div>
    </div>
  );
}
