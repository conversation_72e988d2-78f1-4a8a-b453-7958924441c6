'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

function CallbackContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [userInfo, setUserInfo] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          setStatus('error');
          setMessage(`Erro na autenticação: ${errorDescription || error}`);
          return;
        }

        if (!code) {
          setStatus('error');
          setMessage('Código de autorização não encontrado');
          return;
        }

        // Enviar código para o backend processar via endpoint correto
        const response = await fetch(`http://localhost:8001/api/v1/outlook/auth/callback?code=${code}`, {
          method: 'GET',
        });

        if (!response.ok) {
          const errorData = await response.text();
          throw new Error(`Erro ao processar autenticação: ${errorData}`);
        }

        const result = await response.json();

        if (result.success && result.user) {
          setStatus('success');
          setMessage('Autenticação realizada com sucesso!');
          setUserInfo(result.user);

          // Salvar informações no localStorage para uso posterior
          localStorage.setItem('outlook_user_id', result.user.id);
          localStorage.setItem('outlook_user_email', result.user.email);
          localStorage.setItem('outlook_authenticated', 'true');

          // Redirecionar após 3 segundos
          setTimeout(() => {
            router.push('/integracao/outlook');
          }, 3000);
        } else {
          setStatus('error');
          setMessage('Falha na autenticação');
        }

      } catch (error) {
        console.error('Erro no callback:', error);
        setStatus('error');
        setMessage('Erro interno na autenticação');
      }
    };

    handleCallback();
  }, [searchParams, router]);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {status === 'loading' && (
              <>
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <h2 className="text-lg font-medium text-gray-900 mb-2">
                  Processando autenticação...
                </h2>
                <p className="text-sm text-gray-600">
                  Aguarde enquanto validamos suas credenciais.
                </p>
              </>
            )}

            {status === 'success' && (
              <>
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                  <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-lg font-medium text-green-900 mb-2">
                  Autenticação Realizada!
                </h2>
                <p className="text-sm text-gray-600 mb-4">
                  {message}
                </p>
                {userInfo && (
                  <div className="bg-gray-50 rounded-md p-4 mb-4">
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-900">
                        Usuário: {userInfo.display_name}
                      </p>
                      <p className="text-sm text-gray-600">
                        Email: {userInfo.email}
                      </p>
                      <p className="text-sm text-gray-500">
                        ID: {userInfo.id}
                      </p>
                    </div>
                  </div>
                )}
                <p className="text-xs text-gray-500">
                  Redirecionando em alguns segundos...
                </p>
              </>
            )}

            {status === 'error' && (
              <>
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <h2 className="text-lg font-medium text-red-900 mb-2">
                  Erro na Autenticação
                </h2>
                <p className="text-sm text-gray-600 mb-4">
                  {message}
                </p>
                <button
                  onClick={() => router.push('/integracao/outlook')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  Tentar Novamente
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function OutlookCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <h2 className="text-lg font-medium text-gray-900 mb-2">
                Carregando...
              </h2>
            </div>
          </div>
        </div>
      </div>
    }>
      <CallbackContent />
    </Suspense>
  );
}
