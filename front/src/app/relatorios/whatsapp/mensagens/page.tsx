import React from 'react';
import Link from 'next/link';

export default function WhatsAppMensagensPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900"><PERSON><PERSON><PERSON><PERSON> de Mensagens WhatsApp</h1>
            <p className="mt-2 text-lg text-gray-600">
              Volume, tipos e padrões de mensagens do WhatsApp Business
            </p>
          </div>
          <Link
            href="/relatorios/whatsapp/dashboard"
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            ← Dashboard WhatsApp
          </Link>
        </div>
      </div>

      {/* Mensagem de Desenvolvimento */}
      <div className="bg-white rounded-lg shadow-md p-8">
        <div className="text-center">
          <svg className="mx-auto h-16 w-16 text-green-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
          </svg>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Em desenvolvimento</h3>
          <p className="text-gray-600 mb-6">Esta página está sendo desenvolvida e estará disponível em breve.</p>

          <div className="flex justify-center space-x-4">
            <Link
              href="/relatorios/whatsapp/dashboard"
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              ← Dashboard WhatsApp
            </Link>
            <Link
              href="/relatorios"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Todos os Relatórios
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
