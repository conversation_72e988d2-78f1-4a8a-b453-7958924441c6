import React from 'react';
import Link from 'next/link';

export default function WhatsAppTempoRespostaPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tempo de Resposta WhatsApp</h1>
            <p className="mt-2 text-lg text-gray-600">
              Métricas de velocidade de atendimento
            </p>
          </div>
          <Link
            href="/relatorios/whatsapp/dashboard"
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            ← Dashboard WhatsApp
          </Link>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Em desenvolvimento</h3>
        <p className="text-gray-600">Esta página está sendo desenvolvida e estará disponível em breve.</p>
      </div>
    </div>
  );
}
