'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import DateRangeFilter from '@/components/reports/DateRangeFilter';
import ReportCard from '@/components/reports/ReportCard';
import ChartContainer from '@/components/reports/ChartContainer';
import { CallsByHour<PERSON>hart, CallsByWeekdayChart, SatisfactionChart, AgentComparisonChart } from '@/components/charts';
import { formatDateToString, getDistributionReport, getSatisfactionSurveyReport } from '@/services/api';


export default function DashboardPage() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<string>(formatDateToString(new Date()));
  const [endDate, setEndDate] = useState<string>(formatDateToString(new Date()));
  const [selectedQueues, setSelectedQueues] = useState<number[]>([802]);
  const [distributionData, setDistributionData] = useState<any>(null);
  const [satisfactionData, setSatisfactionData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [exportingPDF, setExportingPDF] = useState(false);
  const [isLoadingNewData, setIsLoadingNewData] = useState(false);
  const currentRequestIdRef = useRef<number>(0);
  const cacheRef = useRef<Map<string, any>>(new Map());



  // Função para carregar os dados
  const loadData = async () => {
    // Gerar chave de cache baseada nos filtros
    const cacheKey = `${startDate}-${endDate}-${selectedQueues.join(',')}`;

    // Verificar se já temos dados em cache
    const cachedData = cacheRef.current.get(cacheKey);
    if (cachedData) {
      console.log('Usando dados do cache para:', cacheKey);
      setDistributionData(cachedData.distribution);
      setSatisfactionData(cachedData.satisfaction);
      setIsLoading(false);
      setIsLoadingNewData(false);
      return;
    }

    // Gerar um ID único para esta requisição
    const requestId = Date.now();
    currentRequestIdRef.current = requestId;

    setIsLoading(true);
    setIsLoadingNewData(true);
    setError(null);

    // Limpar dados antigos IMEDIATAMENTE e forçar re-render
    setDistributionData(null);
    setSatisfactionData(null);

    try {
      // Aguardar um frame para garantir que a limpeza foi renderizada
      await new Promise(resolve => requestAnimationFrame(resolve));

      console.log('Carregando dados da API para:', cacheKey);

      // Carregar dados de distribuição e satisfação em paralelo
      const [distributionResponse, satisfactionResponse] = await Promise.all([
        getDistributionReport({
          start_date: startDate,
          end_date: endDate,
          queues: selectedQueues
        }),
        getSatisfactionSurveyReport({
          start_date: startDate,
          end_date: endDate,
          queues: selectedQueues
        })
      ]);

      // Definir os novos dados apenas se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setDistributionData(distributionResponse.data);
        setSatisfactionData(satisfactionResponse.data);

        // Armazenar no cache
        cacheRef.current.set(cacheKey, {
          distribution: distributionResponse.data,
          satisfaction: satisfactionResponse.data,
          timestamp: Date.now()
        });

        // Limitar cache a 10 entradas (remover mais antigas)
        if (cacheRef.current.size > 10) {
          const firstKey = cacheRef.current.keys().next().value;
          cacheRef.current.delete(firstKey);
        }
      }
    } catch (err) {
      console.error('Erro ao carregar dados:', err);
      // Só mostrar erro se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setError('Ocorreu um erro ao carregar os dados. Por favor, tente novamente.');
        setDistributionData(null);
        setSatisfactionData(null);
      }
    } finally {
      // Só atualizar loading se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setIsLoading(false);
        setIsLoadingNewData(false);
      }
    }
  };

  // Carregar dados quando os filtros mudarem
  useEffect(() => {
    loadData();
  }, []);

  // Função para lidar com a mudança de filtros
  const handleFilterChange = (newStartDate: string, newEndDate: string) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    loadData();
  };

  // Função para lidar com a mudança de filas selecionadas
  const handleQueuesChange = (queues: number[]) => {
    setSelectedQueues(queues);
    loadData();
  };

  // Função para exportar PDF
  const handleExportPDF = async () => {
    setExportingPDF(true);
    try {
      const requestData = {
        start_date: startDate,
        end_date: endDate,
        queues: selectedQueues
      };

      console.log('Enviando dados para PDF dashboard:', requestData);

      const response = await fetch('http://localhost:8001/api/v1/pdf-reports/dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Resposta recebida dashboard:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Erro na resposta:', response.status, errorText);
        throw new Error(`Erro ao gerar relatório PDF: ${response.status} - ${errorText}`);
      }

      const blob = await response.blob();
      console.log('Blob recebido dashboard:', blob.size, 'bytes');

      if (blob.size === 0) {
        throw new Error('PDF vazio recebido do servidor');
      }

      // Verificar se é realmente um PDF
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const header = String.fromCharCode(...uint8Array.slice(0, 4));

      if (header !== '%PDF') {
        console.error('Conteúdo recebido não é um PDF válido:', header);
        throw new Error('Arquivo recebido não é um PDF válido');
      }

      // Criar novo blob com tipo correto
      const pdfBlob = new Blob([arrayBuffer], { type: 'application/pdf' });

      const url = window.URL.createObjectURL(pdfBlob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `dashboard_executivo_${startDate}_${endDate}.pdf`;
      document.body.appendChild(a);
      a.click();

      // Aguardar um pouco antes de limpar
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }, 100);

      console.log('PDF dashboard baixado com sucesso');
    } catch (error) {
      console.error('Erro ao exportar PDF:', error);
      alert(`Erro ao exportar relatório em PDF: ${error.message}`);
    } finally {
      setExportingPDF(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard 3CX</h1>
          <p className="mt-1 text-sm text-gray-500">
            Visão geral das métricas do sistema telefônico 3CX.
          </p>

        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleExportPDF}
            disabled={exportingPDF || isLoading || !distributionData || !satisfactionData}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {exportingPDF ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Gerando PDF...
              </>
            ) : (
              <>
                <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Exportar PDF
              </>
            )}
          </button>
        </div>
      </div>

      {/* Filtros */}
      <DateRangeFilter
        onFilterChange={handleFilterChange}
        onQueuesChange={handleQueuesChange}
        defaultQueues={selectedQueues}
      />

      {isLoading || isLoadingNewData ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600">
              {isLoadingNewData ? 'Carregando novos dados...' : 'Carregando...'}
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-red-500 text-center">
            {error}
          </div>
        </div>
      ) : distributionData && satisfactionData ? (
        <>


          {/* Seção de métricas de chamadas */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Métricas de Chamadas</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <ReportCard
                title="Total de Chamadas"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].sumario &&
                  distributionData["relatorio de distribuição"].sumario["Total de chamadas"] ?
                  distributionData["relatorio de distribuição"].sumario["Total de chamadas"]["Número de chamadas conectadas"] : 0}
                className="xl:col-span-1"
              />
              <ReportCard
                title="Chamadas Atendidas"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].sumario &&
                  distributionData["relatorio de distribuição"].sumario["Total de chamadas"] ?
                  distributionData["relatorio de distribuição"].sumario["Total de chamadas"]["Número de chamadas atendidas"] : 0}
                className="xl:col-span-1"
              />
              <ReportCard
                title="Chamadas Perdidas"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].sumario &&
                  distributionData["relatorio de distribuição"].sumario["Total de chamadas"] ?
                  distributionData["relatorio de distribuição"].sumario["Total de chamadas"]["Número de chamadas não-atendidas pela PA"] : 0}
                className="xl:col-span-1"
              />
              <ReportCard
                title="Tempo Médio de Espera"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0] ?
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0]["Espera Média"] : "00:00:00"}
                className="xl:col-span-1"
              />
              <ReportCard
                title="Tempo Médio de Conversa"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0] ?
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0]["Duração Média"] : "00:00:00"}
                className="xl:col-span-1"
              />
              <ReportCard
                title="Nível de Serviço"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0] ?
                  `${distributionData["relatorio de distribuição"].chamadas_por_filas[0]["Nível de serviço"]}%` : "0%"}
                className="xl:col-span-1"
              />
            </div>
          </div>

          {/* Seção de métricas de satisfação */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Métricas de Satisfação</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <ReportCard
                title="Total de Pesquisas"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"].sumario &&
                  satisfactionData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"] &&
                  satisfactionData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0] ?
                  satisfactionData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0]["Avaliadas"] : 0}
              />
              <ReportCard
                title="Taxa de Avaliação"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"].sumario &&
                  satisfactionData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"] &&
                  satisfactionData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0] ?
                  `${satisfactionData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0]["% Avaliadas"]}%` : "0%"}
              />
              <ReportCard
                title="Satisfeitos"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0] ?
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0]["Satisfeito"] : 0}
              />
              <ReportCard
                title="Insatisfeitos"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0] ?
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0]["Insatisfeito"] : 0}
              />
            </div>
          </div>

          {/* Seção de métricas de agentes */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Métricas de Agentes</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <ReportCard
                title="Total de Agentes"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ?
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"].length : 0}
              />
              <ReportCard
                title="Agentes com Avaliação"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ?
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"].filter(a => a.Avaliadas > 0).length : 0}
              />
              <ReportCard
                title="Melhor Avaliação"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ?
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]
                    .sort((a, b) => parseFloat(b["% Meta(2.0)"]) - parseFloat(a["% Meta(2.0)"]))[0]?.Agente || "N/A" : "N/A"}
              />
              <ReportCard
                title="Melhor Meta"
                value={satisfactionData &&
                  satisfactionData["Pesquisa Satisfação"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ?
                  `${satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]
                    .sort((a, b) => parseFloat(b["% Meta(2.0)"]) - parseFloat(a["% Meta(2.0)"]))[0]?.["% Meta(2.0)"] || "0"}%` : "0%"}
              />
            </div>
          </div>

          {/* Seção de métricas de filas */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Métricas de Filas</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <ReportCard
                title="Total de Filas"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas ?
                  distributionData["relatorio de distribuição"].chamadas_por_filas.length : 0}
              />
              <ReportCard
                title="Maior Tempo de Espera"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0] ?
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0]["Espera Máx"] : "00:00:00"}
              />
              <ReportCard
                title="Menor Tempo de Espera"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0] ?
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0]["Espera Mín"] : "00:00:00"}
              />
              <ReportCard
                title="Taxa de Atendimento"
                value={distributionData &&
                  distributionData["relatorio de distribuição"] &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas &&
                  distributionData["relatorio de distribuição"].chamadas_por_filas[0] ?
                  `${distributionData["relatorio de distribuição"].chamadas_por_filas[0]["Taxa de Atendidas"]}%` : "0%"}
              />
            </div>
          </div>

          {/* Gráficos */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartContainer
              title="Distribuição de Chamadas por Hora"
              description="Visualização da distribuição de chamadas ao longo do dia."
            >
              {(() => {
                // Verificar se os dados existem e têm a estrutura esperada
                if (distributionData &&
                    distributionData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por hora
                  const possibleKeys = [
                    "Chamadas por hora",
                    "chamadas_por_hora",
                    "Chamadas por Hora",
                    "chamadas por hora"
                  ];

                  for (const key of possibleKeys) {
                    const hourData = distributionData["relatorio de distribuição"][key];
                    if (hourData && Array.isArray(hourData)) {
                      const formattedData = hourData.map(item => ({
                        hour: item.Hora,
                        total: item.Recebidas || item.Total || 0,
                        answered: item.Atendidas || 0,
                        missed: item["Não-Atendidas"] || item["Perdidas"] || 0
                      }));

                      return <CallsByHourChart data={formattedData} height={250} />;
                    }
                  }
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>

            <ChartContainer
              title="Distribuição por Dia da Semana"
              description="Visualização da distribuição de chamadas por dia da semana."
            >
              {(() => {
                // Verificar se os dados existem e têm a estrutura esperada
                if (distributionData &&
                    distributionData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por dia da semana
                  const possibleKeys = [
                    "Chamadas por dia da semana",
                    "chamadas_por_dia_da_semana",
                    "Chamadas por Dia da Semana",
                    "chamadas por dia da semana"
                  ];

                  for (const key of possibleKeys) {
                    const weekdayData = distributionData["relatorio de distribuição"][key];
                    if (weekdayData && Array.isArray(weekdayData)) {
                      return <CallsByWeekdayChart data={weekdayData} height={250} />;
                    }
                  }
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>

            <ChartContainer
              title="Satisfação com o Atendente"
              description="Visualização da satisfação dos clientes com o atendimento."
            >
              {satisfactionData &&
                satisfactionData["Pesquisa Satisfação"] &&
                satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ? (
                <SatisfactionChart
                  data={satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]}
                  type="atendente"
                  height={250}
                  maxAgents={5}
                />
              ) : (
                <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                </div>
              )}
            </ChartContainer>

            <ChartContainer
              title="Comparação de Agentes - Total de Chamadas"
              description="Comparação do volume de chamadas entre os agentes."
            >
              {(() => {
                if (distributionData &&
                    distributionData["relatorio de distribuição"] &&
                    distributionData["relatorio de distribuição"]["Detalhes da Distribuição"] &&
                    satisfactionData &&
                    satisfactionData["Pesquisa Satisfação"] &&
                    satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                    satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                    satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]) {

                  // Processar dados dos agentes
                  const agentMap: { [key: string]: any } = {};

                  // Contar chamadas por agente
                  distributionData["relatorio de distribuição"]["Detalhes da Distribuição"].forEach((item: any) => {
                    if (item.Agente && item.Agente !== '') {
                      if (!agentMap[item.Agente]) {
                        agentMap[item.Agente] = {
                          totalCalls: 0,
                          totalDuration: 0,
                          callsWithDuration: 0
                        };
                      }
                      agentMap[item.Agente].totalCalls++;

                      // Calcular duração
                      if (item["Tempo das chamadas"]) {
                        try {
                          const durationStr = item["Tempo das chamadas"].toString();
                          const durationParts = durationStr.split(':');
                          if (durationParts.length === 3) {
                            const seconds = parseInt(durationParts[0]) * 3600 + parseInt(durationParts[1]) * 60 + parseInt(durationParts[2]);
                            agentMap[item.Agente].totalDuration += seconds;
                            agentMap[item.Agente].callsWithDuration++;
                          }
                        } catch (error) {
                          console.error(`Erro ao processar duração para ${item.Agente}:`, error);
                        }
                      }
                    }
                  });

                  // Adicionar dados de satisfação
                  satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"].forEach((item: any) => {
                    if (agentMap[item.Agente]) {
                      agentMap[item.Agente].totalEvaluations = item.Avaliadas || 0;
                      agentMap[item.Agente].satisfiedCount = item.Satisfeito || 0;
                      agentMap[item.Agente].dissatisfiedCount = item.Insatisfeito || 0;
                    }
                  });

                  // Converter para array
                  const agentsData = Object.entries(agentMap).map(([agentName, data]) => {
                    const averageDuration = data.callsWithDuration > 0
                      ? (() => {
                          const avgSeconds = Math.round(data.totalDuration / data.callsWithDuration);
                          const hours = Math.floor(avgSeconds / 3600);
                          const minutes = Math.floor((avgSeconds % 3600) / 60);
                          const seconds = avgSeconds % 60;
                          return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                        })()
                      : '00:00:00';

                    return {
                      name: agentName,
                      data: {
                        totalCalls: data.totalCalls,
                        averageDuration,
                        totalEvaluations: data.totalEvaluations || 0,
                        satisfiedCount: data.satisfiedCount || 0,
                        dissatisfiedCount: data.dissatisfiedCount || 0
                      }
                    };
                  }).slice(0, 5); // Limitar a 5 agentes

                  return <AgentComparisonChart agents={agentsData} metric="totalCalls" height={250} />;
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>
          </div>
        </>
      ) : (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500 text-center">
            <p className="text-lg mb-2">Nenhum dado disponível</p>
            <p className="text-sm">Verifique os filtros selecionados e tente novamente.</p>
          </div>
        </div>
      )}
    </div>
  );
}
