'use client';

import React, { useState, useEffect, useRef } from 'react';
import DateRangeFilter from '@/components/reports/DateRangeFilter';
import ReportCard from '@/components/reports/ReportCard';
import DataTable from '@/components/reports/DataTable';
import ChartContainer from '@/components/reports/ChartContainer';
import DownloadPDFButton from '@/components/reports/DownloadPDFButton';
import { CallsByHourC<PERSON>, CallsByWeekdayChart, CallsByAgentChart, CallTrendChart } from '@/components/charts';
import { getDistributionReport, downloadDistributionReportPDF, formatDateToString } from '@/services/api';

export default function DistribuicaoPage() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [startDate, setStartDate] = useState<string>(formatDateToString(new Date()));
  const [endDate, setEndDate] = useState<string>(formatDateToString(new Date()));
  const [selectedQueues, setSelectedQueues] = useState<number[]>([803]);
  const [isLoadingNewData, setIsLoadingNewData] = useState(false);
  const currentRequestIdRef = useRef<number>(0);
  const cacheRef = useRef<Map<string, any>>(new Map());

  // Função para carregar os dados do relatório
  const loadReportData = async () => {
    // Gerar chave de cache baseada nos filtros
    const cacheKey = `dist-${startDate}-${endDate}-${selectedQueues.join(',')}`;

    // Verificar se já temos dados em cache
    const cachedData = cacheRef.current.get(cacheKey);
    if (cachedData) {
      console.log('Usando dados do cache para distribuição:', cacheKey);
      setReportData(cachedData.data);
      setIsLoading(false);
      setIsLoadingNewData(false);
      return;
    }

    // Gerar um ID único para esta requisição
    const requestId = Date.now();
    currentRequestIdRef.current = requestId;

    setIsLoading(true);
    setIsLoadingNewData(true);
    setError(null);

    // Limpar dados antigos IMEDIATAMENTE e forçar re-render
    setReportData(null);

    try {
      // Aguardar um frame para garantir que a limpeza foi renderizada
      await new Promise(resolve => requestAnimationFrame(resolve));

      console.log('Carregando dados da API para distribuição:', cacheKey);

      // Carregar dados
      const response = await getDistributionReport({
        start_date: startDate,
        end_date: endDate,
        queues: selectedQueues,
      });

      // Definir os novos dados apenas se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        console.log('Dados do relatório de distribuição:', JSON.stringify(response.data, null, 2));
        setReportData(response.data);

        // Armazenar no cache
        cacheRef.current.set(cacheKey, {
          data: response.data,
          timestamp: Date.now()
        });

        // Limitar cache a 10 entradas
        if (cacheRef.current.size > 10) {
          const firstKey = cacheRef.current.keys().next().value;
          cacheRef.current.delete(firstKey);
        }
      }
    } catch (err) {
      console.error('Erro ao carregar relatório:', err);
      // Só mostrar erro se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setError('Ocorreu um erro ao carregar o relatório. Por favor, tente novamente.');
        setReportData(null);
      }
    } finally {
      // Só atualizar loading se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setIsLoading(false);
        setIsLoadingNewData(false);
      }
    }
  };

  // Carregar dados quando os filtros mudarem
  useEffect(() => {
    loadReportData();
  }, []);

  // Função para lidar com a mudança de filtros
  const handleFilterChange = (newStartDate: string, newEndDate: string) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    loadReportData();
  };

  // Função para lidar com a mudança de filas selecionadas
  const handleQueuesChange = (queues: number[]) => {
    setSelectedQueues(queues);
    loadReportData();
  };

  // Função para processar o resumo por agente
  const processAgentSummary = (detailsData: any[]) => {
    if (!detailsData || !Array.isArray(detailsData)) return [];

    // Agrupar dados por agente
    const agentMap: Record<string, {
      calls: number,
      totalDuration: number,
      callsWithDuration: number
    }> = {};

    detailsData.forEach(item => {
      if (!item.Agente) return;

      // Inicializar dados do agente se não existir
      if (!agentMap[item.Agente]) {
        agentMap[item.Agente] = {
          calls: 0,
          totalDuration: 0,
          callsWithDuration: 0
        };
      }

      // Incrementar contagem de chamadas
      agentMap[item.Agente].calls++;

      // Adicionar duração se disponível
      if (item["Tempo das chamadas"]) {
        try {
          const durationStr = item["Tempo das chamadas"].toString();
          // Processar duração da chamada

          // Tenta diferentes formatos de tempo
          let seconds = 0;

          if (durationStr.includes(':')) {
            // Formato HH:MM:SS ou MM:SS
            const durationParts = durationStr.split(':');
            if (durationParts.length === 3) {
              // Formato HH:MM:SS
              seconds = parseInt(durationParts[0]) * 3600 + parseInt(durationParts[1]) * 60 + parseInt(durationParts[2]);
            } else if (durationParts.length === 2) {
              // Formato MM:SS
              seconds = parseInt(durationParts[0]) * 60 + parseInt(durationParts[1]);
            }
          } else {
            // Tenta converter diretamente para número (segundos)
            seconds = parseInt(durationStr);
          }

          if (!isNaN(seconds) && seconds >= 0) {
            agentMap[item.Agente].totalDuration += seconds;
            agentMap[item.Agente].callsWithDuration++;
            // Duração processada com sucesso
          } else {
            // Só mostrar warning se realmente não conseguiu converter
            if (durationStr && durationStr !== "00:00:00") {
              console.warn(`Não foi possível converter a duração para ${item.Agente}:`, durationStr);
            }
          }
        } catch (error) {
          console.error(`Erro ao processar duração para ${item.Agente}:`, error);
        }
      }
    });

    // Converter para array e calcular tempo médio
    return Object.entries(agentMap).map(([agente, data]) => {
      const averageDuration = data.callsWithDuration > 0
        ? Math.round(data.totalDuration / data.callsWithDuration)
        : 0;

      // Formatar tempo médio
      const hours = Math.floor(averageDuration / 3600);
      const minutes = Math.floor((averageDuration % 3600) / 60);
      const seconds = averageDuration % 60;
      const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      return {
        Agente: agente,
        Total: data.calls,
        TempoMedio: formattedTime
      };
    }).sort((a, b) => b.Total - a.Total); // Ordenar por total de chamadas (decrescente)
  };

  // Dados de exemplo para o relatório (substituir pelos dados reais da API)
  const sampleData = {
    totalCalls: 1248,
    answeredCalls: 1052,
    missedCalls: 196,
    averageWaitTime: '00:01:45',
    averageTalkTime: '00:05:32',
    callsByHour: [
      { hour: '08:00', total: 45, answered: 40, missed: 5 },
      { hour: '09:00', total: 78, answered: 70, missed: 8 },
      { hour: '10:00', total: 120, answered: 105, missed: 15 },
      { hour: '11:00', total: 135, answered: 120, missed: 15 },
      { hour: '12:00', total: 90, answered: 75, missed: 15 },
      { hour: '13:00', total: 85, answered: 70, missed: 15 },
      { hour: '14:00', total: 110, answered: 95, missed: 15 },
      { hour: '15:00', total: 125, answered: 110, missed: 15 },
      { hour: '16:00', total: 115, answered: 100, missed: 15 },
      { hour: '17:00', total: 95, answered: 82, missed: 13 },
      { hour: '18:00', total: 50, answered: 45, missed: 5 },
    ],
    callsByAgent: [
      { agent: 'Carlos Silva', total: 245, answered: 230, missed: 15, averageTalkTime: '00:04:45' },
      { agent: 'Ana Oliveira', total: 220, answered: 210, missed: 10, averageTalkTime: '00:05:12' },
      { agent: 'Roberto Santos', total: 198, answered: 185, missed: 13, averageTalkTime: '00:06:05' },
      { agent: 'Juliana Costa', total: 210, answered: 195, missed: 15, averageTalkTime: '00:05:30' },
      { agent: 'Marcos Pereira', total: 175, answered: 160, missed: 15, averageTalkTime: '00:04:55' },
    ],
  };

  // Colunas para a tabela de chamadas por hora
  const callsByHourColumns = [
    { key: 'hour', header: 'Hora' },
    { key: 'total', header: 'Total de Chamadas' },
    { key: 'answered', header: 'Atendidas' },
    { key: 'missed', header: 'Perdidas' },
    {
      key: 'answerRate',
      header: 'Taxa de Atendimento',
      render: (value: any, row: any) => `${Math.round((row.answered / row.total) * 100)}%`
    },
  ];

  // Colunas para a tabela de chamadas por agente
  const callsByAgentColumns = [
    { key: 'agent', header: 'Agente' },
    { key: 'total', header: 'Total de Chamadas' },
    { key: 'answered', header: 'Atendidas' },
    { key: 'missed', header: 'Perdidas' },
    {
      key: 'answerRate',
      header: 'Taxa de Atendimento',
      render: (value: any, row: any) => `${Math.round((row.answered / row.total) * 100)}%`
    },
    { key: 'averageTalkTime', header: 'Tempo Médio de Conversa' },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Relatório de Distribuição - 3CX</h1>
          <p className="mt-1 text-sm text-gray-500">
            Visualize a distribuição de chamadas por hora e por agente.
          </p>
        </div>
        <DownloadPDFButton
          onDownload={downloadDistributionReportPDF}
          params={{
            start_date: startDate,
            end_date: endDate,
            queues: selectedQueues
          }}
          label="Baixar Relatório PDF"
        />
      </div>

      {/* Filtros */}
      <DateRangeFilter
        onFilterChange={handleFilterChange}
        defaultQueues={selectedQueues}
        onQueuesChange={handleQueuesChange}
      />

      {isLoading || isLoadingNewData ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600">
              {isLoadingNewData ? 'Carregando novos dados...' : 'Carregando...'}
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      ) : reportData ? (
        <>
          {/* Cards de resumo */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <ReportCard
              title="Total de Chamadas"
              value={reportData ? reportData["relatorio de distribuição"].sumario["Total de chamadas"]["Número de chamadas conectadas"] : 0}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              }
            />
            <ReportCard
              title="Chamadas Atendidas"
              value={reportData ? reportData["relatorio de distribuição"].sumario["Total de chamadas"]["Número de chamadas atendidas"] : 0}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
              trend={reportData ? {
                value: reportData["relatorio de distribuição"].sumario["Total de chamadas"]["Taxa de Atendidas"] || "0%",
                isPositive: true
              } : undefined}
            />
            <ReportCard
              title="Chamadas Perdidas"
              value={reportData ? reportData["relatorio de distribuição"].sumario["Total de chamadas"]["Número de chamadas não-atendidas pela PA"] : 0}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
              trend={reportData ? {
                value: reportData["relatorio de distribuição"].sumario["Total de chamadas"]["Taxa de Nao Atendidas"] || "0%",
                isPositive: false
              } : undefined}
            />
            <ReportCard
              title="Tempo Médio de Espera"
              value={reportData && reportData["relatorio de distribuição"]["chamadas_por_filas"] && reportData["relatorio de distribuição"]["chamadas_por_filas"][0]
                ? reportData["relatorio de distribuição"]["chamadas_por_filas"][0]["Espera Média"]
                : "00:00:00"}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
            />
          </div>

          {/* Tabela de chamadas por hora */}
          <div className="mb-6">
            <DataTable
              title="Distribuição de Chamadas por Hora"
              description="Visualize a distribuição de chamadas ao longo do dia."
              columns={callsByHourColumns}
              data={(() => {
                // Verifica se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por hora
                  const possibleKeys = [
                    "Chamadas por hora",
                    "chamadas_por_hora",
                    "Chamadas por Hora",
                    "chamadas por hora"
                  ];

                  for (const key of possibleKeys) {
                    const hourData = reportData["relatorio de distribuição"][key];
                    if (hourData && Array.isArray(hourData)) {
                      console.log(`Encontrou dados por hora na chave: ${key}`);
                      return hourData.map(item => ({
                        hour: item.Hora,
                        total: item.Recebidas || item.Total || 0,
                        answered: item.Atendidas || 0,
                        missed: item["Não-Atendidas"] || item["Perdidas"] || 0
                      }));
                    }
                  }
                }

                console.warn("Dados de chamadas por hora não encontrados");
                return [];
              })()}
            />
          </div>

          {/* Tabela de chamadas por dia da semana */}
          <div className="mb-6">
            <DataTable
              title="Distribuição de Chamadas por Dia da Semana"
              description="Visualize a distribuição de chamadas ao longo da semana."
              columns={[
                { key: 'Dia', header: 'Dia' },
                { key: 'Recebidas', header: 'Total' },
                { key: 'Atendidas', header: 'Atendidas' },
                { key: 'Não-Atendidas', header: 'Perdidas' },
                { key: 'Nível de serviço', header: 'Nível de Serviço' },
                { key: 'Taxa de Atendidas', header: 'Taxa de Atendimento' }
              ]}
              data={(() => {
                // Verifica se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por dia da semana
                  const possibleKeys = [
                    "Chamadas por dia da semana",
                    "chamadas_por_dia_da_semana",
                    "Chamadas por Dia da Semana",
                    "chamadas por dia da semana"
                  ];

                  for (const key of possibleKeys) {
                    const weekdayData = reportData["relatorio de distribuição"][key];
                    if (weekdayData && Array.isArray(weekdayData)) {
                      console.log(`Encontrou dados por dia da semana na chave: ${key}`);
                      return weekdayData;
                    }
                  }
                }

                console.warn("Dados de chamadas por dia da semana não encontrados");
                return [];
              })()}
            />
          </div>

          {/* Tabela de chamadas por dia */}
          <div className="mb-6">
            <DataTable
              title="Distribuição de Chamadas por Dia"
              description="Visualize a distribuição de chamadas por dia."
              columns={[
                { key: 'Data', header: 'Data' },
                { key: 'Recebidas', header: 'Total' },
                { key: 'Atendidas', header: 'Atendidas' },
                { key: 'Não-Atendidas', header: 'Perdidas' },
                { key: 'Nível de serviço', header: 'Nível de Serviço' },
                { key: 'Taxa de Atendidas', header: 'Taxa de Atendimento' }
              ]}
              data={(() => {
                // Verifica se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por dia
                  const possibleKeys = [
                    "Chamadas por dia",
                    "chamadas_por_dia",
                    "Chamadas por Dia",
                    "chamadas por dia"
                  ];

                  for (const key of possibleKeys) {
                    const dayData = reportData["relatorio de distribuição"][key];
                    if (dayData && Array.isArray(dayData)) {
                      console.log(`Encontrou dados por dia na chave: ${key}`);
                      return dayData;
                    }
                  }
                }

                console.warn("Dados de chamadas por dia não encontrados");
                return [];
              })()}
            />
          </div>

          {/* Tabela de resumo por agente */}
          <div className="mb-6">
            <DataTable
              title="Resumo por Agente"
              description="Visualize o resumo de chamadas por agente."
              columns={[
                { key: 'Agente', header: 'Agente' },
                { key: 'Total', header: 'Total de Chamadas' },
                { key: 'TempoMedio', header: 'Tempo Médio' },
                { key: 'Link', header: 'Detalhes', render: (value, row) => (
                  <a
                    href={`/relatorios/3cx/agentes?agente=${encodeURIComponent(row.Agente)}`}
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    Ver detalhes
                  </a>
                )}
              ]}
              data={reportData &&
                reportData["relatorio de distribuição"] &&
                reportData["relatorio de distribuição"]["Detalhes da Distribuição"] ?
                processAgentSummary(reportData["relatorio de distribuição"]["Detalhes da Distribuição"]) : []}
            />
          </div>

          {/* Tabela de detalhes da distribuição */}
          <div className="mb-6">
            <DataTable
              title="Detalhes da Distribuição"
              description="Visualize os detalhes das chamadas por agente."
              columns={[
                { key: 'Data', header: 'Data' },
                { key: 'Agente', header: 'Agente' },
                { key: 'Número telefônico', header: 'Número' },
                { key: 'Evento', header: 'Evento' },
                { key: 'Tempo das chamadas', header: 'Duração' }
              ]}
              data={reportData &&
                reportData["relatorio de distribuição"] &&
                reportData["relatorio de distribuição"]["Detalhes da Distribuição"] ?
                reportData["relatorio de distribuição"]["Detalhes da Distribuição"].slice(0, 20) : []}
            />
          </div>

          {/* Gráficos */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <ChartContainer
              title="Distribuição de Chamadas por Hora"
              description="Visualização gráfica da distribuição de chamadas ao longo do dia."
            >
              {(() => {
                // Verificar se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por hora
                  const possibleKeys = [
                    "Chamadas por hora",
                    "chamadas_por_hora",
                    "Chamadas por Hora",
                    "chamadas por hora"
                  ];

                  for (const key of possibleKeys) {
                    const hourData = reportData["relatorio de distribuição"][key];
                    if (hourData && Array.isArray(hourData)) {
                      const formattedData = hourData.map(item => ({
                        hour: item.Hora,
                        total: item.Recebidas || item.Total || 0,
                        answered: item.Atendidas || 0,
                        missed: item["Não-Atendidas"] || item["Perdidas"] || 0
                      }));

                      return <CallsByHourChart data={formattedData} height={300} />;
                    }
                  }
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>

            <ChartContainer
              title="Distribuição de Chamadas por Dia da Semana"
              description="Visualização gráfica da distribuição de chamadas ao longo da semana."
            >
              {(() => {
                // Verificar se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por dia da semana
                  const possibleKeys = [
                    "Chamadas por dia da semana",
                    "chamadas_por_dia_da_semana",
                    "Chamadas por Dia da Semana",
                    "chamadas por dia da semana"
                  ];

                  for (const key of possibleKeys) {
                    const weekdayData = reportData["relatorio de distribuição"][key];
                    if (weekdayData && Array.isArray(weekdayData)) {
                      return <CallsByWeekdayChart data={weekdayData} height={300} />;
                    }
                  }
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>

            <ChartContainer
              title="Chamadas por Agente"
              description="Visualização gráfica do volume de chamadas por agente."
            >
              {(() => {
                // Verificar se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"] &&
                    reportData["relatorio de distribuição"]["Detalhes da Distribuição"]) {

                  const agentData = processAgentSummary(reportData["relatorio de distribuição"]["Detalhes da Distribuição"]);
                  if (agentData && agentData.length > 0) {
                    return <CallsByAgentChart data={agentData} height={300} />;
                  }
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>

            <ChartContainer
              title="Tendência de Chamadas por Dia"
              description="Visualização da evolução das chamadas ao longo do período."
            >
              {(() => {
                // Verificar se os dados existem e têm a estrutura esperada
                if (reportData &&
                    reportData["relatorio de distribuição"]) {

                  // Procura pela chave correta que contém os dados por dia
                  const possibleKeys = [
                    "Chamadas por dia",
                    "chamadas_por_dia",
                    "Chamadas por Dia",
                    "chamadas por dia"
                  ];

                  for (const key of possibleKeys) {
                    const dayData = reportData["relatorio de distribuição"][key];
                    if (dayData && Array.isArray(dayData)) {
                      return <CallTrendChart data={dayData} height={300} />;
                    }
                  }
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                  </div>
                );
              })()}
            </ChartContainer>
          </div>
        </>
      ) : (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500 text-center">
            <p className="text-lg mb-2">Nenhum dado disponível</p>
            <p className="text-sm">Verifique os filtros selecionados e tente novamente.</p>
          </div>
        </div>
      )}
    </div>
  );
}
