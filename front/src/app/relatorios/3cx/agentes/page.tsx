'use client';

import React, { useState, useEffect, useRef } from 'react';
import DateRangeFilter from '@/components/reports/DateRangeFilter';
import ReportCard from '@/components/reports/ReportCard';
import DataTable from '@/components/reports/DataTable';
import ChartContainer from '@/components/reports/ChartContainer';
import DownloadPDFButton from '@/components/reports/DownloadPDFButton';
import { AgentComparisonChart } from '@/components/charts';
import { getDistributionReport, getSatisfactionSurveyReport, formatDateToString } from '@/services/api';

export default function AgentesPage() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [distributionData, setDistributionData] = useState<any>(null);
  const [satisfactionData, setSatisfactionData] = useState<any>(null);
  const [startDate, setStartDate] = useState<string>(formatDateToString(new Date()));
  const [endDate, setEndDate] = useState<string>(formatDateToString(new Date()));
  const [selectedQueues, setSelectedQueues] = useState<number[]>([803, 802]);
  const [selectedAgent, setSelectedAgent] = useState<string>('');
  const [agentsList, setAgentsList] = useState<string[]>([]);
  const [exportingPDF, setExportingPDF] = useState(false);
  const [isLoadingNewData, setIsLoadingNewData] = useState(false);
  const currentRequestIdRef = useRef<number>(0);
  const cacheRef = useRef<Map<string, any>>(new Map());

  // Função para carregar os dados
  const loadData = async () => {
    // Gerar chave de cache baseada nos filtros
    const cacheKey = `${startDate}-${endDate}-${selectedQueues.join(',')}`;

    // Verificar se já temos dados em cache
    const cachedData = cacheRef.current.get(cacheKey);
    if (cachedData) {
      console.log('Usando dados do cache para agentes:', cacheKey);
      setDistributionData(cachedData.distribution);
      setSatisfactionData(cachedData.satisfaction);
      setAgentsList(cachedData.agentsList);
      setIsLoading(false);
      setIsLoadingNewData(false);
      return;
    }

    // Gerar um ID único para esta requisição
    const requestId = Date.now();
    currentRequestIdRef.current = requestId;

    setIsLoading(true);
    setIsLoadingNewData(true);
    setError(null);

    // Limpar dados antigos IMEDIATAMENTE e forçar re-render
    setDistributionData(null);
    setSatisfactionData(null);
    setAgentsList([]);

    try {
      // Aguardar um frame para garantir que a limpeza foi renderizada
      await new Promise(resolve => requestAnimationFrame(resolve));

      console.log('Carregando dados da API para agentes:', cacheKey);

      // Carregar dados de distribuição e satisfação em paralelo
      const [distributionResponse, satisfactionResponse] = await Promise.all([
        getDistributionReport({
          start_date: startDate,
          end_date: endDate,
          queues: selectedQueues
        }),
        getSatisfactionSurveyReport({
          start_date: startDate,
          end_date: endDate,
          queues: selectedQueues
        })
      ]);

      // Definir os novos dados apenas se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setDistributionData(distributionResponse.data);
        setSatisfactionData(satisfactionResponse.data);

        // Extrair lista de agentes dos dados
        const agents = extractAgentsListFromData(distributionResponse.data, satisfactionResponse.data);
        setAgentsList(agents);

        // Armazenar no cache
        cacheRef.current.set(cacheKey, {
          distribution: distributionResponse.data,
          satisfaction: satisfactionResponse.data,
          agentsList: agents,
          timestamp: Date.now()
        });

        // Limitar cache a 10 entradas
        if (cacheRef.current.size > 10) {
          const firstKey = cacheRef.current.keys().next().value;
          cacheRef.current.delete(firstKey);
        }
      }
    } catch (err) {
      console.error('Erro ao carregar dados:', err);
      // Só mostrar erro se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setError('Ocorreu um erro ao carregar os dados. Por favor, tente novamente.');
        setDistributionData(null);
        setSatisfactionData(null);
        setAgentsList([]);
      }
    } finally {
      // Só atualizar loading se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setIsLoading(false);
        setIsLoadingNewData(false);
      }
    }
  };

  // Extrair lista de agentes dos dados (retorna array)
  const extractAgentsListFromData = (distributionData: any, satisfactionData: any): string[] => {
    const agents = new Set<string>();

    // Extrair agentes dos dados de distribuição
    if (distributionData &&
        distributionData["relatorio de distribuição"] &&
        distributionData["relatorio de distribuição"]["Detalhes da Distribuição"]) {
      distributionData["relatorio de distribuição"]["Detalhes da Distribuição"].forEach((item: any) => {
        if (item.Agente) {
          agents.add(item.Agente);
        }
      });
    }

    // Extrair agentes dos dados de satisfação
    if (satisfactionData &&
        satisfactionData["Pesquisa Satisfação"] &&
        satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
        satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
        satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]) {
      satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"].forEach((item: any) => {
        if (item.Agente) {
          agents.add(item.Agente);
        }
      });
    }

    return Array.from(agents).sort();
  };

  // Extrair lista de agentes dos dados (para compatibilidade)
  const extractAgentsList = (distributionData: any, satisfactionData: any) => {
    const agents = extractAgentsListFromData(distributionData, satisfactionData);
    setAgentsList(agents);
  };

  // Verificar se há um agente na URL
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const agente = searchParams.get('agente');
    if (agente) {
      setSelectedAgent(agente);
    }
    loadData();
  }, []);

  // Função para lidar com a mudança de filtros
  const handleFilterChange = (newStartDate: string, newEndDate: string) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    loadData();
  };

  // Função para lidar com a mudança de filas selecionadas
  const handleQueuesChange = (queues: number[]) => {
    setSelectedQueues(queues);
    loadData();
  };

  // Função para filtrar dados por agente
  const getAgentDistributionData = () => {
    if (!distributionData || !selectedAgent) return [];

    const detailsData = distributionData["relatorio de distribuição"]?.["Detalhes da Distribuição"] || [];
    return detailsData.filter((item: any) => item.Agente === selectedAgent);
  };

  // Função para obter dados de satisfação do agente
  const getAgentSatisfactionData = () => {
    if (!satisfactionData || !selectedAgent) return null;

    const agentData = {
      avaliacao: null,
      chamada: null,
      empresa: null
    };

    // Dados de avaliação do atendente
    if (satisfactionData["Pesquisa Satisfação"]?.["Pesquisa por Agente"]?.["Pesquisa Amvox"]?.["Av-1-Avalia Atendente"]) {
      agentData.avaliacao = satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]
        .find((item: any) => item.Agente === selectedAgent);
    }

    // Dados de avaliação da chamada
    if (satisfactionData["Pesquisa Satisfação"]?.["Pesquisa por Agente"]?.["Pesquisa Amvox"]?.["Av-2-Avalia Chamada"]) {
      agentData.chamada = satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-2-Avalia Chamada"]
        .find((item: any) => item.Agente === selectedAgent);
    }

    // Dados de avaliação da empresa
    if (satisfactionData["Pesquisa Satisfação"]?.["Pesquisa por Agente"]?.["Pesquisa Amvox"]?.["Av-3-Avalia Empresa"]) {
      agentData.empresa = satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-3-Avalia Empresa"]
        .find((item: any) => item.Agente === selectedAgent);
    }

    return agentData;
  };

  // Calcular métricas do agente
  const calculateAgentMetrics = () => {
    const agentDistribution = getAgentDistributionData();
    const agentSatisfaction = getAgentSatisfactionData();

    // Contar chamadas
    const totalCalls = agentDistribution.length;

    // Contar eventos
    const eventCounts: Record<string, number> = {};
    agentDistribution.forEach((item: any) => {
      const event = item.Evento || 'Desconhecido';
      eventCounts[event] = (eventCounts[event] || 0) + 1;
    });

    // Calcular tempo médio
    let totalDuration = 0;
    let callsWithDuration = 0;
    agentDistribution.forEach((item: any) => {
      if (item["Tempo das chamadas"]) {
        const durationParts = item["Tempo das chamadas"].split(':');
        if (durationParts.length === 3) {
          const seconds = parseInt(durationParts[0]) * 3600 + parseInt(durationParts[1]) * 60 + parseInt(durationParts[2]);
          totalDuration += seconds;
          callsWithDuration++;
        }
      }
    });

    const averageDuration = callsWithDuration > 0
      ? formatDuration(Math.round(totalDuration / callsWithDuration))
      : '00:00:00';

    return {
      totalCalls,
      eventCounts,
      averageDuration,
      satisfaction: agentSatisfaction
    };
  };

  // Formatar duração em segundos para HH:MM:SS
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Obter métricas do agente
  const agentMetrics = selectedAgent ? calculateAgentMetrics() : null;

  // Função para exportar PDF do agente
  const handleExportAgentPDF = async () => {
    if (!selectedAgent) {
      alert('Selecione um agente para exportar o relatório.');
      return;
    }

    setExportingPDF(true);
    try {
      const requestData = {
        agent_name: selectedAgent,
        start_date: startDate,
        end_date: endDate,
        queues: selectedQueues
      };

      console.log('Enviando dados para PDF:', requestData);

      const response = await fetch('http://localhost:8001/api/v1/pdf-reports/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('Resposta recebida:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Erro na resposta:', response.status, errorText);
        throw new Error(`Erro ao gerar relatório PDF: ${response.status} - ${errorText}`);
      }

      const blob = await response.blob();
      console.log('Blob recebido:', blob.size, 'bytes');

      if (blob.size === 0) {
        throw new Error('PDF vazio recebido do servidor');
      }

      // Verificar se é realmente um PDF
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const header = String.fromCharCode(...uint8Array.slice(0, 4));

      if (header !== '%PDF') {
        console.error('Conteúdo recebido não é um PDF válido:', header);
        throw new Error('Arquivo recebido não é um PDF válido');
      }

      // Criar novo blob com tipo correto
      const pdfBlob = new Blob([arrayBuffer], { type: 'application/pdf' });

      const url = window.URL.createObjectURL(pdfBlob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `relatorio_agente_${selectedAgent}_${startDate}_${endDate}.pdf`;
      document.body.appendChild(a);
      a.click();

      // Aguardar um pouco antes de limpar
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }, 100);

      console.log('PDF baixado com sucesso');
    } catch (error) {
      console.error('Erro ao exportar PDF:', error);
      alert(`Erro ao exportar relatório em PDF: ${error.message}`);
    } finally {
      setExportingPDF(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Relatório por Agente - 3CX</h1>
          <p className="mt-1 text-sm text-gray-500">
            Visualize métricas detalhadas por agente, combinando dados de distribuição e satisfação.
          </p>
        </div>
        {selectedAgent && (
          <button
            onClick={handleExportAgentPDF}
            disabled={exportingPDF || isLoading || !distributionData || !satisfactionData}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {exportingPDF ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Gerando PDF...
              </>
            ) : (
              <>
                <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Exportar PDF do Agente
              </>
            )}
          </button>
        )}
      </div>

      {/* Filtros */}
      <DateRangeFilter
        onFilterChange={handleFilterChange}
        defaultQueues={selectedQueues}
        onQueuesChange={handleQueuesChange}
      />

      {/* Seleção de agente */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Selecione um Agente</h3>
        <select
          value={selectedAgent}
          onChange={(e) => setSelectedAgent(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Selecione um agente</option>
          {agentsList.map((agent) => (
            <option key={agent} value={agent}>{agent}</option>
          ))}
        </select>
      </div>

      {isLoading || isLoadingNewData ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600">
              {isLoadingNewData ? 'Carregando novos dados...' : 'Carregando...'}
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      ) : !distributionData || !satisfactionData ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500 text-center">
            <p className="text-lg mb-2">Nenhum dado disponível</p>
            <p className="text-sm">Verifique os filtros selecionados e tente novamente.</p>
          </div>
        </div>
      ) : selectedAgent && agentMetrics ? (
        <>
          {/* Cards de resumo */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <ReportCard
              title="Total de Chamadas"
              value={agentMetrics.totalCalls}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
              }
            />
            <ReportCard
              title="Tempo Médio de Chamada"
              value={agentMetrics.averageDuration}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
            />
            <ReportCard
              title="Satisfação do Atendente"
              value={agentMetrics.satisfaction?.avaliacao ?
                `${agentMetrics.satisfaction.avaliacao.Satisfeito}/${agentMetrics.satisfaction.avaliacao.Avaliadas}` :
                'N/A'}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
              trend={agentMetrics.satisfaction?.avaliacao ? {
                value: `${agentMetrics.satisfaction.avaliacao['% Avaliadas']}%`,
                isPositive: true
              } : undefined}
            />
            <ReportCard
              title="Avaliação da Chamada"
              value={agentMetrics.satisfaction?.chamada ?
                `${agentMetrics.satisfaction.chamada.Sim}/${agentMetrics.satisfaction.chamada.Avaliadas}` :
                'N/A'}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
              trend={agentMetrics.satisfaction?.chamada ? {
                value: `${agentMetrics.satisfaction.chamada['% Avaliadas']}%`,
                isPositive: true
              } : undefined}
            />
          </div>

          {/* Gráfico de comparação de agentes */}
          <div className="mb-6">
            <ChartContainer
              title="Comparação de Agentes"
              description="Compare o desempenho deste agente com outros agentes."
            >
              {(() => {
                if (agentsList.length > 0 && distributionData && satisfactionData) {
                  // Preparar dados para o gráfico de comparação
                  const agentsData = agentsList.slice(0, 5).map(agentName => {
                    // Dados de distribuição para este agente
                    const agentDistribution = distributionData["relatorio de distribuição"]?.["Detalhes da Distribuição"]?.filter(
                      (item: any) => item.Agente === agentName
                    ) || [];

                    // Calcular métricas para este agente
                    const totalCalls = agentDistribution.length;

                    // Calcular tempo médio
                    let totalDuration = 0;
                    let callsWithDuration = 0;
                    agentDistribution.forEach((item: any) => {
                      if (item["Tempo das chamadas"]) {
                        try {
                          const durationStr = item["Tempo das chamadas"].toString();
                          const durationParts = durationStr.split(':');
                          if (durationParts.length === 3) {
                            const seconds = parseInt(durationParts[0]) * 3600 + parseInt(durationParts[1]) * 60 + parseInt(durationParts[2]);
                            totalDuration += seconds;
                            callsWithDuration++;
                          }
                        } catch (error) {
                          console.error(`Erro ao processar duração para ${agentName}:`, error);
                        }
                      }
                    });

                    const averageDuration = callsWithDuration > 0
                      ? formatDuration(Math.round(totalDuration / callsWithDuration))
                      : '00:00:00';

                    // Dados de satisfação para este agente
                    const agentSatisfaction = {
                      avaliacao: null,
                      chamada: null,
                      empresa: null
                    };

                    // Dados de avaliação do atendente
                    if (satisfactionData["Pesquisa Satisfação"]?.["Pesquisa por Agente"]?.["Pesquisa Amvox"]?.["Av-1-Avalia Atendente"]) {
                      agentSatisfaction.avaliacao = satisfactionData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]
                        .find((item: any) => item.Agente === agentName);
                    }

                    // Calcular taxa de satisfação
                    let satisfactionRate = 0;
                    if (agentSatisfaction.avaliacao) {
                      const totalEvaluations = agentSatisfaction.avaliacao.Avaliadas || 0;
                      const satisfiedCount = agentSatisfaction.avaliacao.Satisfeito || 0;
                      satisfactionRate = totalEvaluations > 0 ? (satisfiedCount / totalEvaluations) * 100 : 0;
                    }

                    return {
                      name: agentName,
                      data: {
                        totalCalls,
                        averageDuration,
                        totalEvaluations: agentSatisfaction.avaliacao?.Avaliadas || 0,
                        satisfiedCount: agentSatisfaction.avaliacao?.Satisfeito || 0,
                        dissatisfiedCount: agentSatisfaction.avaliacao?.Insatisfeito || 0,
                        satisfactionRate
                      }
                    };
                  });

                  return (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <AgentComparisonChart
                        agents={agentsData}
                        metric="totalCalls"
                        height={300}
                      />
                      <AgentComparisonChart
                        agents={agentsData}
                        metric="averageDuration"
                        height={300}
                      />
                      <AgentComparisonChart
                        agents={agentsData}
                        metric="satisfactionRate"
                        height={300}
                      />
                    </div>
                  );
                }

                return (
                  <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                    <p className="text-gray-500">Selecione um agente para visualizar a comparação</p>
                  </div>
                );
              })()}
            </ChartContainer>
          </div>

          {/* Detalhes de chamadas do agente */}
          <div className="mb-6">
            <DataTable
              title={`Detalhes de Chamadas - ${selectedAgent}`}
              description="Visualize os detalhes das chamadas atendidas pelo agente."
              columns={[
                { key: 'Data', header: 'Data' },
                { key: 'Número telefônico', header: 'Número' },
                { key: 'Evento', header: 'Evento' },
                { key: 'Tempo das chamadas', header: 'Duração' }
              ]}
              data={getAgentDistributionData()}
            />
          </div>
        </>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <p className="text-gray-500">Selecione um agente para visualizar suas métricas detalhadas.</p>
        </div>
      )}
    </div>
  );
}
