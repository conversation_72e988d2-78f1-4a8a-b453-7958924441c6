'use client';

import React, { useState, useEffect, useRef } from 'react';
import DateRangeFilter from '@/components/reports/DateRangeFilter';
import ReportCard from '@/components/reports/ReportCard';
import DataTable from '@/components/reports/DataTable';
import ChartContainer from '@/components/reports/ChartContainer';
import DownloadPDFButton from '@/components/reports/DownloadPDFButton';
import { SatisfactionChart, EvaluationCoverageChart, EvaluationDistributionChart } from '@/components/charts';
import { calculateEvaluationMetrics, calculateOverallEvaluationMetrics, getEvaluationStatistics } from '@/utils/evaluationMetrics';
import { getSatisfactionSurveyReport, downloadSatisfactionSurveyReportPDF, getDistributionReport, formatDateToString } from '@/services/api';

export default function SatisfacaoPage() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<any>(null);
  const [distributionData, setDistributionData] = useState<any>(null);
  const [startDate, setStartDate] = useState<string>(formatDateToString(new Date()));
  const [endDate, setEndDate] = useState<string>(formatDateToString(new Date()));
  const [selectedQueues, setSelectedQueues] = useState<number[]>([802]);
  const [isLoadingNewData, setIsLoadingNewData] = useState(false);
  const currentRequestIdRef = useRef<number>(0);
  const cacheRef = useRef<Map<string, any>>(new Map());

  // Função para carregar os dados do relatório
  const loadReportData = async () => {
    // Gerar chave de cache baseada nos filtros
    const cacheKey = `sat-${startDate}-${endDate}-${selectedQueues.join(',')}`;

    // Verificar se já temos dados em cache
    const cachedData = cacheRef.current.get(cacheKey);
    if (cachedData) {
      console.log('Usando dados do cache para satisfação:', cacheKey);
      setReportData(cachedData.satisfaction);
      setDistributionData(cachedData.distribution);
      setIsLoading(false);
      setIsLoadingNewData(false);
      return;
    }

    // Gerar um ID único para esta requisição
    const requestId = Date.now();
    currentRequestIdRef.current = requestId;

    setIsLoading(true);
    setIsLoadingNewData(true);
    setError(null);

    // Limpar dados antigos IMEDIATAMENTE e forçar re-render
    setReportData(null);
    setDistributionData(null);

    try {
      // Aguardar um frame para garantir que a limpeza foi renderizada
      await new Promise(resolve => requestAnimationFrame(resolve));

      console.log('Carregando dados da API para satisfação:', cacheKey);

      // Buscar dados de satisfação e distribuição em paralelo
      const [satisfactionResponse, distributionResponse] = await Promise.all([
        getSatisfactionSurveyReport({
          start_date: startDate,
          end_date: endDate,
          queues: selectedQueues,
        }),
        getDistributionReport({
          start_date: startDate,
          end_date: endDate,
          queues: selectedQueues,
        })
      ]);

      // Definir os novos dados apenas se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setReportData(satisfactionResponse.data);
        setDistributionData(distributionResponse.data);

        // Armazenar no cache
        cacheRef.current.set(cacheKey, {
          satisfaction: satisfactionResponse.data,
          distribution: distributionResponse.data,
          timestamp: Date.now()
        });

        // Limitar cache a 10 entradas
        if (cacheRef.current.size > 10) {
          const firstKey = cacheRef.current.keys().next().value;
          cacheRef.current.delete(firstKey);
        }
      }
    } catch (err) {
      console.error('Erro ao carregar relatório:', err);
      // Só mostrar erro se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setError('Ocorreu um erro ao carregar o relatório. Por favor, tente novamente.');
        setReportData(null);
        setDistributionData(null);
      }
    } finally {
      // Só atualizar loading se esta ainda é a requisição mais recente
      if (currentRequestIdRef.current === requestId) {
        setIsLoading(false);
        setIsLoadingNewData(false);
      }
    }
  };

  // Carregar dados quando os filtros mudarem
  useEffect(() => {
    loadReportData();
  }, []);

  // Função para lidar com a mudança de filtros
  const handleFilterChange = (newStartDate: string, newEndDate: string) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    loadReportData();
  };

  // Função para lidar com a mudança de filas selecionadas
  const handleQueuesChange = (queues: number[]) => {
    setSelectedQueues(queues);
    loadReportData();
  };

  // Dados de exemplo para o relatório (substituir pelos dados reais da API)
  const sampleData = {
    totalSurveys: 850,
    averageRating: 4.2,
    satisfactionRate: 84,
    npsScore: 68,
    ratingDistribution: [
      { rating: 1, count: 25, percentage: 3 },
      { rating: 2, count: 45, percentage: 5 },
      { rating: 3, count: 120, percentage: 14 },
      { rating: 4, count: 310, percentage: 36 },
      { rating: 5, count: 350, percentage: 42 },
    ],
    agentRatings: [
      { agent: 'Carlos Silva', surveys: 180, averageRating: 4.5, satisfactionRate: 92 },
      { agent: 'Ana Oliveira', surveys: 165, averageRating: 4.3, satisfactionRate: 88 },
      { agent: 'Roberto Santos', surveys: 150, averageRating: 4.1, satisfactionRate: 82 },
      { agent: 'Juliana Costa', surveys: 175, averageRating: 4.4, satisfactionRate: 90 },
      { agent: 'Marcos Pereira', surveys: 130, averageRating: 3.9, satisfactionRate: 78 },
    ],
    recentFeedback: [
      {
        id: 1,
        date: '2023-05-15',
        customer: 'João Silva',
        agent: 'Carlos Silva',
        rating: 5,
        comment: 'Atendimento excelente, resolveu meu problema rapidamente.'
      },
      {
        id: 2,
        date: '2023-05-15',
        customer: 'Maria Oliveira',
        agent: 'Ana Oliveira',
        rating: 4,
        comment: 'Bom atendimento, mas demorou um pouco para resolver.'
      },
      {
        id: 3,
        date: '2023-05-14',
        customer: 'Pedro Santos',
        agent: 'Roberto Santos',
        rating: 3,
        comment: 'Atendimento regular, poderia ser mais rápido.'
      },
      {
        id: 4,
        date: '2023-05-14',
        customer: 'Carla Souza',
        agent: 'Juliana Costa',
        rating: 5,
        comment: 'Excelente atendimento, muito atenciosa e eficiente.'
      },
      {
        id: 5,
        date: '2023-05-13',
        customer: 'Ricardo Ferreira',
        agent: 'Marcos Pereira',
        rating: 2,
        comment: 'Não resolveu meu problema, tive que ligar novamente.'
      },
    ],
  };

  // Colunas para a tabela de distribuição de avaliações
  const ratingDistributionColumns = [
    { key: 'rating', header: 'Avaliação' },
    { key: 'count', header: 'Quantidade' },
    {
      key: 'percentage',
      header: 'Percentual',
      render: (value: any) => `${value}%`
    },
  ];

  // Colunas para a tabela de avaliações por agente
  const agentRatingsColumns = [
    { key: 'agent', header: 'Agente' },
    { key: 'surveys', header: 'Pesquisas' },
    { key: 'averageRating', header: 'Avaliação Média' },
    {
      key: 'satisfactionRate',
      header: 'Taxa de Satisfação',
      render: (value: any) => `${value}%`
    },
  ];

  // Colunas para a tabela de feedbacks recentes
  const recentFeedbackColumns = [
    { key: 'date', header: 'Data' },
    { key: 'customer', header: 'Cliente' },
    { key: 'agent', header: 'Agente' },
    {
      key: 'rating',
      header: 'Avaliação',
      render: (value: any) => {
        const stars = '★'.repeat(value) + '☆'.repeat(5 - value);
        return (
          <span className={`text-${value >= 4 ? 'green' : value >= 3 ? 'yellow' : 'red'}-500`}>
            {stars}
          </span>
        );
      }
    },
    { key: 'comment', header: 'Comentário' },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Relatório de Pesquisa de Satisfação - 3CX</h1>
          <p className="mt-1 text-sm text-gray-500">
            Visualize os resultados das pesquisas de satisfação dos clientes.
          </p>
        </div>
        <DownloadPDFButton
          onDownload={downloadSatisfactionSurveyReportPDF}
          params={{
            start_date: startDate,
            end_date: endDate,
            queues: selectedQueues
          }}
          label="Baixar Relatório PDF"
        />
      </div>

      {/* Filtros */}
      <DateRangeFilter
        onFilterChange={handleFilterChange}
        defaultQueues={selectedQueues}
        onQueuesChange={handleQueuesChange}
      />

      {isLoading || isLoadingNewData ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-600">
              {isLoadingNewData ? 'Carregando novos dados...' : 'Carregando...'}
            </p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      ) : reportData ? (
        <>
          {/* Cards de resumo */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <ReportCard
              title="Total de Pesquisas"
              value={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"].sumario &&
                reportData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"] &&
                reportData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0] ?
                reportData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0]["Avaliadas"] : 0}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
              }
            />
            <ReportCard
              title="Taxa de Avaliação"
              value={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"].sumario &&
                reportData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"] &&
                reportData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0] ?
                `${reportData["Pesquisa Satisfação"].sumario["Pesquisas Efetuadas"][0]["% Avaliadas"]}%` : "0%"}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
              }
            />
            <ReportCard
              title="Satisfeitos"
              value={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0] ?
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0]["Satisfeito"] : 0}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              }
              trend={{ value: 'Positivo', isPositive: true }}
            />
            <ReportCard
              title="Insatisfeitos"
              value={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0] ?
                reportData["Pesquisa Satisfação"]["Pesquisa por fila"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"][0]["Insatisfeito"] : 0}
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              }
              trend={{ value: 'Negativo', isPositive: false }}
            />
          </div>

          {/* Cards de métricas de avaliação */}
          {(() => {
            if (reportData && distributionData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                distributionData["relatorio de distribuição"] &&
                distributionData["relatorio de distribuição"]["Detalhes da Distribuição"]) {

              const satisfactionData = reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"];
              const distributionDetails = distributionData["relatorio de distribuição"]["Detalhes da Distribuição"];

              const overallMetrics = calculateOverallEvaluationMetrics(distributionDetails, satisfactionData);

              return (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  <ReportCard
                    title="Total de Chamadas"
                    value={overallMetrics.totalCalls}
                    icon={
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                      </svg>
                    }
                  />
                  <ReportCard
                    title="Chamadas Avaliadas"
                    value={overallMetrics.evaluatedCalls}
                    icon={
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    }
                    trend={{ value: `${overallMetrics.evaluationRate.toFixed(1)}%`, isPositive: overallMetrics.evaluationRate >= 50 }}
                  />
                  <ReportCard
                    title="Chamadas Não Avaliadas"
                    value={overallMetrics.unevaluatedCalls}
                    icon={
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    }
                    trend={{ value: `${(100 - overallMetrics.evaluationRate).toFixed(1)}%`, isPositive: false }}
                  />
                  <ReportCard
                    title="Taxa de Cobertura"
                    value={`${overallMetrics.evaluationRate.toFixed(1)}%`}
                    icon={
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    }
                    trend={{ value: overallMetrics.evaluationRate >= 70 ? 'Boa' : overallMetrics.evaluationRate >= 50 ? 'Regular' : 'Baixa', isPositive: overallMetrics.evaluationRate >= 70 }}
                  />
                </div>
              );
            }
            return null;
          })()}

          {/* Gráficos de cobertura de avaliação */}
          {(() => {
            if (reportData && distributionData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                distributionData["relatorio de distribuição"] &&
                distributionData["relatorio de distribuição"]["Detalhes da Distribuição"]) {

              const satisfactionData = reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"];
              const distributionDetails = distributionData["relatorio de distribuição"]["Detalhes da Distribuição"];

              const evaluationMetrics = calculateEvaluationMetrics(distributionDetails, satisfactionData);
              const overallMetrics = calculateOverallEvaluationMetrics(distributionDetails, satisfactionData);

              return (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                  <ChartContainer
                    title="Distribuição Geral de Avaliações"
                    description="Visualização da proporção de chamadas avaliadas vs não avaliadas."
                  >
                    <EvaluationDistributionChart
                      totalCalls={overallMetrics.totalCalls}
                      evaluatedCalls={overallMetrics.evaluatedCalls}
                      height={300}
                    />
                  </ChartContainer>

                  <ChartContainer
                    title="Cobertura de Avaliações por Agente"
                    description="Comparação da cobertura de avaliações entre agentes."
                  >
                    <EvaluationCoverageChart
                      data={evaluationMetrics}
                      height={300}
                      maxAgents={8}
                    />
                  </ChartContainer>
                </div>
              );
            }
            return null;
          })()}

          {/* Tabela de cobertura de avaliações por agente */}
          {(() => {
            if (reportData && distributionData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] &&
                distributionData["relatorio de distribuição"] &&
                distributionData["relatorio de distribuição"]["Detalhes da Distribuição"]) {

              const satisfactionData = reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"];
              const distributionDetails = distributionData["relatorio de distribuição"]["Detalhes da Distribuição"];

              const evaluationMetrics = calculateEvaluationMetrics(distributionDetails, satisfactionData);

              return (
                <div className="mb-6">
                  <DataTable
                    title="Cobertura de Avaliações por Agente"
                    description="Visualize a cobertura de avaliações para cada agente, incluindo chamadas não avaliadas."
                    columns={[
                      { key: 'agentName', header: 'Agente' },
                      { key: 'totalCalls', header: 'Total de Chamadas' },
                      { key: 'evaluatedCalls', header: 'Chamadas Avaliadas' },
                      { key: 'unevaluatedCalls', header: 'Chamadas Não Avaliadas' },
                      {
                        key: 'evaluationRate',
                        header: 'Taxa de Cobertura',
                        render: (value: number) => (
                          <span className={`font-semibold ${
                            value >= 70 ? 'text-green-600' :
                            value >= 50 ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {value.toFixed(1)}%
                          </span>
                        )
                      },
                      {
                        key: 'agentDetails',
                        header: 'Detalhes',
                        render: (value: any, row: any) => (
                          <a
                            href={`/relatorios/3cx/agentes?agente=${encodeURIComponent(row.agentName)}`}
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            Ver detalhes
                          </a>
                        )
                      }
                    ]}
                    data={evaluationMetrics}
                  />
                </div>
              );
            }
            return null;
          })()}

          {/* Tabela de avaliações por agente */}
          <div className="mb-6">
            <DataTable
              title="Avaliações por Agente"
              description="Visualize as avaliações recebidas por cada agente."
              columns={[
                { key: 'Agente', header: 'Agente' },
                { key: 'Avaliadas', header: 'Avaliadas' },
                { key: 'Satisfeito', header: 'Satisfeito' },
                { key: 'Insatisfeito', header: 'Insatisfeito' },
                { key: '% Avaliadas', header: '% Avaliadas' },
                { key: '% Meta(2.0)', header: 'Meta' },
                { key: 'AgentDetailsLink1', header: 'Detalhes', render: (value, row) => (
                  <a
                    href={`/relatorios/3cx/agentes?agente=${encodeURIComponent(row.Agente)}`}
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    Ver detalhes
                  </a>
                )}
              ]}
              data={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ?
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] : []}
            />
          </div>

          {/* Tabela de avaliação de chamadas */}
          <div className="mb-6">
            <DataTable
              title="Avaliação de Chamadas"
              description="Visualize a avaliação das chamadas por agente."
              columns={[
                { key: 'Agente', header: 'Agente' },
                { key: 'Avaliadas', header: 'Avaliadas' },
                { key: 'Sim', header: 'Sim' },
                { key: 'Não', header: 'Não' },
                { key: '% Avaliadas', header: '% Avaliadas' },
                { key: '% Meta(2.0)', header: 'Meta' },
                { key: 'AgentDetailsLink2', header: 'Detalhes', render: (value, row) => (
                  <a
                    href={`/relatorios/3cx/agentes?agente=${encodeURIComponent(row.Agente)}`}
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    Ver detalhes
                  </a>
                )}
              ]}
              data={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-2-Avalia Chamada"] ?
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-2-Avalia Chamada"] : []}
            />
          </div>

          {/* Tabela de avaliação da empresa */}
          <div className="mb-6">
            <DataTable
              title="Avaliação da Empresa"
              description="Visualize a avaliação da empresa por agente."
              columns={[
                { key: 'Agente', header: 'Agente' },
                { key: 'Avaliadas', header: 'Avaliadas' },
                { key: 'Nota 1', header: 'Nota 1' },
                { key: 'Nota 2', header: 'Nota 2' },
                { key: 'Nota 3', header: 'Nota 3' },
                { key: 'Nota 4', header: 'Nota 4' },
                { key: 'Nota 5', header: 'Nota 5' },
                { key: '% Meta(5.0)', header: 'Meta' },
                { key: 'AgentDetailsLink3', header: 'Detalhes', render: (value, row) => (
                  <a
                    href={`/relatorios/3cx/agentes?agente=${encodeURIComponent(row.Agente)}`}
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    Ver detalhes
                  </a>
                )}
              ]}
              data={reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-3-Avalia Empresa"] ?
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-3-Avalia Empresa"] : []}
            />
          </div>

          {/* Gráficos de satisfação */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <ChartContainer
              title="Satisfação com o Atendente"
              description="Visualização gráfica da satisfação dos clientes com o atendimento."
            >
              {reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"] ? (
                <SatisfactionChart
                  data={reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-1-Avalia Atendente"]}
                  type="atendente"
                  height={300}
                />
              ) : (
                <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                </div>
              )}
            </ChartContainer>

            <ChartContainer
              title="Satisfação com a Resolução da Chamada"
              description="Visualização gráfica da satisfação dos clientes com a resolução do problema."
            >
              {reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-2-Avalia Chamada"] ? (
                <SatisfactionChart
                  data={reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-2-Avalia Chamada"]}
                  type="chamada"
                  height={300}
                />
              ) : (
                <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                </div>
              )}
            </ChartContainer>

            <ChartContainer
              title="Avaliação da Empresa"
              description="Visualização gráfica da avaliação geral da empresa pelos clientes."
            >
              {reportData &&
                reportData["Pesquisa Satisfação"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"] &&
                reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-3-Avalia Empresa"] ? (
                <SatisfactionChart
                  data={reportData["Pesquisa Satisfação"]["Pesquisa por Agente"]["Pesquisa Amvox"]["Av-3-Avalia Empresa"]}
                  type="empresa"
                  height={300}
                />
              ) : (
                <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                  <p className="text-gray-500">Dados não disponíveis para o gráfico</p>
                </div>
              )}
            </ChartContainer>
          </div>
        </>
      ) : (
        <div className="flex justify-center items-center h-64">
          <div className="text-gray-500 text-center">
            <p className="text-lg mb-2">Nenhum dado disponível</p>
            <p className="text-sm">Verifique os filtros selecionados e tente novamente.</p>
          </div>
        </div>
      )}
    </div>
  );
}
