import React from 'react';
import Link from 'next/link';

export default function RelatoriosPage() {
  // Categorias de relatórios focadas em 3CX, WhatsApp e Unificado
  const reportCategories = [
    {
      title: '3CX Telefonia',
      description: 'Relatórios completos do sistema telefônico 3CX',
      icon: (
        <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
        </svg>
      ),
      links: [
        { name: 'Dashboard 3CX', href: '/relatorios/3cx/dashboard', description: 'Visão geral das chamadas' },
        { name: 'Distribuição de Chamadas', href: '/relatorios/3cx/distribuicao', description: 'Análise de distribuição' },
        { name: 'Relatório por Agente', href: '/relatorios/3cx/agentes', description: 'Performance individual' },
        { name: 'Pesquisa de Satisfação', href: '/relatorios/3cx/satisfacao', description: 'Feedback dos clientes' },
      ],
      color: 'bg-blue-50',
      hoverColor: 'hover:bg-blue-100',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200',
    },
    {
      title: 'WhatsApp Business',
      description: 'Relatórios de atendimento via WhatsApp',
      icon: (
        <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
        </svg>
      ),
      links: [
        { name: 'Dashboard WhatsApp', href: '/relatorios/whatsapp/dashboard', description: 'Visão geral das conversas' },
        { name: 'Análise de Mensagens', href: '/relatorios/whatsapp/mensagens', description: 'Volume e tipos de mensagens' },
        { name: 'Atendimentos', href: '/relatorios/whatsapp/atendimentos', description: 'Histórico de atendimentos' },
        { name: 'Tempo de Resposta', href: '/relatorios/whatsapp/tempo-resposta', description: 'Métricas de velocidade' },
      ],
      color: 'bg-green-50',
      hoverColor: 'hover:bg-green-100',
      textColor: 'text-green-600',
      borderColor: 'border-green-200',
    },
    {
      title: 'Relatório Unificado',
      description: 'Métricas consolidadas de 3CX e WhatsApp',
      icon: (
        <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      ),
      links: [
        { name: 'Dashboard Geral', href: '/relatorios/unificado/dashboard', description: 'Visão consolidada de todos os canais' },
        { name: 'Comparativo de Canais', href: '/relatorios/unificado/comparativo', description: '3CX vs WhatsApp' },
        { name: 'Performance Global', href: '/relatorios/unificado/performance', description: 'Métricas gerais de atendimento' },
        { name: 'Relatório Executivo', href: '/relatorios/unificado/executivo', description: 'Resumo para gestão' },
      ],
      color: 'bg-purple-50',
      hoverColor: 'hover:bg-purple-100',
      textColor: 'text-purple-600',
      borderColor: 'border-purple-200',
    },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Central de Relatórios</h1>
        <p className="mt-2 text-lg text-gray-600">
          Análises detalhadas de 3CX, WhatsApp e métricas unificadas do sistema omnichannel.
        </p>
      </div>

      {/* Cards de Categorias */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {reportCategories.map((category, index) => (
          <div key={index} className={`bg-white rounded-xl shadow-lg border-2 ${category.borderColor} overflow-hidden hover:shadow-xl transition-shadow duration-300`}>
            <div className="p-6">
              {/* Header do Card */}
              <div className="flex items-center mb-6">
                <div className={`p-4 rounded-xl ${category.color} ${category.textColor} mr-4`}>
                  {category.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{category.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                </div>
              </div>

              {/* Links dos Relatórios */}
              <div className="space-y-3">
                {category.links.map((link, linkIndex) => (
                  <Link
                    key={linkIndex}
                    href={link.href}
                    className={`group block p-4 rounded-lg border-2 border-transparent ${category.color} ${category.hoverColor} transition-all duration-200 hover:border-current ${link.isNew ? 'ring-2 ring-blue-300 bg-blue-100' : ''}`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center">
                          <h4 className={`font-semibold ${category.textColor} group-hover:text-opacity-80`}>
                            {link.name}
                          </h4>
                          {link.isNew && (
                            <span className="ml-2 px-2 py-1 text-xs bg-blue-600 text-white rounded-full">
                              NOVO
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{link.description}</p>
                      </div>
                      <svg className={`w-5 h-5 ${category.textColor} group-hover:translate-x-1 transition-transform`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Seção removida conforme solicitado */}
    </div>
  );
}
