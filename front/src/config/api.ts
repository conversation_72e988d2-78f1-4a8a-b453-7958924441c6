/**
 * Configurações da API
 */

// URL base da API baseada no ambiente
// Usar rotas locais do Next.js que fazem proxy para o backend
export const API_BASE_URL = '';

// Endpoints da API
export const API_ENDPOINTS = {
  // Autenticação
  LOGIN: `${API_BASE_URL}/api/v1/auth/login`,
  LOGOUT: `${API_BASE_URL}/api/v1/auth/logout`,
  ME: `${API_BASE_URL}/api/v1/auth/me`,
  CHANGE_PASSWORD: `${API_BASE_URL}/api/v1/auth/change-password`,
  VERIFY_ADMIN: `${API_BASE_URL}/api/v1/auth/verify-admin`,

  // Usuários
  USERS: `${API_BASE_URL}/api/v1/usuarios`,

  // Outros endpoints podem ser adicionados aqui
};

// Headers padrão para requisições
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
};

// Headers para form data
export const FORM_HEADERS = {
  'Content-Type': 'application/x-www-form-urlencoded',
};

// Função para criar headers com autorização
export const createAuthHeaders = (token: string) => ({
  ...DEFAULT_HEADERS,
  'Authorization': `Bearer ${token}`,
});

// Função para fazer requisições autenticadas
export const fetchWithAuth = async (url: string, options: RequestInit = {}, token?: string) => {
  const headers = token
    ? createAuthHeaders(token)
    : DEFAULT_HEADERS;

  return fetch(url, {
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  });
};
