/**
 * Utilitários para calcular métricas de avaliação
 */

interface DistributionData {
  Agente: string;
  [key: string]: any;
}

interface SatisfactionData {
  Agente: string;
  Avaliadas: number;
  [key: string]: any;
}

interface EvaluationMetrics {
  agentName: string;
  totalCalls: number;
  evaluatedCalls: number;
  unevaluatedCalls: number;
  evaluationRate: number;
}

/**
 * Calcula as métricas de avaliação por agente
 * @param distributionData - Dados de distribuição de chamadas
 * @param satisfactionData - Dados de satisfação
 * @returns Array com métricas de avaliação por agente
 */
export function calculateEvaluationMetrics(
  distributionData: DistributionData[],
  satisfactionData: SatisfactionData[]
): EvaluationMetrics[] {
  // Criar um mapa com o total de chamadas por agente
  const agentCallsMap: { [key: string]: number } = {};

  distributionData.forEach(item => {
    if (item.Agente && item.Agente !== '') {
      agentCallsMap[item.Agente] = (agentCallsMap[item.Agente] || 0) + 1;
    }
  });

  // Criar um mapa com o total de avaliações por agente
  const agentEvaluationsMap: { [key: string]: number } = {};

  satisfactionData.forEach(item => {
    if (item.Agente && item.Agente !== '') {
      agentEvaluationsMap[item.Agente] = item.Avaliadas || 0;
    }
  });

  // Combinar os dados para calcular as métricas
  const metrics: EvaluationMetrics[] = [];
  const processedAgents = new Set<string>();

  // Incluir todos os agentes que têm chamadas
  Object.keys(agentCallsMap).forEach(agentName => {
    if (agentName && agentName.trim() !== '' && !processedAgents.has(agentName)) {
      const totalCalls = agentCallsMap[agentName];
      const evaluatedCalls = agentEvaluationsMap[agentName] || 0;
      const unevaluatedCalls = totalCalls - evaluatedCalls;
      const evaluationRate = totalCalls > 0 ? (evaluatedCalls / totalCalls) * 100 : 0;

      metrics.push({
        agentName,
        totalCalls,
        evaluatedCalls,
        unevaluatedCalls,
        evaluationRate
      });

      processedAgents.add(agentName);
    }
  });

  // Incluir agentes que têm avaliações mas não aparecem na distribuição (caso raro)
  Object.keys(agentEvaluationsMap).forEach(agentName => {
    if (agentName && agentName.trim() !== '' && !agentCallsMap[agentName] && !processedAgents.has(agentName)) {
      const evaluatedCalls = agentEvaluationsMap[agentName];

      metrics.push({
        agentName,
        totalCalls: evaluatedCalls, // Assumir que todas as chamadas foram avaliadas
        evaluatedCalls,
        unevaluatedCalls: 0,
        evaluationRate: 100
      });

      processedAgents.add(agentName);
    }
  });

  return metrics.sort((a, b) => b.totalCalls - a.totalCalls);
}

/**
 * Calcula as métricas gerais de avaliação
 * @param distributionData - Dados de distribuição de chamadas
 * @param satisfactionData - Dados de satisfação
 * @returns Métricas gerais de avaliação
 */
export function calculateOverallEvaluationMetrics(
  distributionData: DistributionData[],
  satisfactionData: SatisfactionData[]
): {
  totalCalls: number;
  evaluatedCalls: number;
  unevaluatedCalls: number;
  evaluationRate: number;
} {
  const totalCalls = distributionData.length;
  const evaluatedCalls = satisfactionData.reduce((sum, item) => sum + (item.Avaliadas || 0), 0);
  const unevaluatedCalls = totalCalls - evaluatedCalls;
  const evaluationRate = totalCalls > 0 ? (evaluatedCalls / totalCalls) * 100 : 0;

  return {
    totalCalls,
    evaluatedCalls,
    unevaluatedCalls,
    evaluationRate
  };
}

/**
 * Identifica agentes com baixa taxa de avaliação
 * @param metrics - Métricas de avaliação por agente
 * @param threshold - Limite mínimo de taxa de avaliação (padrão: 50%)
 * @returns Array com agentes que têm taxa de avaliação abaixo do limite
 */
export function getAgentsWithLowEvaluationRate(
  metrics: EvaluationMetrics[],
  threshold: number = 50
): EvaluationMetrics[] {
  return metrics.filter(metric =>
    metric.totalCalls >= 5 && // Considerar apenas agentes com pelo menos 5 chamadas
    metric.evaluationRate < threshold
  );
}

/**
 * Calcula estatísticas de avaliação por período
 * @param metrics - Métricas de avaliação
 * @returns Estatísticas resumidas
 */
export function getEvaluationStatistics(metrics: EvaluationMetrics[]): {
  averageEvaluationRate: number;
  medianEvaluationRate: number;
  bestAgent: EvaluationMetrics | null;
  worstAgent: EvaluationMetrics | null;
  agentsAboveAverage: number;
  agentsBelowAverage: number;
} {
  if (metrics.length === 0) {
    return {
      averageEvaluationRate: 0,
      medianEvaluationRate: 0,
      bestAgent: null,
      worstAgent: null,
      agentsAboveAverage: 0,
      agentsBelowAverage: 0
    };
  }

  // Filtrar agentes com pelo menos 5 chamadas para estatísticas mais confiáveis
  const filteredMetrics = metrics.filter(m => m.totalCalls >= 5);

  if (filteredMetrics.length === 0) {
    return {
      averageEvaluationRate: 0,
      medianEvaluationRate: 0,
      bestAgent: null,
      worstAgent: null,
      agentsAboveAverage: 0,
      agentsBelowAverage: 0
    };
  }

  // Calcular média
  const averageEvaluationRate = filteredMetrics.reduce((sum, m) => sum + m.evaluationRate, 0) / filteredMetrics.length;

  // Calcular mediana
  const sortedRates = filteredMetrics.map(m => m.evaluationRate).sort((a, b) => a - b);
  const medianEvaluationRate = sortedRates.length % 2 === 0
    ? (sortedRates[sortedRates.length / 2 - 1] + sortedRates[sortedRates.length / 2]) / 2
    : sortedRates[Math.floor(sortedRates.length / 2)];

  // Encontrar melhor e pior agente
  const bestAgent = filteredMetrics.reduce((best, current) =>
    current.evaluationRate > best.evaluationRate ? current : best
  );

  const worstAgent = filteredMetrics.reduce((worst, current) =>
    current.evaluationRate < worst.evaluationRate ? current : worst
  );

  // Contar agentes acima e abaixo da média
  const agentsAboveAverage = filteredMetrics.filter(m => m.evaluationRate >= averageEvaluationRate).length;
  const agentsBelowAverage = filteredMetrics.filter(m => m.evaluationRate < averageEvaluationRate).length;

  return {
    averageEvaluationRate,
    medianEvaluationRate,
    bestAgent,
    worstAgent,
    agentsAboveAverage,
    agentsBelowAverage
  };
}
