/**
 * Serviço para comunicação com a API do backend
 */

// Usar sempre as rotas locais do Next.js que fazem proxy para o backend
const API_BASE_URL = '/api/v1';

/**
 * Tipos para os relatórios
 */
export interface ReportRequestParams {
  start_date: string;
  end_date: string;
  queues: number[];
}

export interface ReportResponse {
  data: any;
}

/**
 * Função para obter o relatório de distribuição
 */
export async function getDistributionReport(params: ReportRequestParams): Promise<ReportResponse> {
  const response = await fetch(`${API_BASE_URL}/reports/distribution`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || 'Erro ao obter relatório de distribuição');
  }

  return response.json();
}

/**
 * Função para obter o relatório de pesquisa de satisfação
 */
export async function getSatisfactionSurveyReport(params: ReportRequestParams): Promise<ReportResponse> {
  const response = await fetch(`${API_BASE_URL}/reports/satisfaction-survey`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || 'Erro ao obter relatório de pesquisa de satisfação');
  }

  return response.json();
}

/**
 * Função para baixar o relatório de distribuição em PDF
 */
export async function downloadDistributionReportPDF(params: ReportRequestParams): Promise<Blob> {
  const response = await fetch(`${API_BASE_URL}/pdf-reports/distribution`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || 'Erro ao baixar relatório de distribuição em PDF');
  }

  return response.blob();
}

/**
 * Função para baixar o relatório de pesquisa de satisfação em PDF
 */
export async function downloadSatisfactionSurveyReportPDF(params: ReportRequestParams): Promise<Blob> {
  const response = await fetch(`${API_BASE_URL}/pdf-reports/satisfaction-survey`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || 'Erro ao baixar relatório de pesquisa de satisfação em PDF');
  }

  return response.blob();
}

/**
 * Função para formatar uma data para o formato YYYY-MM-DD
 */
export function formatDateToString(date: Date): string {
  return date.toISOString().split('T')[0];
}

/**
 * Função para obter o primeiro dia da semana atual
 */
export function getFirstDayOfWeek(): Date {
  const today = new Date();
  const day = today.getDay(); // 0 = domingo, 1 = segunda, etc.
  const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Ajuste para começar na segunda-feira
  return new Date(today.setDate(diff));
}

/**
 * Função para obter o último dia da semana atual
 */
export function getLastDayOfWeek(): Date {
  const firstDay = getFirstDayOfWeek();
  const lastDay = new Date(firstDay);
  lastDay.setDate(lastDay.getDate() + 6);
  return lastDay;
}

/**
 * Função para obter o primeiro dia do mês atual
 */
export function getFirstDayOfMonth(): Date {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth(), 1);
}

/**
 * Função para obter o último dia do mês atual
 */
export function getLastDayOfMonth(): Date {
  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth() + 1, 0);
}

/**
 * Função para obter o primeiro dia do ano atual
 */
export function getFirstDayOfYear(): Date {
  const today = new Date();
  return new Date(today.getFullYear(), 0, 1);
}

/**
 * Função para obter o último dia do ano atual
 */
export function getLastDayOfYear(): Date {
  const today = new Date();
  return new Date(today.getFullYear(), 11, 31);
}
