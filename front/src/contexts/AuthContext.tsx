'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { API_ENDPOINTS, FORM_HEADERS, createAuthHeaders } from '@/config/api';

// Tipos para o usuário
export interface User {
  id: number;
  nome: string;
  sobrenome: string;
  login: string;
  nivel_usuario: 'administrador' | 'agente';
  email_corporativo: string;
  ativo: boolean;
  criado_em: string;
  atualizado_em: string;
  ultimo_login?: string;
}

// Tipos para o contexto de autenticação
interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<{ success: boolean; outlookAuthRequired?: boolean; outlookAuthUrl?: string }>;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

// Criar o contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook para usar o contexto
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

// Props do provider
interface AuthProviderProps {
  children: ReactNode;
}

// Provider do contexto de autenticação
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Verificar se está autenticado
  const isAuthenticated = !!user && !!token;

  // Verificar se é administrador
  const isAdmin = user?.nivel_usuario === 'administrador';

  // Função para fazer login
  const login = async (username: string, password: string): Promise<{ success: boolean; outlookAuthRequired?: boolean; outlookAuthUrl?: string }> => {
    try {
      setIsLoading(true);

      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      // Criar URLSearchParams para enviar diretamente ao backend
      const urlParams = new URLSearchParams();
      urlParams.append('username', username);
      urlParams.append('password', password);

      console.log('🔄 AuthContext: Fazendo login para:', username);

      // Fazer requisição via proxy do Next.js (URL relativa)
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        body: urlParams,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('📡 AuthContext: Resposta recebida:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ AuthContext: Login bem-sucedido');

        // Salvar token e usuário
        setToken(data.access_token);
        setUser(data.usuario);

        // Salvar no localStorage
        localStorage.setItem('auth_token', data.access_token);
        localStorage.setItem('user_data', JSON.stringify(data.usuario));

        return { success: true };
      } else {
        const errorData = await response.json();
        console.error('❌ AuthContext: Erro no login:', errorData);
        return { success: false };
      }
    } catch (error) {
      console.error('Erro ao fazer login:', error);
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  // Função para fazer logout
  const logout = () => {
    console.log('🔄 AuthContext: Fazendo logout e limpando dados...');

    setUser(null);
    setToken(null);

    // Remover todos os dados do localStorage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('outlook_user_id');
    localStorage.removeItem('outlook_user_email');
    localStorage.removeItem('outlook_authenticated');
    localStorage.removeItem('outlook_status');

    // Redirecionar para login
    window.location.href = '/login';
  };

  // Função para verificar autenticação
  const checkAuth = async () => {
    try {
      setIsLoading(true);

      const savedToken = localStorage.getItem('auth_token');
      const savedUser = localStorage.getItem('user_data');

      if (savedToken && savedUser) {
        console.log('🔄 AuthContext: Verificando token salvo...');

        // Verificar se o token ainda é válido via proxy do Next.js
        const response = await fetch('/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${savedToken}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const userData = await response.json();
          console.log('✅ AuthContext: Token válido, usuário logado:', userData.login);
          setToken(savedToken);
          setUser(userData);
        } else {
          console.log('❌ AuthContext: Token inválido, limpando dados');
          // Token inválido, limpar dados
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          localStorage.removeItem('outlook_user_id');
          localStorage.removeItem('outlook_user_email');
          localStorage.removeItem('outlook_authenticated');
          setToken(null);
          setUser(null);

          // Se estivermos em uma página protegida, redirecionar para login
          if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
            window.location.href = '/login';
          }
        }
      }
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      // Em caso de erro, limpar dados
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      localStorage.removeItem('outlook_user_id');
      localStorage.removeItem('outlook_user_email');
      localStorage.removeItem('outlook_authenticated');
    } finally {
      setIsLoading(false);
    }
  };

  // Verificar autenticação ao carregar o componente
  useEffect(() => {
    checkAuth();
  }, []);

  // Verificação periódica de token (mais estável que interceptor)
  useEffect(() => {
    if (token && isAuthenticated) {
      // Verificar token a cada 30 minutos
      const interval = setInterval(async () => {
        try {
          const response = await fetch('/api/v1/auth/me', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            console.log('❌ AuthContext: Token expirado, fazendo logout');
            logout();
          }
        } catch (error) {
          console.log('❌ AuthContext: Erro na verificação periódica, fazendo logout');
          logout();
        }
      }, 30 * 60 * 1000); // 30 minutos

      return () => clearInterval(interval);
    }
  }, [token, isAuthenticated]);

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated,
    isAdmin,
    isLoading,
    login,
    logout,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
