import React, { useState } from 'react';

interface SendEmailRequest {
  to: string[];
  cc: string[];
  bcc: string[];
  subject: string;
  body: string;
  body_type: string;
  importance: string;
}

interface OutlookSendEmailProps {
  userId: string;
}

export const OutlookSendEmail: React.FC<OutlookSendEmailProps> = ({ userId }) => {
  const [formData, setFormData] = useState<SendEmailRequest>({
    to: [''],
    cc: [],
    bcc: [],
    subject: '',
    body: '',
    body_type: 'HTML',
    importance: 'normal'
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showCc, setShowCc] = useState(false);
  const [showBcc, setShowBcc] = useState(false);

  const handleInputChange = (field: keyof SendEmailRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Limpar mensagens ao editar
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleEmailListChange = (field: 'to' | 'cc' | 'bcc', index: number, value: string) => {
    const newList = [...formData[field]];
    newList[index] = value;
    handleInputChange(field, newList);
  };

  const addEmailField = (field: 'to' | 'cc' | 'bcc') => {
    const newList = [...formData[field], ''];
    handleInputChange(field, newList);
  };

  const removeEmailField = (field: 'to' | 'cc' | 'bcc', index: number) => {
    const newList = formData[field].filter((_, i) => i !== index);
    handleInputChange(field, newList);
  };

  const validateForm = (): string | null => {
    // Validar destinatários
    const validToEmails = formData.to.filter(email => email.trim() && isValidEmail(email.trim()));
    if (validToEmails.length === 0) {
      return 'Pelo menos um destinatário válido é obrigatório';
    }

    // Validar assunto
    if (!formData.subject.trim()) {
      return 'O assunto é obrigatório';
    }

    // Validar corpo
    if (!formData.body.trim()) {
      return 'O corpo do email é obrigatório';
    }

    return null;
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Filtrar emails válidos
      const cleanedData = {
        ...formData,
        to: formData.to.filter(email => email.trim() && isValidEmail(email.trim())),
        cc: formData.cc.filter(email => email.trim() && isValidEmail(email.trim())),
        bcc: formData.bcc.filter(email => email.trim() && isValidEmail(email.trim()))
      };

      const response = await fetch(`http://localhost:8001/api/v1/outlook/send?user_id=${userId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Erro ao enviar email');
      }

      const result = await response.json();
      setSuccess('Email enviado com sucesso!');
      
      // Limpar formulário
      setFormData({
        to: [''],
        cc: [],
        bcc: [],
        subject: '',
        body: '',
        body_type: 'HTML',
        importance: 'normal'
      });
      setShowCc(false);
      setShowBcc(false);

    } catch (err: any) {
      console.error('Erro ao enviar email:', err);
      setError(err.message || 'Erro ao enviar email');
    } finally {
      setIsLoading(false);
    }
  };

  const renderEmailFields = (field: 'to' | 'cc' | 'bcc', label: string) => (
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      {formData[field].map((email, index) => (
        <div key={index} className="flex items-center space-x-2 mb-2">
          <input
            type="email"
            value={email}
            onChange={(e) => handleEmailListChange(field, index, e.target.value)}
            placeholder="<EMAIL>"
            className="flex-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          />
          {formData[field].length > 1 && (
            <button
              type="button"
              onClick={() => removeEmailField(field, index)}
              className="text-red-600 hover:text-red-800"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      ))}
      <button
        type="button"
        onClick={() => addEmailField(field)}
        className="text-sm text-blue-600 hover:text-blue-800"
      >
        + Adicionar {label.toLowerCase()}
      </button>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">Enviar Email</h3>
        <p className="mt-1 text-sm text-gray-500">
          Envie emails diretamente através da integração com Microsoft Outlook.
        </p>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Destinatários */}
        {renderEmailFields('to', 'Para')}

        {/* Botões CC/BCC */}
        <div className="flex space-x-4">
          {!showCc && (
            <button
              type="button"
              onClick={() => setShowCc(true)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              + CC
            </button>
          )}
          {!showBcc && (
            <button
              type="button"
              onClick={() => setShowBcc(true)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              + BCC
            </button>
          )}
        </div>

        {/* CC */}
        {showCc && renderEmailFields('cc', 'CC')}

        {/* BCC */}
        {showBcc && renderEmailFields('bcc', 'BCC')}

        {/* Assunto */}
        <div>
          <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
            Assunto
          </label>
          <input
            type="text"
            id="subject"
            value={formData.subject}
            onChange={(e) => handleInputChange('subject', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            placeholder="Digite o assunto do email"
          />
        </div>

        {/* Importância */}
        <div>
          <label htmlFor="importance" className="block text-sm font-medium text-gray-700">
            Importância
          </label>
          <select
            id="importance"
            value={formData.importance}
            onChange={(e) => handleInputChange('importance', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          >
            <option value="low">Baixa</option>
            <option value="normal">Normal</option>
            <option value="high">Alta</option>
          </select>
        </div>

        {/* Tipo de corpo */}
        <div>
          <label className="block text-sm font-medium text-gray-700">
            Formato do texto
          </label>
          <div className="mt-2 space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                value="HTML"
                checked={formData.body_type === 'HTML'}
                onChange={(e) => handleInputChange('body_type', e.target.value)}
                className="form-radio h-4 w-4 text-blue-600"
              />
              <span className="ml-2 text-sm text-gray-700">HTML</span>
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                value="Text"
                checked={formData.body_type === 'Text'}
                onChange={(e) => handleInputChange('body_type', e.target.value)}
                className="form-radio h-4 w-4 text-blue-600"
              />
              <span className="ml-2 text-sm text-gray-700">Texto simples</span>
            </label>
          </div>
        </div>

        {/* Corpo do email */}
        <div>
          <label htmlFor="body" className="block text-sm font-medium text-gray-700">
            Mensagem
          </label>
          <textarea
            id="body"
            rows={8}
            value={formData.body}
            onChange={(e) => handleInputChange('body', e.target.value)}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            placeholder={formData.body_type === 'HTML' ? 'Digite o conteúdo HTML do email...' : 'Digite o conteúdo do email...'}
          />
        </div>

        {/* Botões de ação */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => {
              setFormData({
                to: [''],
                cc: [],
                bcc: [],
                subject: '',
                body: '',
                body_type: 'HTML',
                importance: 'normal'
              });
              setShowCc(false);
              setShowBcc(false);
              setError(null);
              setSuccess(null);
            }}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Limpar
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Enviando...
              </>
            ) : (
              'Enviar Email'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};
