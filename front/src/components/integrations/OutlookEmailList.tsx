import React, { useState, useEffect } from 'react';

interface EmailAddress {
  name?: string;
  address: string;
}

interface EmailRecipient {
  email_address: EmailAddress;
}

interface EmailMessage {
  id: string;
  subject: string;
  body_content: string;
  body_content_type: string;
  from?: EmailRecipient;
  to_recipients: EmailRecipient[];
  importance: string;
  is_read: boolean;
  received_date_time?: string;
  sent_date_time?: string;
  conversation_id?: string;
  web_link?: string;
}

interface EmailFolder {
  id: string;
  display_name: string;
  unread_item_count: number;
  total_item_count: number;
}

interface OutlookEmailListProps {
  userId: string;
}

export const OutlookEmailList: React.FC<OutlookEmailListProps> = ({ userId }) => {
  const [emails, setEmails] = useState<EmailMessage[]>([]);
  const [folders, setFolders] = useState<EmailFolder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string>('inbox');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);

  // Estados para modal de confirmação de exclusão
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [emailToDelete, setEmailToDelete] = useState<string | null>(null);

  useEffect(() => {
    // Só carregar se userId estiver disponível
    if (userId) {
      loadFolders();
    }
  }, [userId]);

  useEffect(() => {
    // Só carregar se userId estiver disponível e pasta selecionada
    if (selectedFolder && userId) {
      loadEmails();
    }
  }, [selectedFolder, userId]);

  const loadFolders = async () => {
    try {
      const response = await fetch(`/api/v1/outlook/folders?user_id=${userId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar pastas');
      }

      const foldersData = await response.json();
      setFolders(foldersData);
    } catch (err) {
      console.error('Erro ao carregar pastas:', err);
      setError('Erro ao carregar pastas');
    }
  };

  const loadEmails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(
        `/api/v1/outlook/messages?user_id=${userId}&folder_id=${selectedFolder}&limit=50`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Erro ao carregar emails');
      }

      const emailsData = await response.json();
      setEmails(emailsData);
    } catch (err) {
      console.error('Erro ao carregar emails:', err);
      setError('Erro ao carregar emails');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('pt-BR');
  };

  const handleMarkAsRead = async (messageId: string, isRead: boolean) => {
    try {
      const response = await fetch(`/api/v1/outlook/messages/read?user_id=${userId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          message_id: messageId,
          is_read: isRead
        })
      });

      if (response.ok) {
        // Atualizar o estado local
        setEmails(emails.map(email =>
          email.id === messageId
            ? { ...email, is_read: isRead }
            : email
        ));
      } else {
        console.error('Erro ao marcar email como lido/não lido');
      }
    } catch (error) {
      console.error('Erro ao marcar email:', error);
    }
  };

  // Função para abrir modal de confirmação de exclusão
  const handleDeleteEmail = (messageId: string) => {
    setEmailToDelete(messageId);
    setShowDeleteConfirm(true);
  };

  // Função para confirmar exclusão
  const confirmDeleteEmail = async () => {
    if (!emailToDelete) return;

    try {
      const response = await fetch(`/api/v1/outlook/messages/${emailToDelete}?user_id=${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        }
      });

      if (response.ok) {
        // Remover o email da lista local
        setEmails(emails.filter(email => email.id !== emailToDelete));

        // Se o email excluído estava selecionado, fechar o modal
        if (selectedEmail?.id === emailToDelete) {
          setSelectedEmail(null);
        }
      } else {
        console.error('Erro ao excluir email');
      }
    } catch (error) {
      console.error('Erro ao excluir email:', error);
    } finally {
      // Fechar modal de confirmação
      setShowDeleteConfirm(false);
      setEmailToDelete(null);
    }
  };

  // Função para cancelar exclusão
  const cancelDeleteEmail = () => {
    setShowDeleteConfirm(false);
    setEmailToDelete(null);
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const stripHtml = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Emails</h3>

            {/* Seletor de pasta */}
            <div className="flex items-center space-x-4">
              <label htmlFor="folder-select" className="text-sm font-medium text-gray-700">
                Pasta:
              </label>
              <select
                id="folder-select"
                value={selectedFolder}
                onChange={(e) => setSelectedFolder(e.target.value)}
                className="block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                {folders.map((folder) => (
                  <option key={folder.id} value={folder.id}>
                    {folder.display_name} ({folder.total_item_count})
                  </option>
                ))}
              </select>

              <button
                onClick={loadEmails}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Atualizar
              </button>
            </div>
          </div>
        </div>

        {/* Barra de informações sobre funcionalidades */}
        <div className="px-6 py-3 bg-blue-50 border-t border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm text-blue-800 font-medium">
                💡 Dica: Use os botões ao lado direito de cada email para marcar como lido ou excluir
              </span>
            </div>
            <div className="flex items-center space-x-3 text-sm text-blue-700">
              <span className="flex items-center space-x-1 px-2 py-1 bg-blue-100 rounded">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>Marcar lido</span>
              </span>
              <span className="flex items-center space-x-1 px-2 py-1 bg-red-100 rounded">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                <span>Excluir</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <p className="text-sm text-red-800">{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando emails...</p>
        </div>
      ) : emails.length === 0 ? (
        <div className="p-8 text-center">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Nenhum email encontrado</h3>
          <p className="mt-1 text-sm text-gray-500">Esta pasta não contém emails ou eles ainda não foram sincronizados.</p>
        </div>
      ) : (
        <div className="divide-y divide-gray-200">
          {emails.map((email) => (
            <div
              key={email.id}
              className={`p-4 hover:bg-gray-50 cursor-pointer ${!email.is_read ? 'bg-blue-50' : ''}`}
              onClick={() => setSelectedEmail(email)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    {!email.is_read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                    <p className={`text-sm ${!email.is_read ? 'font-semibold' : 'font-medium'} text-gray-900 truncate`}>
                      {email.from?.email_address.name || email.from?.email_address.address || 'Remetente desconhecido'}
                    </p>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                      email.importance === 'high' ? 'bg-red-100 text-red-800' :
                      email.importance === 'low' ? 'bg-gray-100 text-gray-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {email.importance === 'high' ? 'Alta' : email.importance === 'low' ? 'Baixa' : 'Normal'}
                    </span>
                  </div>

                  <p className={`mt-1 text-sm ${!email.is_read ? 'font-medium' : ''} text-gray-900 truncate`}>
                    {email.subject || '(Sem assunto)'}
                  </p>

                  <p className="mt-1 text-sm text-gray-500 truncate">
                    {truncateText(stripHtml(email.body_content))}
                  </p>
                </div>

                <div className="ml-4 flex-shrink-0 text-right">
                  <div className="flex items-center space-x-2 mb-2">
                    {/* Botão Marcar como lido/não lido - MAIS VISÍVEL */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMarkAsRead(email.id, !email.is_read);
                      }}
                      className={`px-3 py-2 rounded-lg border-2 font-medium text-sm transition-all duration-200 shadow-sm ${
                        email.is_read
                          ? 'text-gray-600 border-gray-400 bg-gray-50 hover:bg-gray-100 hover:border-gray-500'
                          : 'text-blue-700 border-blue-500 bg-blue-50 hover:bg-blue-100 hover:border-blue-600'
                      }`}
                      title={email.is_read ? 'Marcar como não lido' : 'Marcar como lido'}
                    >
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span className="hidden sm:inline">
                          {email.is_read ? 'Não lido' : 'Lido'}
                        </span>
                      </div>
                    </button>

                    {/* Botão Excluir - MUITO MAIS VISÍVEL */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteEmail(email.id);
                      }}
                      className="px-3 py-2 rounded-lg border-2 border-red-500 bg-red-50 text-red-700 hover:bg-red-100 hover:border-red-600 font-medium text-sm transition-all duration-200 shadow-sm"
                      title="Excluir email"
                    >
                      <div className="flex items-center space-x-1">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        <span className="hidden sm:inline">Excluir</span>
                      </div>
                    </button>
                  </div>

                  <p className="text-xs text-gray-500">
                    {formatDate(email.received_date_time)}
                  </p>
                  {email.web_link && (
                    <a
                      href={email.web_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-1 inline-flex items-center text-xs text-blue-600 hover:text-blue-500"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                      Abrir no Outlook
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de visualização de email */}
      {selectedEmail && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {selectedEmail.subject || '(Sem assunto)'}
              </h3>
              <div className="flex items-center space-x-3">
                {/* Botões de ação no modal - MAIS VISÍVEIS */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMarkAsRead(selectedEmail.id, !selectedEmail.is_read);
                  }}
                  className={`px-4 py-2 rounded-lg border-2 font-medium text-sm transition-all duration-200 shadow-sm ${
                    selectedEmail.is_read
                      ? 'text-gray-600 border-gray-400 bg-gray-50 hover:bg-gray-100'
                      : 'text-blue-700 border-blue-500 bg-blue-50 hover:bg-blue-100'
                  }`}
                  title={selectedEmail.is_read ? 'Marcar como não lido' : 'Marcar como lido'}
                >
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span>{selectedEmail.is_read ? 'Marcar não lido' : 'Marcar como lido'}</span>
                  </div>
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteEmail(selectedEmail.id);
                    setSelectedEmail(null);
                  }}
                  className="px-4 py-2 rounded-lg border-2 border-red-500 bg-red-50 text-red-700 hover:bg-red-100 hover:border-red-600 font-medium text-sm transition-all duration-200 shadow-sm"
                  title="Excluir email"
                >
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span>Excluir Email</span>
                  </div>
                </button>

                <button
                  onClick={() => setSelectedEmail(null)}
                  className="text-gray-400 hover:text-gray-600 p-1"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="mb-4 space-y-2">
              <p className="text-sm">
                <span className="font-medium">De:</span> {selectedEmail.from?.email_address.name || selectedEmail.from?.email_address.address}
              </p>
              <p className="text-sm">
                <span className="font-medium">Para:</span> {selectedEmail.to_recipients.map(r => r.email_address.name || r.email_address.address).join(', ')}
              </p>
              <p className="text-sm">
                <span className="font-medium">Data:</span> {formatDate(selectedEmail.received_date_time)}
              </p>
            </div>

            <div className="border-t pt-4 max-h-96 overflow-y-auto">
              <div
                className="prose max-w-none text-sm"
                dangerouslySetInnerHTML={{
                  __html: selectedEmail.body_content_type === 'HTML'
                    ? selectedEmail.body_content
                    : selectedEmail.body_content.replace(/\n/g, '<br>')
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmação de Exclusão */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    Confirmar Exclusão
                  </h3>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-600">
                  Tem certeza que deseja excluir este email? O email será movido para a pasta de itens excluídos e poderá ser recuperado posteriormente.
                </p>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteEmail}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={confirmDeleteEmail}
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  Excluir Email
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
