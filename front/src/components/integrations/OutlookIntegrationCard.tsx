import React from 'react';

interface OutlookUser {
  id: string;
  display_name: string;
  email: string;
  user_principal_name: string;
  given_name?: string;
  surname?: string;
  job_title?: string;
  office_location?: string;
}

interface OutlookIntegrationStatus {
  is_connected: boolean;
  user_info?: OutlookUser;
  last_sync?: string;
  sync_status: string;
  error_message?: string;
  folders_count: number;
  messages_count: number;
}

interface OutlookIntegrationCardProps {
  status: OutlookIntegrationStatus | null;
  onConnect: () => void;
  onDisconnect: () => void;
  onSync: () => void;
}

export const OutlookIntegrationCard: React.FC<OutlookIntegrationCardProps> = ({
  status,
  onConnect,
  onDisconnect,
  onSync
}) => {
  if (!status) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {/* Ícone do Outlook */}
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 9a4 4 0 1 1 8 0v9a4 4 0 1 1-8 0V9z"/>
                <path d="M15 9a4 4 0 1 1 8 0v9a4 4 0 1 1-8 0V9z"/>
              </svg>
            </div>
          </div>

          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900">Microsoft Outlook</h3>
            <div className="flex items-center mt-1">
              <div className={`w-2 h-2 rounded-full mr-2 ${
                status.is_connected ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <p className="text-sm text-gray-600">
                {status.is_connected ? 'Conectado' : 'Desconectado'}
              </p>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          {status.is_connected ? (
            <>
              <button
                onClick={onSync}
                disabled={status.sync_status === 'syncing'}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {status.sync_status === 'syncing' ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sincronizando...
                  </>
                ) : (
                  <>
                    <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Sincronizar
                  </>
                )}
              </button>
              <button
                onClick={onDisconnect}
                className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Desconectar
              </button>
            </>
          ) : (
            <button
              onClick={onConnect}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              Conectar
            </button>
          )}
        </div>
      </div>

      {/* Informações do usuário conectado */}
      {status.is_connected && status.user_info && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-700">
                  {status.user_info.given_name?.[0] || status.user_info.display_name[0]}
                </span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {status.user_info.display_name}
              </p>
              <p className="text-sm text-gray-500">
                {status.user_info.email}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Mensagem de erro */}
      {status.error_message && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{status.error_message}</p>
            </div>
          </div>
        </div>
      )}

      {/* Informações de sincronização */}
      {status.is_connected && status.last_sync && (
        <div className="mt-4 text-sm text-gray-500">
          <p>
            Última sincronização: {new Date(status.last_sync).toLocaleString('pt-BR')}
          </p>
        </div>
      )}
    </div>
  );
};
