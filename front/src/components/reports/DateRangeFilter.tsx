'use client';

import React, { useState, useEffect } from 'react';
import {
  formatDateToString,
  getFirstDayOfWeek,
  getLastDayOfWeek,
  getFirstDayOfMonth,
  getLastDayOfMonth,
  getFirstDayOfYear,
  getLastDayOfYear
} from '@/services/api';

interface DateRangeFilterProps {
  onFilterChange: (startDate: string, endDate: string) => void;
  defaultQueues?: number[];
  onQueuesChange?: (queues: number[]) => void;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  onFilterChange,
  defaultQueues = [803, 802],
  onQueuesChange
}) => {
  // Inicializar com o dia atual
  const today = new Date();
  const [startDate, setStartDate] = useState<string>(formatDateToString(today));
  const [endDate, setEndDate] = useState<string>(formatDateToString(today));
  const [selectedQueues, setSelectedQueues] = useState<number[]>(defaultQueues);
  const [activeFilter, setActiveFilter] = useState<string>('today');

  // Aplicar o filtro inicial quando o componente for montado
  useEffect(() => {
    onFilterChange(startDate, endDate);
    if (onQueuesChange) {
      onQueuesChange(selectedQueues);
    }
  }, []);

  // Lista de filas disponíveis
  const availableQueues = [
    { id: 803, name: 'Fila 803' },
    { id: 802, name: 'Fila 802' },
    { id: 801, name: 'Fila 801' },
  ];

  const applyFilter = () => {
    onFilterChange(startDate, endDate);
    if (onQueuesChange) {
      onQueuesChange(selectedQueues);
    }
  };

  const handleFilterClick = (filter: string) => {
    let start: Date;
    let end: Date;

    switch (filter) {
      case 'today':
        start = new Date();
        end = new Date();
        break;
      case 'yesterday':
        start = new Date();
        start.setDate(start.getDate() - 1);
        end = new Date(start);
        break;
      case 'week':
        start = getFirstDayOfWeek();
        end = getLastDayOfWeek();
        break;
      case 'month':
        start = getFirstDayOfMonth();
        end = getLastDayOfMonth();
        break;
      case 'year':
        start = getFirstDayOfYear();
        end = getLastDayOfYear();
        break;
      default:
        return;
    }

    const formattedStartDate = formatDateToString(start);
    const formattedEndDate = formatDateToString(end);

    setStartDate(formattedStartDate);
    setEndDate(formattedEndDate);
    setActiveFilter(filter);

    // Aplicar o filtro automaticamente
    onFilterChange(formattedStartDate, formattedEndDate);

    // Se houver uma função para alterar as filas, também a chamamos
    if (onQueuesChange) {
      onQueuesChange(selectedQueues);
    }
  };

  const handleQueueChange = (queueId: number) => {
    const newSelectedQueues = selectedQueues.includes(queueId)
      ? selectedQueues.filter(id => id !== queueId)
      : [...selectedQueues, queueId];

    setSelectedQueues(newSelectedQueues);

    if (onQueuesChange) {
      onQueuesChange(newSelectedQueues);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Filtros</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">
            Data Inicial
          </label>
          <input
            type="date"
            id="start-date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            onBlur={() => applyFilter()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">
            Data Final
          </label>
          <input
            type="date"
            id="end-date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            onBlur={() => applyFilter()}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        <button
          type="button"
          onClick={() => handleFilterClick('today')}
          className={`px-3 py-1 text-sm rounded-md ${
            activeFilter === 'today'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          Hoje
        </button>
        <button
          type="button"
          onClick={() => handleFilterClick('yesterday')}
          className={`px-3 py-1 text-sm rounded-md ${
            activeFilter === 'yesterday'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          Ontem
        </button>
        <button
          type="button"
          onClick={() => handleFilterClick('week')}
          className={`px-3 py-1 text-sm rounded-md ${
            activeFilter === 'week'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          Esta Semana
        </button>
        <button
          type="button"
          onClick={() => handleFilterClick('month')}
          className={`px-3 py-1 text-sm rounded-md ${
            activeFilter === 'month'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          Este Mês
        </button>
        <button
          type="button"
          onClick={() => handleFilterClick('year')}
          className={`px-3 py-1 text-sm rounded-md ${
            activeFilter === 'year'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          }`}
        >
          Este Ano
        </button>
      </div>

      {onQueuesChange && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Filas
          </label>
          <div className="flex flex-wrap gap-2">
            {availableQueues.map((queue) => (
              <label key={queue.id} className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={selectedQueues.includes(queue.id)}
                  onChange={() => handleQueueChange(queue.id)}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 mr-1"
                />
                <span className="text-sm text-gray-700">{queue.name}</span>
              </label>
            ))}
          </div>
        </div>
      )}

      <div className="flex justify-end">
        <button
          type="button"
          onClick={applyFilter}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Aplicar Filtros
        </button>
      </div>
    </div>
  );
};

export default DateRangeFilter;
