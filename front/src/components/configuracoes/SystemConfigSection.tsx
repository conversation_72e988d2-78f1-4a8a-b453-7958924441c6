'use client';

import React, { useState, useEffect } from 'react';
import { Server, Database, Zap, Clock, FileText, HardDrive, Activity, Save, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface SystemConfigSectionProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

interface SystemConfig {
  cache_enabled: boolean;
  cache_ttl: number;
  max_file_size: number;
  session_timeout: number;
  log_level: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR';
  backup_enabled: boolean;
  backup_frequency: 'daily' | 'weekly' | 'monthly';
  maintenance_mode: boolean;
}

export default function SystemConfigSection({ onSuccess, onError }: SystemConfigSectionProps) {
  const { token } = useAuth();
  const [config, setConfig] = useState<SystemConfig>({
    cache_enabled: true,
    cache_ttl: 3600,
    max_file_size: 10,
    session_timeout: 1440,
    log_level: 'INFO',
    backup_enabled: false,
    backup_frequency: 'daily',
    maintenance_mode: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const loadConfig = async () => {
    try {
      setIsLoading(true);
      // TODO: Implementar API para carregar configurações
      // Por enquanto, usar valores padrão
      onSuccess('Configurações carregadas com sucesso');
    } catch (error) {
      onError('Erro ao carregar configurações do sistema');
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setIsSaving(true);
      // TODO: Implementar API para salvar configurações
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular delay
      onSuccess('Configurações salvas com sucesso');
    } catch (error) {
      onError('Erro ao salvar configurações do sistema');
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const configSections = [
    {
      id: 'performance',
      title: 'Performance e Cache',
      icon: Zap,
      description: 'Configurações para otimizar a performance do sistema',
      settings: [
        {
          key: 'cache_enabled',
          label: 'Habilitar Cache',
          type: 'boolean',
          description: 'Ativa o sistema de cache para melhorar a performance'
        },
        {
          key: 'cache_ttl',
          label: 'TTL do Cache (segundos)',
          type: 'number',
          description: 'Tempo de vida dos dados em cache',
          min: 300,
          max: 86400
        }
      ]
    },
    {
      id: 'files',
      title: 'Arquivos e Upload',
      icon: HardDrive,
      description: 'Configurações de upload e armazenamento de arquivos',
      settings: [
        {
          key: 'max_file_size',
          label: 'Tamanho Máximo de Arquivo (MB)',
          type: 'number',
          description: 'Tamanho máximo permitido para upload de arquivos',
          min: 1,
          max: 100
        }
      ]
    },
    {
      id: 'session',
      title: 'Sessões e Segurança',
      icon: Clock,
      description: 'Configurações de sessão e timeout',
      settings: [
        {
          key: 'session_timeout',
          label: 'Timeout de Sessão (minutos)',
          type: 'number',
          description: 'Tempo limite para sessões inativas',
          min: 30,
          max: 2880
        }
      ]
    },
    {
      id: 'logging',
      title: 'Logs e Auditoria',
      icon: FileText,
      description: 'Configurações de logging e auditoria',
      settings: [
        {
          key: 'log_level',
          label: 'Nível de Log',
          type: 'select',
          description: 'Nível de detalhamento dos logs',
          options: [
            { value: 'DEBUG', label: 'Debug (Muito Detalhado)' },
            { value: 'INFO', label: 'Info (Padrão)' },
            { value: 'WARNING', label: 'Warning (Apenas Avisos)' },
            { value: 'ERROR', label: 'Error (Apenas Erros)' }
          ]
        }
      ]
    },
    {
      id: 'backup',
      title: 'Backup e Manutenção',
      icon: Database,
      description: 'Configurações de backup e manutenção do sistema',
      settings: [
        {
          key: 'backup_enabled',
          label: 'Habilitar Backup Automático',
          type: 'boolean',
          description: 'Ativa backups automáticos do banco de dados'
        },
        {
          key: 'backup_frequency',
          label: 'Frequência do Backup',
          type: 'select',
          description: 'Frequência dos backups automáticos',
          options: [
            { value: 'daily', label: 'Diário' },
            { value: 'weekly', label: 'Semanal' },
            { value: 'monthly', label: 'Mensal' }
          ]
        },
        {
          key: 'maintenance_mode',
          label: 'Modo de Manutenção',
          type: 'boolean',
          description: 'Ativa o modo de manutenção (bloqueia acesso de usuários)'
        }
      ]
    }
  ];

  const renderSetting = (setting: any) => {
    const value = config[setting.key as keyof SystemConfig];

    switch (setting.type) {
      case 'boolean':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              id={setting.key}
              checked={value as boolean}
              onChange={(e) => setConfig(prev => ({ ...prev, [setting.key]: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor={setting.key} className="ml-2 block text-sm text-gray-900">
              {setting.label}
            </label>
          </div>
        );

      case 'number':
        return (
          <div>
            <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700 mb-1">
              {setting.label}
            </label>
            <input
              type="number"
              id={setting.key}
              value={value as number}
              onChange={(e) => setConfig(prev => ({ ...prev, [setting.key]: parseInt(e.target.value) }))}
              min={setting.min}
              max={setting.max}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        );

      case 'select':
        return (
          <div>
            <label htmlFor={setting.key} className="block text-sm font-medium text-gray-700 mb-1">
              {setting.label}
            </label>
            <select
              id={setting.key}
              value={value as string}
              onChange={(e) => setConfig(prev => ({ ...prev, [setting.key]: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {setting.options.map((option: any) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Server className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Configurações do Sistema</h2>
              <p className="text-gray-600 mt-1">
                Configure parâmetros gerais de funcionamento do sistema
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={loadConfig}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Recarregar
            </button>
            <button
              onClick={saveConfig}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Seções de Configuração */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {configSections.map((section) => {
          const Icon = section.icon;
          return (
            <div key={section.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center mb-4">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <Icon className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{section.title}</h3>
                  <p className="text-sm text-gray-600">{section.description}</p>
                </div>
              </div>

              <div className="space-y-4">
                {section.settings.map((setting) => (
                  <div key={setting.key} className="border border-gray-200 rounded-lg p-4">
                    {renderSetting(setting)}
                    {setting.description && (
                      <p className="mt-2 text-xs text-gray-500">{setting.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Status do Sistema */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-4">
          <Activity className="h-6 w-6 text-green-600 mr-3" />
          <h3 className="text-lg font-medium text-gray-900">Status do Sistema</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-green-800">Sistema Online</span>
            </div>
            <p className="text-xs text-green-600 mt-1">Todos os serviços funcionando</p>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-blue-800">Cache Ativo</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">Performance otimizada</p>
          </div>
          
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
              <span className="text-sm font-medium text-gray-800">Backup Pendente</span>
            </div>
            <p className="text-xs text-gray-600 mt-1">Configure backup automático</p>
          </div>
        </div>
      </div>
    </div>
  );
}
