'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Lock, Users, Eye, AlertTriangle, Save, RefreshCw, Key, Clock, UserCheck } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface SecurityConfigSectionProps {
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

interface SecurityConfig {
  password_min_length: number;
  password_require_uppercase: boolean;
  password_require_lowercase: boolean;
  password_require_numbers: boolean;
  password_require_symbols: boolean;
  password_expiry_days: number;
  max_login_attempts: number;
  lockout_duration: number;
  session_timeout: number;
  require_2fa: boolean;
  audit_login_attempts: boolean;
  audit_data_changes: boolean;
  default_user_permissions: string[];
}

export default function SecurityConfigSection({ onSuccess, onError }: SecurityConfigSectionProps) {
  const { token } = useAuth();
  const [config, setConfig] = useState<SecurityConfig>({
    password_min_length: 8,
    password_require_uppercase: true,
    password_require_lowercase: true,
    password_require_numbers: true,
    password_require_symbols: false,
    password_expiry_days: 90,
    max_login_attempts: 5,
    lockout_duration: 30,
    session_timeout: 1440,
    require_2fa: false,
    audit_login_attempts: true,
    audit_data_changes: true,
    default_user_permissions: ['dashboard_view', 'reports_view']
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const loadConfig = async () => {
    try {
      setIsLoading(true);
      // TODO: Implementar API para carregar configurações de segurança
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular delay
      onSuccess('Configurações de segurança carregadas');
    } catch (error) {
      onError('Erro ao carregar configurações de segurança');
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setIsSaving(true);
      // TODO: Implementar API para salvar configurações de segurança
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simular delay
      onSuccess('Configurações de segurança salvas com sucesso');
    } catch (error) {
      onError('Erro ao salvar configurações de segurança');
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const availablePermissions = [
    { id: 'dashboard_view', label: 'Visualizar Dashboard', category: 'Dashboard' },
    { id: 'reports_view', label: 'Visualizar Relatórios', category: 'Relatórios' },
    { id: 'reports_export', label: 'Exportar Relatórios', category: 'Relatórios' },
    { id: 'users_view', label: 'Visualizar Usuários', category: 'Usuários' },
    { id: 'users_manage', label: 'Gerenciar Usuários', category: 'Usuários' },
    { id: 'config_view', label: 'Visualizar Configurações', category: 'Configurações' },
    { id: 'config_manage', label: 'Gerenciar Configurações', category: 'Configurações' },
    { id: 'outlook_access', label: 'Acessar Outlook', category: 'Integrações' },
    { id: 'threecx_access', label: 'Acessar 3CX', category: 'Integrações' }
  ];

  const getPasswordStrength = () => {
    let strength = 0;
    let requirements = [];

    if (config.password_min_length >= 8) {
      strength += 20;
      requirements.push('Mínimo 8 caracteres');
    }
    if (config.password_require_uppercase) {
      strength += 20;
      requirements.push('Letras maiúsculas');
    }
    if (config.password_require_lowercase) {
      strength += 20;
      requirements.push('Letras minúsculas');
    }
    if (config.password_require_numbers) {
      strength += 20;
      requirements.push('Números');
    }
    if (config.password_require_symbols) {
      strength += 20;
      requirements.push('Símbolos');
    }

    return { strength, requirements };
  };

  const { strength, requirements } = getPasswordStrength();

  const getStrengthColor = (strength: number) => {
    if (strength >= 80) return 'bg-green-500';
    if (strength >= 60) return 'bg-yellow-500';
    if (strength >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getStrengthText = (strength: number) => {
    if (strength >= 80) return 'Muito Forte';
    if (strength >= 60) return 'Forte';
    if (strength >= 40) return 'Média';
    return 'Fraca';
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Shield className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Configurações de Segurança</h2>
              <p className="text-gray-600 mt-1">
                Configure políticas de segurança, senhas e controle de acesso
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={loadConfig}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Recarregar
            </button>
            <button
              onClick={saveConfig}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Salvando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Políticas de Senha */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <Lock className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Políticas de Senha</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Comprimento Mínimo
              </label>
              <input
                type="number"
                value={config.password_min_length}
                onChange={(e) => setConfig(prev => ({ ...prev, password_min_length: parseInt(e.target.value) }))}
                min={6}
                max={32}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Requisitos</label>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="uppercase"
                  checked={config.password_require_uppercase}
                  onChange={(e) => setConfig(prev => ({ ...prev, password_require_uppercase: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="uppercase" className="ml-2 text-sm text-gray-900">
                  Letras maiúsculas (A-Z)
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="lowercase"
                  checked={config.password_require_lowercase}
                  onChange={(e) => setConfig(prev => ({ ...prev, password_require_lowercase: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="lowercase" className="ml-2 text-sm text-gray-900">
                  Letras minúsculas (a-z)
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="numbers"
                  checked={config.password_require_numbers}
                  onChange={(e) => setConfig(prev => ({ ...prev, password_require_numbers: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="numbers" className="ml-2 text-sm text-gray-900">
                  Números (0-9)
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="symbols"
                  checked={config.password_require_symbols}
                  onChange={(e) => setConfig(prev => ({ ...prev, password_require_symbols: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="symbols" className="ml-2 text-sm text-gray-900">
                  Símbolos (!@#$%^&*)
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiração da Senha (dias)
              </label>
              <input
                type="number"
                value={config.password_expiry_days}
                onChange={(e) => setConfig(prev => ({ ...prev, password_expiry_days: parseInt(e.target.value) }))}
                min={30}
                max={365}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">0 = nunca expira</p>
            </div>

            {/* Indicador de Força da Senha */}
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Força da Política</span>
                <span className="text-sm text-gray-600">{getStrengthText(strength)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all ${getStrengthColor(strength)}`}
                  style={{ width: `${strength}%` }}
                ></div>
              </div>
              <div className="mt-2">
                <p className="text-xs text-gray-600">Requisitos ativos:</p>
                <ul className="text-xs text-gray-500 mt-1">
                  {requirements.map((req, index) => (
                    <li key={index}>• {req}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Controle de Acesso */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <Key className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Controle de Acesso</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Máximo de Tentativas de Login
              </label>
              <input
                type="number"
                value={config.max_login_attempts}
                onChange={(e) => setConfig(prev => ({ ...prev, max_login_attempts: parseInt(e.target.value) }))}
                min={3}
                max={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duração do Bloqueio (minutos)
              </label>
              <input
                type="number"
                value={config.lockout_duration}
                onChange={(e) => setConfig(prev => ({ ...prev, lockout_duration: parseInt(e.target.value) }))}
                min={5}
                max={1440}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timeout de Sessão (minutos)
              </label>
              <input
                type="number"
                value={config.session_timeout}
                onChange={(e) => setConfig(prev => ({ ...prev, session_timeout: parseInt(e.target.value) }))}
                min={30}
                max={2880}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="require_2fa"
                checked={config.require_2fa}
                onChange={(e) => setConfig(prev => ({ ...prev, require_2fa: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="require_2fa" className="ml-2 text-sm text-gray-900">
                Exigir Autenticação de Dois Fatores (2FA)
              </label>
            </div>
          </div>
        </div>

        {/* Auditoria */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <Eye className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Auditoria e Logs</h3>
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="audit_login"
                checked={config.audit_login_attempts}
                onChange={(e) => setConfig(prev => ({ ...prev, audit_login_attempts: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="audit_login" className="ml-2 text-sm text-gray-900">
                Auditar tentativas de login
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="audit_changes"
                checked={config.audit_data_changes}
                onChange={(e) => setConfig(prev => ({ ...prev, audit_data_changes: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="audit_changes" className="ml-2 text-sm text-gray-900">
                Auditar alterações de dados
              </label>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
                <p className="text-sm text-blue-800">
                  Logs de auditoria ajudam a rastrear atividades suspeitas e garantir conformidade.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Permissões Padrão */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center mb-4">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <UserCheck className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">Permissões Padrão</h3>
          </div>

          <div className="space-y-3">
            <p className="text-sm text-gray-600">
              Selecione as permissões que novos usuários terão por padrão:
            </p>
            
            {Object.entries(
              availablePermissions.reduce((acc, perm) => {
                if (!acc[perm.category]) acc[perm.category] = [];
                acc[perm.category].push(perm);
                return acc;
              }, {} as Record<string, typeof availablePermissions>)
            ).map(([category, perms]) => (
              <div key={category} className="border border-gray-200 rounded-lg p-3">
                <h4 className="font-medium text-gray-900 mb-2">{category}</h4>
                <div className="space-y-1">
                  {perms.map((perm) => (
                    <div key={perm.id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={perm.id}
                        checked={config.default_user_permissions.includes(perm.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setConfig(prev => ({
                              ...prev,
                              default_user_permissions: [...prev.default_user_permissions, perm.id]
                            }));
                          } else {
                            setConfig(prev => ({
                              ...prev,
                              default_user_permissions: prev.default_user_permissions.filter(p => p !== perm.id)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={perm.id} className="ml-2 text-sm text-gray-900">
                        {perm.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
