'use client';

import React, { useState } from 'react';
import { Mail, Phone, MessageSquare, Zap, CheckCircle, XCircle, AlertTriangle, ExternalLink } from 'lucide-react';
import OutlookConfigSection from './OutlookConfigSection';

interface ConfigStatus {
  outlook_graph: {
    is_configured: boolean;
    message: string;
    required_env_vars?: string[];
  };
}

interface IntegrationsConfigSectionProps {
  configStatus: ConfigStatus | null;
  onConfigUpdate: () => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
}

export default function IntegrationsConfigSection({
  configStatus,
  onConfigUpdate,
  onSuccess,
  onError
}: IntegrationsConfigSectionProps) {
  const [activeIntegration, setActiveIntegration] = useState<'outlook' | '3cx' | 'whatsapp' | null>('outlook');

  const integrations = [
    {
      id: 'outlook',
      name: 'Microsoft Outlook',
      description: 'Integração com Microsoft Graph API para gerenciamento de emails',
      icon: Mail,
      status: configStatus?.outlook_graph.is_configured ? 'configured' : 'not_configured',
      statusText: configStatus?.outlook_graph.is_configured ? 'Configurado' : 'Não Configurado',
      priority: 'high',
      features: ['Leitura de emails', 'Envio de emails', 'Sincronização de contatos', 'Calendário']
    },
    {
      id: '3cx',
      name: '3CX PBX',
      description: 'Integração com sistema de telefonia 3CX para relatórios de chamadas e atendimento',
      icon: Phone,
      status: 'configured',
      statusText: 'Configurado',
      priority: 'high',
      features: ['Relatórios de chamadas', 'Estatísticas de agentes', 'Monitoramento em tempo real', 'CDR', 'Interface de atendimento']
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp Business',
      description: 'Integração com WhatsApp Business API para mensagens',
      icon: MessageSquare,
      status: 'not_configured',
      statusText: 'Não Configurado',
      priority: 'medium',
      features: ['Envio de mensagens', 'Recebimento de mensagens', 'Templates', 'Webhooks']
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'configured':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'partial':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'configured':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-green-100 text-green-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-4">
          <Zap className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-xl font-semibold text-gray-900">Integrações Externas</h2>
        </div>
        <p className="text-gray-600">
          Configure e gerencie as integrações com sistemas externos para expandir as funcionalidades do Amvox Omnichannel.
        </p>
      </div>

      {/* Grid de Integrações */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {integrations.map((integration) => {
          const Icon = integration.icon;
          return (
            <div
              key={integration.id}
              className={`bg-white rounded-lg shadow-sm border-2 transition-all cursor-pointer hover:shadow-md ${
                activeIntegration === integration.id
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setActiveIntegration(integration.id as any)}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg mr-3">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{integration.name}</h3>
                      <div className="flex items-center mt-1">
                        {getStatusIcon(integration.status)}
                        <span className="ml-1 text-sm text-gray-600">{integration.statusText}</span>
                      </div>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(integration.priority)}`}>
                    {integration.priority === 'high' ? 'Alta' : integration.priority === 'medium' ? 'Média' : 'Baixa'}
                  </span>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-600 mb-4">{integration.description}</p>

                {/* Status Badge */}
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(integration.status)} mb-4`}>
                  {getStatusIcon(integration.status)}
                  <span className="ml-1">{integration.statusText}</span>
                </div>

                {/* Features */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Funcionalidades:</h4>
                  <ul className="text-xs text-gray-600 space-y-1">
                    {integration.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Configuração Detalhada */}
      {activeIntegration && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {activeIntegration === 'outlook' && (
            <div className="p-6">
              <OutlookConfigSection
                configStatus={configStatus}
                onConfigUpdate={onConfigUpdate}
              />
            </div>
          )}

          {activeIntegration === '3cx' && (
            <div className="p-6">
              <div className="flex items-center mb-6">
                <Phone className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">Configuração 3CX PBX</h3>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <div>
                    <p className="text-sm text-green-800">
                      <strong>Sistema Configurado:</strong> 3CX integrado com relatórios e atendimento funcionais.
                    </p>
                    <p className="text-xs text-green-700 mt-1">
                      Acesso a relatórios via endpoints e interface de atendimento disponível.
                    </p>
                  </div>
                </div>
              </div>

              {/* Seção de Relatórios */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  Relatórios 3CX
                </h4>

                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Servidor 3CX Externo
                      </label>
                      <input
                        type="text"
                        value="http://ticmobilerb.ddns.net"
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        API Backend Local
                      </label>
                      <input
                        type="text"
                        value="http://localhost:8001/api/v1/threecx"
                        readOnly
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Documentação da API
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value="http://localhost:8001/docs#/3CX"
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
                      />
                      <button
                        onClick={() => window.open('http://localhost:8001/docs#/3CX', '_blank')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Abrir Docs
                      </button>
                    </div>
                  </div>

                  <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start">
                      <svg className="w-5 h-5 text-gray-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <p className="text-sm text-gray-800 font-medium">Arquitetura da Integração</p>
                        <p className="text-xs text-gray-600 mt-1">
                          Nossa API backend se conecta ao servidor 3CX externo ({`http://ticmobilerb.ddns.net`})
                          e expõe endpoints padronizados para o frontend. Isso garante segurança e controle de acesso.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Endpoints Disponíveis
                    </label>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <p className="text-sm text-blue-800 font-medium">Endpoints da API 3CX</p>
                          <p className="text-xs text-blue-700 mt-1">
                            Os relatórios do 3CX são acessados através da nossa API backend que se conecta ao servidor 3CX.
                            Todos os endpoints estão disponíveis através do nosso sistema.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-sm text-gray-700">Relatório de Chamadas</span>
                        <div className="flex items-center space-x-2">
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">/api/v1/threecx/calls</code>
                          <button
                            onClick={() => window.open('http://localhost:8001/api/v1/threecx/calls', '_blank')}
                            className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 transition-colors"
                          >
                            Testar
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-sm text-gray-700">Status dos Agentes</span>
                        <div className="flex items-center space-x-2">
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">/api/v1/threecx/agents</code>
                          <button
                            onClick={() => window.open('http://localhost:8001/api/v1/threecx/agents', '_blank')}
                            className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 transition-colors"
                          >
                            Testar
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-sm text-gray-700">Detalhes de Chamada</span>
                        <div className="flex items-center space-x-2">
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">/api/v1/threecx/calls/{`{call_id}`}</code>
                          <button
                            onClick={() => window.open('http://localhost:8001/api/v1/threecx/calls/example-id', '_blank')}
                            className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200 transition-colors"
                          >
                            Testar
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-sm text-gray-700">Webhook 3CX</span>
                        <div className="flex items-center space-x-2">
                          <code className="text-xs bg-gray-100 px-2 py-1 rounded">/api/v1/threecx/webhook</code>
                          <span className="text-xs text-gray-500 px-2 py-1">POST only</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Seção de Atendimento */}
              <div className="mb-8">
                <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  Interface de Atendimento
                </h4>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      URL de Acesso ao Atendimento
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value="https://callcentermobile.ddns.net/gdacx/gmag"
                        readOnly
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
                      />
                      <button
                        onClick={() => window.open('https://callcentermobile.ddns.net/gdacx/gmag', '_blank')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Abrir
                      </button>
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-start">
                      <svg className="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <div>
                        <p className="text-sm text-blue-800 font-medium">Acesso Integrado</p>
                        <p className="text-xs text-blue-700 mt-1">
                          A interface de atendimento está integrada na seção "Atendimento → 3CX - Gerenciador de Chamadas" do sistema.
                          Use as credenciais: <strong>usuário: teste_amvox, senha: 1111</strong>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Configurações Atuais</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">URL Base:</span>
                      <span className="font-mono text-gray-900">Configurado via ENV</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">API Key:</span>
                      <span className="font-mono text-gray-900">Configurado via ENV</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tenant ID:</span>
                      <span className="font-mono text-gray-900">Configurado via ENV</span>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <ExternalLink className="h-5 w-5 text-blue-600 mr-3" />
                    <div>
                      <p className="text-sm text-blue-800">
                        <strong>Próximos Passos:</strong> Interface de configuração será implementada em uma próxima versão.
                      </p>
                      <p className="text-xs text-blue-700 mt-1">
                        Por enquanto, as configurações devem ser feitas no arquivo docker-compose.yml.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeIntegration === 'whatsapp' && (
            <div className="p-6">
              <div className="flex items-center mb-6">
                <MessageSquare className="h-6 w-6 text-blue-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">Configuração WhatsApp Business</h3>
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <XCircle className="h-5 w-5 text-gray-500" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-gray-800">
                      <strong>Não Implementado:</strong> A integração com WhatsApp Business API será desenvolvida em uma próxima versão.
                    </p>
                    <p className="text-xs text-gray-600 mt-1">
                      Funcionalidades planejadas: envio/recebimento de mensagens, templates, webhooks e relatórios.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
