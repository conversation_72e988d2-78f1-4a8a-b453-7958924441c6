'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ConfigStatus {
  outlook_graph: {
    is_configured: boolean;
    message: string;
    required_env_vars?: string[];
  };
}

interface OutlookConfigSectionProps {
  configStatus: ConfigStatus | null;
  onConfigUpdate: () => void;
}

export default function OutlookConfigSection({ configStatus, onConfigUpdate }: OutlookConfigSectionProps) {
  const { token, isAdmin } = useAuth();
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [isSavingGraphConfig, setIsSavingGraphConfig] = useState(false);
  const [graphConfigForm, setGraphConfigForm] = useState({
    clientId: '',
    clientSecret: '',
    tenantId: '',
    showClientSecret: false
  });
  const [graphConfigSaveResult, setGraphConfigSaveResult] = useState<any>(null);

  const testConnection = async () => {
    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const response = await fetch('http://localhost:8001/api/v1/outlook/config/status');
      const result = await response.json();
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, message: 'Erro ao testar conexão' });
    } finally {
      setIsTestingConnection(false);
    }
  };



  const saveGraphConfig = async () => {
    if (!graphConfigForm.clientId || !graphConfigForm.clientSecret || !graphConfigForm.tenantId) {
      setGraphConfigSaveResult({
        success: false,
        message: 'Por favor, preencha todos os campos do Microsoft Graph'
      });
      return;
    }

    if (!isAdmin) {
      setGraphConfigSaveResult({
        success: false,
        message: 'Apenas administradores podem configurar o Microsoft Graph'
      });
      return;
    }

    if (!token) {
      setGraphConfigSaveResult({
        success: false,
        message: 'Token de autenticação não encontrado. Faça login novamente.'
      });
      return;
    }

    setIsSavingGraphConfig(true);
    setGraphConfigSaveResult(null);

    try {
      console.log('🔄 Salvando configuração Microsoft Graph...');
      console.log('🔑 Token disponível:', !!token);
      console.log('👤 É admin:', isAdmin);

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      console.log('📡 Headers da requisição:', headers);

      const response = await fetch('/api/v1/outlook/graph-config', {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          client_id: graphConfigForm.clientId,
          client_secret: graphConfigForm.clientSecret,
          tenant_id: graphConfigForm.tenantId
        }),
      });

      console.log('📡 Status da resposta:', response.status);
      console.log('📡 Headers da resposta:', Object.fromEntries(response.headers.entries()));

      const result = await response.json();
      console.log('📡 Resultado da resposta:', result);

      if (response.ok) {
        console.log('✅ Configuração salva com sucesso');
        setGraphConfigSaveResult({
          success: true,
          message: 'Configuração do Microsoft Graph salva com sucesso!'
        });
        setGraphConfigForm({
          clientId: '',
          clientSecret: '',
          tenantId: '',
          showClientSecret: false
        });
        onConfigUpdate(); // Atualizar status da configuração
      } else {
        console.log('❌ Erro ao salvar configuração:', result);
        setGraphConfigSaveResult({
          success: false,
          message: result.detail || 'Erro ao salvar configuração'
        });
      }
    } catch (error) {
      setGraphConfigSaveResult({
        success: false,
        message: 'Erro ao conectar com o servidor'
      });
    } finally {
      setIsSavingGraphConfig(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho da Seção */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Integração Microsoft Outlook</h2>
        <p className="mt-2 text-gray-600">
          Configure a integração com Microsoft Outlook para gerenciar emails no sistema.
        </p>
      </div>

      {/* Status da Integração */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Status da Integração</h3>

        <div className="border-2 border-blue-500 bg-blue-50 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 8L10.89 13.26C11.2 13.09 11.54 13 11.89 13C12.24 13 12.58 13.09 12.89 13.26L21 8V6L12 11L3 6V8ZM0 4V20C0 21.1 0.9 22 2 22H22C23.1 22 24 21.1 24 20V4C24 2.9 23.1 2 22 2H2C0.9 2 0 2.9 0 4Z"/>
            </svg>
            <span className="font-medium text-gray-900">Microsoft Graph API</span>
          </div>
          <p className="text-sm text-gray-600 mb-3">
            Integração oficial da Microsoft com OAuth2, máxima segurança e funcionalidades completas.
          </p>
          <div className="flex items-center text-sm">
            <div className={`w-2 h-2 rounded-full mr-2 ${
              configStatus?.outlook_graph.is_configured ? 'bg-green-400' : 'bg-red-400'
            }`}></div>
            <span className={
              configStatus?.outlook_graph.is_configured ? 'text-green-600' : 'text-red-600'
            }>
              {configStatus?.outlook_graph.is_configured ? 'Configurado' : 'Não configurado'}
            </span>
          </div>
        </div>
      </div>

      {/* Configuração Microsoft Graph API - Apenas para Administradores */}
      {isAdmin && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Configuração Microsoft Graph API</h3>
          <p className="text-sm text-gray-600 mb-6">
            Configure as credenciais do Azure AD para habilitar a integração com o Microsoft Outlook via Graph API.
          </p>

        <div className="space-y-4">
          {/* Client ID */}
          <div>
            <label htmlFor="graph-client-id" className="block text-sm font-medium text-gray-700 mb-2">
              Client ID (Application ID)
            </label>
            <div className="relative">
              <input
                type="text"
                id="graph-client-id"
                value={graphConfigForm.clientId}
                onChange={(e) => setGraphConfigForm(prev => ({ ...prev, clientId: e.target.value }))}
                placeholder="12345678-1234-1234-1234-123456789012"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              ID da aplicação registrada no Azure AD
            </p>
          </div>

          {/* Client Secret */}
          <div>
            <label htmlFor="graph-client-secret" className="block text-sm font-medium text-gray-700 mb-2">
              Client Secret (Application Secret)
            </label>
            <div className="relative">
              <input
                type={graphConfigForm.showClientSecret ? "text" : "password"}
                id="graph-client-secret"
                value={graphConfigForm.clientSecret}
                onChange={(e) => setGraphConfigForm(prev => ({ ...prev, clientSecret: e.target.value }))}
                placeholder="Digite o client secret da aplicação"
                className="block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
              />
              <button
                type="button"
                onClick={() => setGraphConfigForm(prev => ({ ...prev, showClientSecret: !prev.showClientSecret }))}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                {graphConfigForm.showClientSecret ? (
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Secret da aplicação gerado no Azure AD
            </p>
          </div>

          {/* Tenant ID */}
          <div>
            <label htmlFor="graph-tenant-id" className="block text-sm font-medium text-gray-700 mb-2">
              Tenant ID (Directory ID)
            </label>
            <div className="relative">
              <input
                type="text"
                id="graph-tenant-id"
                value={graphConfigForm.tenantId}
                onChange={(e) => setGraphConfigForm(prev => ({ ...prev, tenantId: e.target.value }))}
                placeholder="87654321-4321-4321-4321-210987654321"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-2m-2 0H7m14 0V9a2 2 0 00-2-2M9 7h6m-6 4h6m-6 4h6" />
                </svg>
              </div>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              ID do diretório (tenant) do Azure AD
            </p>
          </div>

          {/* Resultado da operação */}
          {graphConfigSaveResult && (
            <div className={`rounded-md p-4 ${
              graphConfigSaveResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  {graphConfigSaveResult.success ? (
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    graphConfigSaveResult.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {graphConfigSaveResult.message}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Botões de Ação */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={async () => {
                if (!isAdmin) {
                  setGraphConfigSaveResult({
                    success: false,
                    message: 'Apenas administradores podem recarregar a configuração'
                  });
                  return;
                }

                if (!token) {
                  setGraphConfigSaveResult({
                    success: false,
                    message: 'Token de autenticação não encontrado'
                  });
                  return;
                }

                try {
                  const response = await fetch('/api/v1/outlook/reload-config', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${token}`,
                    },
                  });
                  const result = await response.json();
                  setGraphConfigSaveResult(result);
                  if (result.success) {
                    onConfigUpdate();
                  }
                } catch (error) {
                  setGraphConfigSaveResult({
                    success: false,
                    message: 'Erro ao recarregar configuração'
                  });
                }
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Recarregar Config
            </button>

            <button
              onClick={async () => {
                if (!isAdmin) {
                  setGraphConfigSaveResult({
                    success: false,
                    message: 'Apenas administradores podem remover a configuração'
                  });
                  return;
                }

                if (!token) {
                  setGraphConfigSaveResult({
                    success: false,
                    message: 'Token de autenticação não encontrado'
                  });
                  return;
                }

                try {
                  const response = await fetch('/api/v1/outlook/graph-config', {
                    method: 'DELETE',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${token}`,
                    },
                  });
                  const result = await response.json();
                  setGraphConfigSaveResult(result);
                  if (result.success) {
                    setGraphConfigForm({
                      clientId: '',
                      clientSecret: '',
                      tenantId: '',
                      showClientSecret: false
                    });
                    onConfigUpdate();
                  }
                } catch (error) {
                  setGraphConfigSaveResult({
                    success: false,
                    message: 'Erro ao remover configuração'
                  });
                }
              }}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Remover Configuração
            </button>

            <button
              onClick={saveGraphConfig}
              disabled={isSavingGraphConfig || !graphConfigForm.clientId || !graphConfigForm.clientSecret || !graphConfigForm.tenantId}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSavingGraphConfig ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Salvando...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                  </svg>
                  Salvar Configuração
                </>
              )}
            </button>
          </div>

          {/* Informações sobre Azure AD */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Como obter essas credenciais
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Acesse o <a href="https://portal.azure.com" target="_blank" rel="noopener noreferrer" className="underline">Portal do Azure</a></li>
                    <li>Vá para "Azure Active Directory" → "App registrations"</li>
                    <li>Clique em "New registration" ou selecione uma aplicação existente</li>
                    <li>Copie o "Application (client) ID" e "Directory (tenant) ID"</li>
                    <li>Em "Certificates & secrets", crie um novo "Client secret"</li>
                    <li>Configure as permissões necessárias para Microsoft Graph</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      )}





      {/* Resultado do Teste */}
      {testResult && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Resultado do Teste</h3>
          <div className={`p-4 rounded-md ${
            testResult.success || testResult.is_configured
              ? 'bg-green-50 border border-green-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <pre className="text-sm whitespace-pre-wrap">
              {JSON.stringify(testResult, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
