'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface CallsByHourChartProps {
  data: {
    hour: string;
    total: number;
    answered: number;
    missed: number;
  }[];
  height?: number;
  className?: string;
}

const CallsByHourChart: React.FC<CallsByHourChartProps> = ({
  data,
  height = 300,
  className
}) => {
  // Preparar os dados para o gráfico
  const chartData: ChartData<'bar'> = {
    labels: data.map(item => item.hour),
    datasets: [
      {
        label: 'Total de Chamadas',
        data: data.map(item => item.total),
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        borderColor: 'rgba(53, 162, 235, 1)',
        borderWidth: 1,
      },
      {
        label: 'Chamadas Atendidas',
        data: data.map(item => item.answered),
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
      {
        label: '<PERSON><PERSON><PERSON>',
        data: data.map(item => item.missed),
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Distribuição de Chamadas por Hora',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value}`;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Hora do Dia'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Número de Chamadas'
        }
      }
    }
  };

  return (
    <Chart
      type="bar"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default CallsByHourChart;
