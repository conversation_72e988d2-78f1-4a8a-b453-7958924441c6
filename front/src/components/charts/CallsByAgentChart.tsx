'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface CallsByAgentChartProps {
  data: {
    Agente: string;
    Total: number;
    TempoMedio: string;
  }[];
  height?: number;
  className?: string;
  maxAgents?: number;
}

const CallsByAgentChart: React.FC<CallsByAgentChartProps> = ({
  data,
  height = 300,
  className,
  maxAgents = 10
}) => {
  // Ordenar os dados por total de chamadas (decrescente) e limitar ao número máximo de agentes
  const sortedData = [...data]
    .sort((a, b) => b.Total - a.Total)
    .slice(0, maxAgents);

  // Converter o tempo médio de string (HH:MM:SS) para segundos
  const timeToSeconds = (timeStr: string): number => {
    const parts = timeStr.split(':');
    if (parts.length === 3) {
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    }
    return 0;
  };

  // Preparar os dados para o gráfico
  const chartData: ChartData<'bar'> = {
    labels: sortedData.map(item => item.Agente),
    datasets: [
      {
        label: 'Total de Chamadas',
        data: sortedData.map(item => item.Total),
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        borderColor: 'rgba(53, 162, 235, 1)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: 'Tempo Médio (segundos)',
        data: sortedData.map(item => timeToSeconds(item.TempoMedio)),
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        borderColor: 'rgba(255, 159, 64, 1)',
        borderWidth: 1,
        yAxisID: 'y1',
        type: 'line' as const,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Chamadas por Agente',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            if (label.includes('Tempo Médio')) {
              // Converter segundos de volta para formato HH:MM:SS
              const hours = Math.floor(value / 3600);
              const minutes = Math.floor((value % 3600) / 60);
              const seconds = value % 60;
              const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
              return `Tempo Médio: ${formattedTime}`;
            }
            return `${label}: ${value}`;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Agente'
        }
      },
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        beginAtZero: true,
        title: {
          display: true,
          text: 'Número de Chamadas'
        }
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        beginAtZero: true,
        grid: {
          drawOnChartArea: false,
        },
        title: {
          display: true,
          text: 'Tempo Médio (segundos)'
        }
      }
    }
  };

  return (
    <Chart
      type="bar"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default CallsByAgentChart;
