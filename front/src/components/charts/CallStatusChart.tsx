'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface CallStatusChartProps {
  data: {
    answered: number;
    missed: number;
    failed: number;
    noAnswer: number;
  };
  height?: number;
  className?: string;
}

const CallStatusChart: React.FC<CallStatusChartProps> = ({
  data,
  height = 300,
  className
}) => {
  // Preparar os dados para o gráfico de pizza
  const chartData: ChartData<'doughnut'> = {
    labels: ['Atendidas', 'Perdidas', 'Falharam', 'Sem Resposta'],
    datasets: [
      {
        data: [data.answered, data.missed, data.failed, data.noAnswer],
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',   // Verde para atendidas
          'rgba(255, 99, 132, 0.8)',   // Vermelho para perdidas
          'rgba(255, 159, 64, 0.8)',   // Laranja para falharam
          'rgba(255, 206, 86, 0.8)',   // Amarelo para sem resposta
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 159, 64, 1)',
          'rgba(255, 206, 86, 1)',
        ],
        borderWidth: 2,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'doughnut'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: true,
        text: 'Distribuição de Status das Chamadas',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0';
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    cutout: '60%', // Para fazer um gráfico de donut
  };

  return (
    <Chart
      type="doughnut"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default CallStatusChart;
