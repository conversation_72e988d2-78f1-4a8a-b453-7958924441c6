'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface CallTrendChartProps {
  data: {
    Data: string;
    Recebidas: number;
    Atendidas: number;
    'Não-Atendidas': number;
    'Nível de serviço'?: string;
    'Taxa de Atendidas'?: string;
  }[];
  height?: number;
  className?: string;
}

const CallTrendChart: React.FC<CallTrendChartProps> = ({
  data,
  height = 300,
  className
}) => {
  // Ordenar os dados por data
  const sortedData = [...data].sort((a, b) => new Date(a.Data).getTime() - new Date(b.Data).getTime());

  // Preparar os dados para o gráfico de linha
  const chartData: ChartData<'line'> = {
    labels: sortedData.map(item => {
      // Formatar a data para exibição
      const date = new Date(item.Data);
      return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    }),
    datasets: [
      {
        label: 'Total de Chamadas',
        data: sortedData.map(item => item.Recebidas),
        borderColor: 'rgba(53, 162, 235, 1)',
        backgroundColor: 'rgba(53, 162, 235, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
      },
      {
        label: 'Chamadas Atendidas',
        data: sortedData.map(item => item.Atendidas),
        borderColor: 'rgba(75, 192, 192, 1)',
        backgroundColor: 'rgba(75, 192, 192, 0.1)',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
      },
      {
        label: 'Chamadas Perdidas',
        data: sortedData.map(item => item['Não-Atendidas']),
        borderColor: 'rgba(255, 99, 132, 1)',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        borderWidth: 2,
        fill: false,
        tension: 0.4,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'line'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Tendência de Chamadas ao Longo do Tempo',
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          afterBody: function(context) {
            const index = context[0].dataIndex;
            const item = sortedData[index];
            return [
              `Nível de Serviço: ${item['Nível de serviço'] || 'N/A'}`,
              `Taxa de Atendimento: ${item['Taxa de Atendidas'] || 'N/A'}`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Data'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Número de Chamadas'
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    }
  };

  return (
    <Chart
      type="line"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default CallTrendChart;
