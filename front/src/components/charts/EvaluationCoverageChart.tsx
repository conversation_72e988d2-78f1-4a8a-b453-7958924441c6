'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface EvaluationCoverageData {
  agentName: string;
  totalCalls: number;
  evaluatedCalls: number;
  unevaluatedCalls: number;
  evaluationRate: number;
}

interface EvaluationCoverageChartProps {
  data: EvaluationCoverageData[];
  height?: number;
  className?: string;
  maxAgents?: number;
}

const EvaluationCoverageChart: React.FC<EvaluationCoverageChartProps> = ({
  data,
  height = 300,
  className,
  maxAgents = 10
}) => {
  // Ordenar os dados por total de chamadas (decrescente) e limitar ao número máximo de agentes
  const sortedData = [...data]
    .sort((a, b) => b.totalCalls - a.totalCalls)
    .slice(0, maxAgents);

  // Preparar os dados para o gráfico
  const chartData: ChartData<'bar'> = {
    labels: sortedData.map(item => item.agentName),
    datasets: [
      {
        label: '<PERSON><PERSON><PERSON>',
        data: sortedData.map(item => item.evaluatedCalls),
        backgroundColor: 'rgba(75, 192, 192, 0.8)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
      {
        label: 'Chamadas Não Avaliadas',
        data: sortedData.map(item => item.unevaluatedCalls),
        backgroundColor: 'rgba(255, 159, 64, 0.8)',
        borderColor: 'rgba(255, 159, 64, 1)',
        borderWidth: 1,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Cobertura de Avaliações por Agente',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value}`;
          },
          afterBody: function(context) {
            const index = context[0].dataIndex;
            const item = sortedData[index];
            return [
              `Total de Chamadas: ${item.totalCalls}`,
              `Taxa de Avaliação: ${item.evaluationRate.toFixed(1)}%`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Agente'
        },
        stacked: true
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Número de Chamadas'
        },
        stacked: true
      }
    }
  };

  return (
    <Chart
      type="bar"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default EvaluationCoverageChart;
