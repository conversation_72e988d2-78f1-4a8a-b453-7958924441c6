'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface SatisfactionChartProps {
  data: {
    Agente: string;
    Avaliadas: number;
    Satisfeito?: number;
    Insatisfeito?: number;
    Sim?: number;
    Não?: number;
    'Nota 1'?: number;
    'Nota 2'?: number;
    'Nota 3'?: number;
    'Nota 4'?: number;
    'Nota 5'?: number;
    '% Avaliadas'?: string;
    '% Meta(2.0)'?: string;
    '% Meta(5.0)'?: string;
  }[];
  type: 'atendente' | 'chamada' | 'empresa';
  height?: number;
  className?: string;
  maxAgents?: number;
}

const SatisfactionChart: React.FC<SatisfactionChartProps> = ({
  data,
  type,
  height = 300,
  className,
  maxAgents = 10
}) => {
  // Ordenar os dados por número de avaliações (decrescente) e limitar ao número máximo de agentes
  const sortedData = [...data]
    .sort((a, b) => b.Avaliadas - a.Avaliadas)
    .slice(0, maxAgents);

  // Preparar os dados para o gráfico com base no tipo
  let chartData: ChartData<'bar' | 'pie'>;
  let options: ChartOptions<'bar' | 'pie'>;

  if (type === 'atendente') {
    // Gráfico de satisfação do atendente (Satisfeito/Insatisfeito)
    chartData = {
      labels: sortedData.map(item => item.Agente),
      datasets: [
        {
          label: 'Satisfeito',
          data: sortedData.map(item => item.Satisfeito || 0),
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
        {
          label: 'Insatisfeito',
          data: sortedData.map(item => item.Insatisfeito || 0),
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        }
      ]
    };

    options = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Satisfação com o Atendente',
        },
        tooltip: {
          callbacks: {
            afterBody: function(context) {
              const index = context[0].dataIndex;
              const item = sortedData[index];
              return [
                `Total Avaliadas: ${item.Avaliadas}`,
                `% Avaliadas: ${item['% Avaliadas'] || 'N/A'}`,
                `% Meta: ${item['% Meta(2.0)'] || 'N/A'}`
              ];
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Agente'
          }
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Número de Avaliações'
          },
          stacked: true
        }
      }
    };
  } else if (type === 'chamada') {
    // Gráfico de satisfação da chamada (Sim/Não)
    chartData = {
      labels: sortedData.map(item => item.Agente),
      datasets: [
        {
          label: 'Sim',
          data: sortedData.map(item => item.Sim || 0),
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
        {
          label: 'Não',
          data: sortedData.map(item => item.Não || 0),
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        }
      ]
    };

    options = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Satisfação com a Resolução da Chamada',
        },
        tooltip: {
          callbacks: {
            afterBody: function(context) {
              const index = context[0].dataIndex;
              const item = sortedData[index];
              return [
                `Total Avaliadas: ${item.Avaliadas}`,
                `% Avaliadas: ${item['% Avaliadas'] || 'N/A'}`,
                `% Meta: ${item['% Meta(2.0)'] || 'N/A'}`
              ];
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Agente'
          }
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Número de Avaliações'
          },
          stacked: true
        }
      }
    };
  } else {
    // Gráfico de satisfação da empresa (Notas 1-5)
    chartData = {
      labels: sortedData.map(item => item.Agente),
      datasets: [
        {
          label: 'Nota 5',
          data: sortedData.map(item => item['Nota 5'] || 0),
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
        {
          label: 'Nota 4',
          data: sortedData.map(item => item['Nota 4'] || 0),
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
        {
          label: 'Nota 3',
          data: sortedData.map(item => item['Nota 3'] || 0),
          backgroundColor: 'rgba(255, 206, 86, 0.5)',
          borderColor: 'rgba(255, 206, 86, 1)',
          borderWidth: 1,
        },
        {
          label: 'Nota 2',
          data: sortedData.map(item => item['Nota 2'] || 0),
          backgroundColor: 'rgba(255, 159, 64, 0.5)',
          borderColor: 'rgba(255, 159, 64, 1)',
          borderWidth: 1,
        },
        {
          label: 'Nota 1',
          data: sortedData.map(item => item['Nota 1'] || 0),
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        }
      ]
    };

    options = {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: 'Avaliação da Empresa',
        },
        tooltip: {
          callbacks: {
            afterBody: function(context) {
              const index = context[0].dataIndex;
              const item = sortedData[index];
              return [
                `Total Avaliadas: ${item.Avaliadas}`,
                `% Meta: ${item['% Meta(5.0)'] || 'N/A'}`
              ];
            }
          }
        }
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Agente'
          }
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Número de Avaliações'
          },
          stacked: true
        }
      }
    };
  }

  return (
    <Chart
      type="bar"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default SatisfactionChart;
