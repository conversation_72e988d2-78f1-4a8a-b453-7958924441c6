'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface EvaluationDistributionChartProps {
  totalCalls: number;
  evaluatedCalls: number;
  height?: number;
  className?: string;
}

const EvaluationDistributionChart: React.FC<EvaluationDistributionChartProps> = ({
  totalCalls,
  evaluatedCalls,
  height = 300,
  className
}) => {
  const unevaluatedCalls = totalCalls - evaluatedCalls;
  const evaluationRate = totalCalls > 0 ? (evaluatedCalls / totalCalls) * 100 : 0;

  // Preparar os dados para o gráfico de pizza
  const chartData: ChartData<'doughnut'> = {
    labels: ['Chamadas Avaliadas', 'Chamadas Não Avaliadas'],
    datasets: [
      {
        data: [evaluatedCalls, unevaluatedCalls],
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',   // Verde para avaliadas
          'rgba(255, 159, 64, 0.8)',   // Laranja para não avaliadas
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 159, 64, 1)',
        ],
        borderWidth: 2,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'doughnut'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: true,
        text: 'Distribuição Geral de Avaliações',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = totalCalls > 0 ? ((value / totalCalls) * 100).toFixed(1) : '0';
            return `${label}: ${value} (${percentage}%)`;
          },
          afterLabel: function(context) {
            if (context.dataIndex === 0) {
              return `Taxa de Avaliação: ${evaluationRate.toFixed(1)}%`;
            }
            return '';
          }
        }
      }
    },
    cutout: '60%', // Para fazer um gráfico de donut
  };

  return (
    <div className="flex flex-col items-center">
      <Chart
        type="doughnut"
        data={chartData}
        options={options}
        height={height}
        className={className}
      />
      <div className="mt-4 text-center">
        <div className="text-2xl font-bold text-blue-600">
          {evaluationRate.toFixed(1)}%
        </div>
        <div className="text-sm text-gray-600">
          Taxa de Avaliação
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {evaluatedCalls} de {totalCalls} chamadas avaliadas
        </div>
      </div>
    </div>
  );
};

export default EvaluationDistributionChart;
