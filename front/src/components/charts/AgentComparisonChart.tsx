'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface AgentData {
  // Dados de distribuição
  totalCalls?: number;
  answeredCalls?: number;
  missedCalls?: number;
  averageDuration?: string;
  
  // Dados de satisfação
  totalEvaluations?: number;
  satisfiedCount?: number;
  dissatisfiedCount?: number;
  satisfactionRate?: number;
}

interface AgentComparisonChartProps {
  agents: {
    name: string;
    data: AgentData;
  }[];
  metric: 'totalCalls' | 'answeredCalls' | 'missedCalls' | 'averageDuration' | 'satisfactionRate';
  height?: number;
  className?: string;
}

const AgentComparisonChart: React.FC<AgentComparisonChartProps> = ({
  agents,
  metric,
  height = 300,
  className
}) => {
  // Função para converter tempo (HH:MM:SS) para segundos
  const timeToSeconds = (timeStr: string): number => {
    if (!timeStr) return 0;
    const parts = timeStr.split(':');
    if (parts.length === 3) {
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    }
    return 0;
  };

  // Preparar os dados para o gráfico com base na métrica selecionada
  let chartData: ChartData<'bar' | 'line'>;
  let options: ChartOptions<'bar' | 'line'>;
  let chartType: 'bar' | 'line' = 'bar';

  // Definir título e configurações com base na métrica
  let title = '';
  let yAxisLabel = '';
  let backgroundColor = 'rgba(53, 162, 235, 0.5)';
  let borderColor = 'rgba(53, 162, 235, 1)';
  
  // Preparar os dados com base na métrica selecionada
  let data: number[] = [];
  
  switch (metric) {
    case 'totalCalls':
      title = 'Total de Chamadas por Agente';
      yAxisLabel = 'Número de Chamadas';
      backgroundColor = 'rgba(53, 162, 235, 0.5)';
      borderColor = 'rgba(53, 162, 235, 1)';
      data = agents.map(agent => agent.data.totalCalls || 0);
      break;
      
    case 'answeredCalls':
      title = 'Chamadas Atendidas por Agente';
      yAxisLabel = 'Número de Chamadas';
      backgroundColor = 'rgba(75, 192, 192, 0.5)';
      borderColor = 'rgba(75, 192, 192, 1)';
      data = agents.map(agent => agent.data.answeredCalls || 0);
      break;
      
    case 'missedCalls':
      title = 'Chamadas Perdidas por Agente';
      yAxisLabel = 'Número de Chamadas';
      backgroundColor = 'rgba(255, 99, 132, 0.5)';
      borderColor = 'rgba(255, 99, 132, 1)';
      data = agents.map(agent => agent.data.missedCalls || 0);
      break;
      
    case 'averageDuration':
      title = 'Tempo Médio de Chamada por Agente';
      yAxisLabel = 'Tempo (segundos)';
      backgroundColor = 'rgba(255, 159, 64, 0.5)';
      borderColor = 'rgba(255, 159, 64, 1)';
      data = agents.map(agent => timeToSeconds(agent.data.averageDuration || '00:00:00'));
      break;
      
    case 'satisfactionRate':
      title = 'Taxa de Satisfação por Agente';
      yAxisLabel = 'Taxa de Satisfação (%)';
      backgroundColor = 'rgba(153, 102, 255, 0.5)';
      borderColor = 'rgba(153, 102, 255, 1)';
      data = agents.map(agent => {
        if (agent.data.totalEvaluations && agent.data.totalEvaluations > 0) {
          return ((agent.data.satisfiedCount || 0) / agent.data.totalEvaluations) * 100;
        }
        return 0;
      });
      break;
  }

  // Ordenar os agentes com base na métrica selecionada (decrescente)
  const sortedAgents = [...agents].sort((a, b) => {
    const valueA = data[agents.indexOf(a)];
    const valueB = data[agents.indexOf(b)];
    return valueB - valueA;
  });

  // Preparar os dados ordenados para o gráfico
  const sortedData = sortedAgents.map(agent => {
    switch (metric) {
      case 'totalCalls':
        return agent.data.totalCalls || 0;
      case 'answeredCalls':
        return agent.data.answeredCalls || 0;
      case 'missedCalls':
        return agent.data.missedCalls || 0;
      case 'averageDuration':
        return timeToSeconds(agent.data.averageDuration || '00:00:00');
      case 'satisfactionRate':
        if (agent.data.totalEvaluations && agent.data.totalEvaluations > 0) {
          return ((agent.data.satisfiedCount || 0) / agent.data.totalEvaluations) * 100;
        }
        return 0;
    }
  });

  chartData = {
    labels: sortedAgents.map(agent => agent.name),
    datasets: [
      {
        label: title,
        data: sortedData,
        backgroundColor,
        borderColor,
        borderWidth: 1,
      }
    ]
  };

  options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: title,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.parsed.y;
            if (metric === 'averageDuration') {
              // Converter segundos de volta para formato HH:MM:SS
              const hours = Math.floor(value / 3600);
              const minutes = Math.floor((value % 3600) / 60);
              const seconds = value % 60;
              const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
              return `Tempo Médio: ${formattedTime}`;
            } else if (metric === 'satisfactionRate') {
              return `Taxa de Satisfação: ${value.toFixed(2)}%`;
            }
            return `${title}: ${value}`;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Agente'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: yAxisLabel
        }
      }
    }
  };

  return (
    <Chart
      type={chartType}
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default AgentComparisonChart;
