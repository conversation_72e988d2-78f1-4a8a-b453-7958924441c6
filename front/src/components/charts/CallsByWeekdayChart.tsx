'use client';

import React from 'react';
import Chart from './Chart';
import { ChartData, ChartOptions } from 'chart.js';

interface CallsByWeekdayChartProps {
  data: {
    Dia: string;
    Recebidas: number;
    Atendidas: number;
    'Não-Atendidas': number;
    'Nível de serviço'?: string;
    'Taxa de Atendidas'?: string;
  }[];
  height?: number;
  className?: string;
}

const CallsByWeekdayChart: React.FC<CallsByWeekdayChartProps> = ({
  data,
  height = 300,
  className
}) => {
  // Ordenar os dias da semana corretamente
  const weekdayOrder = ['Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado', 'Domingo'];
  const sortedData = [...data].sort((a, b) => {
    return weekdayOrder.indexOf(a.Dia) - weekdayOrder.indexOf(b.Dia);
  });

  // Preparar os dados para o gráfico
  const chartData: ChartData<'bar'> = {
    labels: sortedData.map(item => item.Dia),
    datasets: [
      {
        label: 'Total de Chamadas',
        data: sortedData.map(item => item.Recebidas),
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        borderColor: 'rgba(53, 162, 235, 1)',
        borderWidth: 1,
      },
      {
        label: 'Chamadas Atendidas',
        data: sortedData.map(item => item.Atendidas),
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
      },
      {
        label: 'Chamadas Perdidas',
        data: sortedData.map(item => item['Não-Atendidas']),
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      }
    ]
  };

  // Opções do gráfico
  const options: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Distribuição de Chamadas por Dia da Semana',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value}`;
          },
          afterBody: function(context) {
            const index = context[0].dataIndex;
            const item = sortedData[index];
            return [
              `Nível de Serviço: ${item['Nível de serviço'] || 'N/A'}`,
              `Taxa de Atendimento: ${item['Taxa de Atendidas'] || 'N/A'}`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Dia da Semana'
        }
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Número de Chamadas'
        }
      }
    }
  };

  return (
    <Chart
      type="bar"
      data={chartData}
      options={options}
      height={height}
      className={className}
    />
  );
};

export default CallsByWeekdayChart;
