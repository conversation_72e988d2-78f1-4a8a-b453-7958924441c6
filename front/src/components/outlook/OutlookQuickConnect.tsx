'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface OutlookUser {
  id: string;
  display_name: string;
  email: string;
  user_principal_name: string;
}

interface OutlookStatus {
  authenticated: boolean;
  user_id?: number;
  user_info?: OutlookUser;
  status: string;
  system_user?: {
    id: number;
    login: string;
    nome: string;
    email_corporativo: string;
  };
  message?: string;
}

interface OutlookQuickConnectProps {
  onStatusChange?: (status: OutlookStatus) => void;
  showUserInfo?: boolean;
  compact?: boolean;
}

export default function OutlookQuickConnect({
  onStatusChange,
  showUserInfo = true,
  compact = false
}: OutlookQuickConnectProps) {
  const { token } = useAuth();
  const [status, setStatus] = useState<OutlookStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkOutlookStatus();
  }, [token]);

  useEffect(() => {
    if (status && onStatusChange) {
      onStatusChange(status);
    }
  }, [status]); // Removido onStatusChange para evitar loop

  const checkOutlookStatus = async () => {
    if (!token) {
      setIsLoading(false);
      setStatus({
        authenticated: false,
        status: 'disconnected',
        message: 'Token não encontrado'
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Timeout reduzido para melhor UX
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/v1/outlook/user/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const statusData = await response.json();
        setStatus(statusData);
        console.log('✅ Status Outlook obtido:', statusData);
      } else {
        const errorText = await response.text();
        console.log('❌ Erro ao verificar status:', response.status, errorText);

        // Se não autenticado, definir status padrão
        setStatus({
          authenticated: false,
          status: 'disconnected',
          message: 'Não conectado ao Outlook'
        });
      }
    } catch (err) {
      console.log('❌ Erro na requisição:', err);

      // Definir status padrão em caso de erro
      setStatus({
        authenticated: false,
        status: 'disconnected',
        message: 'Erro ao verificar conexão'
      });

      if (err.name === 'AbortError') {
        setError('Timeout ao verificar status - tente novamente');
      } else {
        setError('Erro de conexão - verifique sua internet');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!token) {
      setError('Token de autenticação não encontrado');
      return;
    }

    try {
      setIsConnecting(true);
      setError(null);

      console.log('🔗 Iniciando conexão Outlook...');

      // Usar endpoint do frontend para evitar CORS
      const response = await fetch('/api/v1/outlook/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('🔗 URL de autenticação obtida:', data);

        if (data.auth_url) {
          // Redirecionar na mesma aba
          console.log('🔗 Redirecionando para autenticação Microsoft...');
          window.location.href = data.auth_url;
        } else {
          setError('URL de autenticação não encontrada');
        }
      } else {
        const errorText = await response.text();
        console.log('❌ Erro ao obter URL de autenticação:', errorText);
        setError('Erro ao iniciar autenticação. Tente novamente.');
      }
    } catch (err) {
      console.log('❌ Erro na conexão:', err);
      setError('Erro de conexão. Verifique sua internet e tente novamente.');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!token) {
      setError('Token de autenticação não encontrado');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('http://localhost:8001/api/v1/outlook/auth/disconnect', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Desconectado com sucesso:', result);

        // Atualizar status
        await checkOutlookStatus();
      } else {
        const errorText = await response.text();
        console.error('❌ Erro ao desconectar:', errorText);
        setError('Erro ao desconectar');
      }
    } catch (err) {
      console.error('❌ Erro na desconexão:', err);
      setError('Erro ao desconectar');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-2 ${compact ? 'p-2' : 'p-4'}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span className="text-sm text-gray-600">Verificando conexão...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg ${compact ? 'p-2' : 'p-4'}`}>
        <div className="flex items-center">
          <svg className="w-4 h-4 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span className="text-sm text-red-700">{error}</span>
        </div>
        <button
          onClick={checkOutlookStatus}
          className="mt-2 text-xs text-red-600 hover:text-red-800 underline"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  if (!status) {
    return (
      <div className={`text-center ${compact ? 'p-2' : 'p-4'}`}>
        <span className="text-sm text-gray-500">Status não disponível</span>
      </div>
    );
  }

  return (
    <div className={`bg-white border rounded-lg ${compact ? 'p-3' : 'p-4'}`}>
      {/* Status da Conexão */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            status.authenticated ? 'bg-green-400' : 'bg-gray-400'
          }`}></div>
          <span className={`text-sm font-medium ${
            status.authenticated ? 'text-green-700' : 'text-gray-700'
          }`}>
            {status.authenticated ? 'Conectado' : 'Desconectado'}
          </span>
        </div>

        {status.authenticated ? (
          <button
            onClick={handleDisconnect}
            disabled={isLoading}
            className="text-xs text-red-600 hover:text-red-800 underline disabled:opacity-50"
          >
            Desconectar
          </button>
        ) : (
          <button
            onClick={handleConnect}
            disabled={isConnecting}
            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isConnecting ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                Conectando...
              </>
            ) : (
              'Conectar Outlook'
            )}
          </button>
        )}
      </div>

      {/* Informações do Usuário */}
      {showUserInfo && status.authenticated && status.user_info && (
        <div className="bg-blue-50 rounded-lg p-3 mt-3">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-medium">
                  {status.user_info.display_name.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-blue-900 truncate">
                {status.user_info.display_name}
              </p>
              <p className="text-xs text-blue-700 truncate">
                {status.user_info.email}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Mensagem de Status */}
      {status.message && !status.authenticated && (
        <div className="mt-3 text-xs text-gray-600 text-center">
          {status.message}
        </div>
      )}
    </div>
  );
}
