'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface OutlookUser {
  id: number; // ID do sistema (integer), não UUID do Microsoft
  email: string;
  name: string;
  is_connected: boolean;
}

interface EmailMessage {
  id: string;
  subject: string;
  sender: string;
  sender_email: string;
  body: string;
  body_html?: string;
  date: string;
  is_read: boolean;
  has_attachments: boolean;
}

interface EmailFolder {
  id: string;
  display_name: string;
  total_item_count: number;
}

export default function OutlookIsolatedWorkspace() {
  const { token } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [user, setUser] = useState<OutlookUser | null>(null);
  const [emails, setEmails] = useState<EmailMessage[]>([]);
  const [folders, setFolders] = useState<EmailFolder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState('inbox');
  const [selectedEmail, setSelectedEmail] = useState<EmailMessage | null>(null);
  const [activeTab, setActiveTab] = useState<'inbox' | 'compose' | 'sent'>('inbox');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Refs para evitar múltiplas chamadas simultâneas
  const loadingRef = useRef(false);
  const lastConnectionCheck = useRef<number>(0);
  const connectionCheckCooldown = 10000; // 10 segundos entre verificações

  // Estados para composição
  const [composeForm, setComposeForm] = useState({
    to: '',
    cc: '',
    bcc: '',
    subject: '',
    body: '',
    is_html: true
  });
  const [isSending, setIsSending] = useState(false);

  // Estados para modal de confirmação de exclusão
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [emailToDelete, setEmailToDelete] = useState<string | null>(null);

  // Função para verificar status da conexão com cooldown
  const checkConnectionStatus = useCallback(async (retryCount = 0, forceCheck = false): Promise<void> => {
    const now = Date.now();

    // Verificar cooldown (exceto se forçado ou for retry)
    if (!forceCheck && retryCount === 0 && (now - lastConnectionCheck.current) < connectionCheckCooldown) {
      console.log('Verificação de status em cooldown, pulando...');
      return;
    }

    if (loadingRef.current && retryCount === 0) return;

    const maxRetries = 2; // Reduzido para evitar spam
    const retryDelay = Math.min(2000 * Math.pow(2, retryCount), 8000); // Exponential backoff

    try {
      if (retryCount === 0) {
        loadingRef.current = true;
        lastConnectionCheck.current = now;
      }

      const response = await fetch('/api/v1/outlook/user/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const status = await response.json();
        setIsConnected(status.authenticated || false);
        if (status.user_info && status.user_id) {
          console.log('🔧 DEBUG: Definindo user com ID:', status.user_id, 'tipo:', typeof status.user_id);
          const newUser = {
            id: status.user_id, // Usar o user_id do sistema, não o UUID do Microsoft
            email: status.user_info.email,
            name: status.user_info.name,
            is_connected: status.authenticated
          };
          console.log('🔧 DEBUG: Novo user object:', newUser);
          setUser(newUser);
        }
        setError(null);
      } else if (response.status === 401) {
        // Token expirado ou inválido - estado normal
        setIsConnected(false);
        setUser(null);
        setError(null);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      console.error(`Erro ao verificar status (tentativa ${retryCount + 1}):`, err);

      if (retryCount < maxRetries) {
        // Retry automático com delay exponencial
        setTimeout(() => {
          checkConnectionStatus(retryCount + 1, true); // Force no retry
        }, retryDelay);
        return;
      } else {
        // Falha após todas as tentativas - não definir como erro crítico
        console.warn('Falha persistente ao verificar status, mantendo estado atual');
        setIsConnected(false);
        setUser(null);
      }
    } finally {
      if (retryCount === 0) {
        loadingRef.current = false;
      }
    }
  }, [token]);

  // Função para carregar emails (isolada)
  const loadEmails = useCallback(async () => {
    console.log('🔧 DEBUG: loadEmails chamado com user:', user, 'user.id:', user?.id, 'tipo:', typeof user?.id);
    if (!user?.id || loadingRef.current || !isConnected) return;

    try {
      loadingRef.current = true;
      setIsLoading(true);
      setError(null);

      // Carregar pastas
      const foldersResponse = await fetch(`/api/v1/outlook/folders?user_id=${user.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (foldersResponse.ok) {
        const foldersData = await foldersResponse.json();
        setFolders(foldersData);
      } else if (foldersResponse.status === 401) {
        console.log('Token expirado ao carregar pastas - desconectando');
        setIsConnected(false);
        setUser(null);
        setError('Sessão expirada. Reconecte sua conta.');
        return;
      }

      // Carregar emails
      const emailsResponse = await fetch(`/api/v1/outlook/messages/list?user_id=${user.id}&folder_id=${selectedFolder}&limit=20`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (emailsResponse.ok) {
        const emailsData = await emailsResponse.json();
        const processedEmails = emailsData.map((msg: any) => ({
          id: msg.id,
          subject: msg.subject || '(Sem assunto)',
          sender: msg.from?.email_address?.name || msg.from?.email_address?.address || 'Desconhecido',
          sender_email: msg.from?.email_address?.address || '',
          body: msg.body_content || '',
          body_html: msg.body_content_type === 'html' ? msg.body_content : null,
          date: msg.received_date_time || msg.sent_date_time,
          is_read: msg.is_read || false,
          has_attachments: msg.attachments?.length > 0 || false,
        }));

        setEmails(processedEmails);
        setLastUpdate(new Date());
      } else if (emailsResponse.status === 401) {
        console.log('Token expirado ao carregar emails - desconectando');
        setIsConnected(false);
        setUser(null);
        setError('Sessão expirada. Reconecte sua conta.');
        return;
      } else {
        const errorData = await emailsResponse.json().catch(() => ({ detail: 'Erro desconhecido' }));
        console.error('Erro detalhado ao carregar emails:', {
          status: emailsResponse.status,
          statusText: emailsResponse.statusText,
          error: errorData
        });
        throw new Error(errorData.detail || `Erro ${emailsResponse.status}: ${emailsResponse.statusText}`);
      }
    } catch (err) {
      console.error('Erro ao carregar emails:', err);
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(`Erro ao carregar emails: ${errorMessage}`);
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [user?.id, selectedFolder, token, isConnected]);

  // Função para conectar ao Outlook
  const handleConnect = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch('/api/v1/outlook/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.auth_url) {
          // Redirecionar na mesma aba para manter o contexto
          window.location.href = result.auth_url;
        }
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Erro desconhecido' }));
        throw new Error(errorData.detail || 'Erro ao iniciar autenticação');
      }
    } catch (err) {
      console.error('Erro na conexão:', err);
      setError(`Erro ao conectar com Outlook: ${err.message}`);
    }
  }, [token]);

  // Função para desconectar
  const handleDisconnect = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch('/api/v1/outlook/auth/disconnect', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setIsConnected(false);
          setUser(null);
          setEmails([]);
          setFolders([]);
          setSelectedEmail(null);
        }
      }
    } catch (err) {
      console.error('Erro ao desconectar:', err);
      setError('Erro ao desconectar. Tente novamente.');
    }
  }, [token]);

  // Função para enviar email
  const handleSendEmail = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id || !composeForm.to || !composeForm.subject) {
      setError('Preencha todos os campos obrigatórios');
      return;
    }

    try {
      setIsSending(true);
      setError(null);

      const emailData = {
        to: composeForm.to.split(',').map(email => email.trim()),
        cc: composeForm.cc ? composeForm.cc.split(',').map(email => email.trim()) : [],
        bcc: composeForm.bcc ? composeForm.bcc.split(',').map(email => email.trim()) : [],
        subject: composeForm.subject,
        body: composeForm.body,
        body_type: composeForm.is_html ? 'HTML' : 'Text',
      };

      const response = await fetch(`/api/v1/outlook/send?user_id=${user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(emailData),
      });

      if (response.ok) {
        setComposeForm({
          to: '',
          cc: '',
          bcc: '',
          subject: '',
          body: '',
          is_html: true
        });
        setActiveTab('inbox');
        // Recarregar emails após envio
        setTimeout(() => loadEmails(), 1000);
      } else {
        throw new Error('Erro ao enviar email');
      }
    } catch (err) {
      console.error('Erro ao enviar email:', err);
      setError('Erro ao enviar email. Tente novamente.');
    } finally {
      setIsSending(false);
    }
  }, [user?.id, composeForm, token, loadEmails]);

  // Verificar status inicial e parâmetros da URL
  useEffect(() => {
    if (token) {
      // LIMPEZA FORÇADA: Resetar todos os estados
      console.log('🧹 LIMPEZA FORÇADA: Resetando todos os estados');
      setUser(null);
      setIsConnected(false);
      setEmails([]);
      setFolders([]);
      setSelectedEmail(null);
      setError(null);

      // Limpar dados antigos do localStorage que podem estar causando problemas
      localStorage.removeItem('outlook_user_id');
      localStorage.removeItem('outlook_user_email');
      localStorage.removeItem('outlook_user_name');
      localStorage.removeItem('outlook_authenticated');
      localStorage.removeItem('outlook_status');

      // Verificar se veio do callback de autenticação
      const urlParams = new URLSearchParams(window.location.search);
      const outlookAuth = urlParams.get('outlook_auth');

      if (outlookAuth === 'success') {
        // Aguardar um pouco para o backend processar e então verificar status
        setTimeout(() => {
          checkConnectionStatus(0, true); // Force check após callback
        }, 2000);
      } else {
        checkConnectionStatus(0, true); // Force check inicial
      }
    }
  }, [token]); // Removido checkConnectionStatus para evitar re-renders

  // Carregar emails quando conectado (apenas uma vez por mudança)
  useEffect(() => {
    if (isConnected && user?.id && !loadingRef.current) {
      loadEmails();
    }
  }, [isConnected, user?.id, selectedFolder]);

  // Polling inteligente para verificar mudanças de status (menos agressivo)
  useEffect(() => {
    if (!token) return;

    let pollInterval: NodeJS.Timeout;
    let consecutiveErrors = 0;
    const maxConsecutiveErrors = 3;

    const smartPoll = () => {
      // Não fazer polling se já estiver carregando ou se houver muitos erros consecutivos
      if (loadingRef.current || consecutiveErrors >= maxConsecutiveErrors) {
        pollInterval = setTimeout(smartPoll, 60000); // 1 minuto de pausa
        return;
      }

      checkConnectionStatus()
        .then(() => {
          consecutiveErrors = 0; // Reset error count on success
          // Polling muito menos frequente
          const interval = isConnected ? 300000 : 60000; // 5 min se conectado, 1 min se não
          pollInterval = setTimeout(smartPoll, interval);
        })
        .catch((error) => {
          console.error('Erro no polling:', error);
          consecutiveErrors++;

          // Aumentar intervalo progressivamente com erros
          const errorInterval = Math.min(30000 * consecutiveErrors, 120000); // Max 2 minutos
          pollInterval = setTimeout(smartPoll, errorInterval);
        });
    };

    // Iniciar polling após um delay inicial
    pollInterval = setTimeout(smartPoll, 5000); // 5 segundos inicial

    return () => {
      if (pollInterval) {
        clearTimeout(pollInterval);
      }
    };
  }, [token, isConnected]); // Removido checkConnectionStatus para evitar re-renders

  // Função para abrir modal de confirmação de exclusão
  const handleDeleteEmail = useCallback((emailId: string, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setEmailToDelete(emailId);
    setShowDeleteConfirm(true);
  }, []);

  // Função para confirmar exclusão
  const confirmDeleteEmail = useCallback(async () => {
    if (!emailToDelete) return;

    try {
      setError(null);
      const response = await fetch(`http://localhost:8001/api/v1/outlook/messages/${emailToDelete}?user_id=${user?.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Remover email da lista local
        setEmails(prev => prev.filter(email => email.id !== emailToDelete));

        // Se o email excluído estava selecionado, fechar o modal
        if (selectedEmail?.id === emailToDelete) {
          setSelectedEmail(null);
        }

        // Fechar modal de confirmação
        setShowDeleteConfirm(false);
        setEmailToDelete(null);
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Erro desconhecido' }));
        throw new Error(errorData.detail || 'Erro ao excluir email');
      }
    } catch (err) {
      console.error('Erro ao excluir email:', err);
      setError(`Erro ao excluir email: ${err.message}`);
      // Fechar modal mesmo em caso de erro
      setShowDeleteConfirm(false);
      setEmailToDelete(null);
    }
  }, [token, user?.id, selectedEmail, emailToDelete]);

  // Função para cancelar exclusão
  const cancelDeleteEmail = useCallback(() => {
    setShowDeleteConfirm(false);
    setEmailToDelete(null);
  }, []);

  // Função para marcar como lido/não lido
  const handleMarkAsRead = useCallback(async (emailId: string, isRead: boolean, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }

    try {
      setError(null);
      const response = await fetch(`http://localhost:8001/api/v1/outlook/messages/read?user_id=${user?.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message_id: emailId,
          is_read: isRead,
        }),
      });

      if (response.ok) {
        // Atualizar email na lista local
        setEmails(prev => prev.map(email =>
          email.id === emailId
            ? { ...email, is_read: isRead }
            : email
        ));

        // Se o email está selecionado, atualizar também
        if (selectedEmail?.id === emailId) {
          setSelectedEmail(prev => prev ? { ...prev, is_read: isRead } : null);
        }
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Erro desconhecido' }));
        throw new Error(errorData.detail || 'Erro ao marcar email');
      }
    } catch (err) {
      console.error('Erro ao marcar email:', err);
      setError(`Erro ao marcar email: ${err.message}`);
    }
  }, [token, user?.id, selectedEmail]);

  // Se não estiver conectado, mostrar tela de conexão
  if (!isConnected || !user) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            Conectar ao Microsoft Outlook
          </h3>

          <p className="text-gray-600 mb-6">
            Para acessar seus emails, conecte sua conta Microsoft Outlook corporativa. A autenticação será feita nesta mesma aba.
          </p>

          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <button
            onClick={handleConnect}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            Conectar Outlook
          </button>

          <p className="text-xs text-gray-500 mt-4">
            Você será redirecionado para a Microsoft e retornará automaticamente
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com informações do usuário */}
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-sm">
                {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{user.name}</h3>
              <p className="text-sm text-gray-600">{user.email}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {lastUpdate && (
              <span className="text-xs text-gray-500">
                Atualizado: {lastUpdate.toLocaleTimeString('pt-BR')}
              </span>
            )}
            <button
              onClick={loadEmails}
              disabled={isLoading}
              className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
              title="Atualizar"
            >
              <svg className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            <button
              onClick={handleDisconnect}
              className="text-xs text-red-600 hover:text-red-700 transition-colors"
            >
              Desconectar
            </button>
          </div>
        </div>
      </div>

      {/* Navegação por abas */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('inbox')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'inbox'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7" />
            </svg>
            Caixa de Entrada ({emails.length})
          </button>

          <button
            onClick={() => setActiveTab('compose')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'compose'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <svg className="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
            Novo Email
          </button>
        </nav>
      </div>

      {/* Conteúdo das abas */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Aba Caixa de Entrada */}
      {activeTab === 'inbox' && (
        <div className="space-y-4">
          {/* Barra informativa sobre funcionalidades */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm text-blue-800 font-medium">
                  💡 Dica: Use os botões ao lado direito de cada email para marcar como lido ou excluir
                </span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-blue-700">
                <span className="flex items-center space-x-1 px-2 py-1 bg-blue-100 rounded">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span>Marcar lido</span>
                </span>
                <span className="flex items-center space-x-1 px-2 py-1 bg-red-100 rounded">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  <span>Excluir</span>
                </span>
              </div>
            </div>
          </div>

          {/* Seletor de pasta */}
          {folders.length > 0 && (
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">Pasta:</label>
              <select
                value={selectedFolder}
                onChange={(e) => setSelectedFolder(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-blue-500 focus:border-blue-500"
              >
                {folders.map((folder) => (
                  <option key={folder.id} value={folder.id}>
                    {folder.display_name} ({folder.total_item_count})
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Lista de emails */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Carregando emails...</p>
            </div>
          ) : emails.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 009.586 13H7" />
              </svg>
              <p className="text-gray-600">Nenhum email encontrado</p>
            </div>
          ) : (
            <div className="space-y-2">
              {emails.map((email) => (
                <div
                  key={email.id}
                  onClick={() => setSelectedEmail(email)}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                    !email.is_read ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className={`text-sm truncate ${!email.is_read ? 'font-semibold' : 'font-medium'}`}>
                          {email.subject}
                        </h4>
                        {!email.is_read && (
                          <span className="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 truncate">
                        De: {email.sender}
                      </p>
                    </div>

                    {/* BOTÕES DE AÇÃO - MUITO VISÍVEIS */}
                    <div className="flex items-center space-x-2 ml-4">
                      {/* Botão Marcar como lido/não lido */}
                      <button
                        onClick={(e) => handleMarkAsRead(email.id, !email.is_read, e)}
                        className={`px-3 py-2 rounded-lg border-2 font-medium text-sm transition-all duration-200 shadow-sm ${
                          email.is_read
                            ? 'text-gray-600 border-gray-400 bg-gray-50 hover:bg-gray-100 hover:border-gray-500'
                            : 'text-blue-700 border-blue-500 bg-blue-50 hover:bg-blue-100 hover:border-blue-600'
                        }`}
                        title={email.is_read ? 'Marcar como não lido' : 'Marcar como lido'}
                      >
                        <div className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                          <span className="hidden sm:inline">
                            {email.is_read ? 'Não lido' : 'Lido'}
                          </span>
                        </div>
                      </button>

                      {/* Botão Excluir */}
                      <button
                        onClick={(e) => handleDeleteEmail(email.id, e)}
                        className="px-3 py-2 rounded-lg border-2 border-red-500 bg-red-50 text-red-700 hover:bg-red-100 hover:border-red-600 font-medium text-sm transition-all duration-200 shadow-sm"
                        title="Excluir email"
                      >
                        <div className="flex items-center space-x-1">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          <span className="hidden sm:inline">Excluir</span>
                        </div>
                      </button>

                      {/* Data */}
                      <div className="text-xs text-gray-500 ml-2">
                        {new Date(email.date).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Aba Composição */}
      {activeTab === 'compose' && (
        <form onSubmit={handleSendEmail} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Para *
              </label>
              <input
                type="email"
                required
                value={composeForm.to}
                onChange={(e) => setComposeForm(prev => ({ ...prev, to: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                CC
              </label>
              <input
                type="email"
                value={composeForm.cc}
                onChange={(e) => setComposeForm(prev => ({ ...prev, cc: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assunto *
            </label>
            <input
              type="text"
              required
              value={composeForm.subject}
              onChange={(e) => setComposeForm(prev => ({ ...prev, subject: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Assunto do email"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mensagem
            </label>
            <textarea
              value={composeForm.body}
              onChange={(e) => setComposeForm(prev => ({ ...prev, body: e.target.value }))}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="Digite sua mensagem..."
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={composeForm.is_html}
                onChange={(e) => setComposeForm(prev => ({ ...prev, is_html: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Formato HTML</span>
            </label>

            <div className="space-x-3">
              <button
                type="button"
                onClick={() => setActiveTab('inbox')}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isSending}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isSending ? 'Enviando...' : 'Enviar Email'}
              </button>
            </div>
          </div>
        </form>
      )}

      {/* Modal de visualização de email */}
      {selectedEmail && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 truncate">
                  {selectedEmail.subject}
                </h3>
                <button
                  onClick={() => setSelectedEmail(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                <p>De: {selectedEmail.sender}</p>
                <p>Data: {new Date(selectedEmail.date).toLocaleString('pt-BR')}</p>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {selectedEmail.body_html ? (
                <div dangerouslySetInnerHTML={{ __html: selectedEmail.body_html }} />
              ) : (
                <pre className="whitespace-pre-wrap font-sans text-gray-900">
                  {selectedEmail.body}
                </pre>
              )}
            </div>

            <div className="p-6 border-t border-gray-200">
              <button
                onClick={() => setSelectedEmail(null)}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors"
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmação de Exclusão */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900">
                    Confirmar Exclusão
                  </h3>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-sm text-gray-600">
                  Tem certeza que deseja excluir este email? O email será movido para a pasta de itens excluídos e poderá ser recuperado posteriormente.
                </p>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteEmail}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={confirmDeleteEmail}
                  className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                >
                  Excluir Email
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
