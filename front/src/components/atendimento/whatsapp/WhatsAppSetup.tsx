'use client';

import React, { useState, useEffect } from 'react';

interface WhatsAppSetupProps {
  instanceName: string;
  onConnect: () => void;
}

export default function WhatsAppSetup({ instanceName, onConnect }: WhatsAppSetupProps) {
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'initial' | 'creating' | 'qr' | 'connecting'>('initial');

  const createInstance = async () => {
    setIsLoading(true);
    setError(null);
    setStep('creating');

    try {
      // Criar instância
      const response = await fetch('/api/v1/whatsapp/instances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          instance_name: instanceName,
          webhook_url: `${window.location.origin}/api/v1/whatsapp/webhook`
        }),
      });

      if (!response.ok) {
        throw new Error('Falha ao criar instância');
      }

      // Aguardar um pouco e obter QR code
      setTimeout(async () => {
        await getQRCode();
      }, 2000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      setStep('initial');
    } finally {
      setIsLoading(false);
    }
  };

  const getQRCode = async () => {
    try {
      setStep('qr');
      const response = await fetch(`/api/v1/whatsapp/instances/${instanceName}/qr`);
      
      if (response.ok) {
        const data = await response.json();
        setQrCode(data.qr_code);
        
        // Verificar status periodicamente
        checkConnectionStatus();
      } else {
        throw new Error('QR Code não disponível');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao obter QR Code');
      setStep('initial');
    }
  };

  const checkConnectionStatus = () => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/v1/whatsapp/instances/${instanceName}/status`);
        if (response.ok) {
          const data = await response.json();
          if (data.status === 'connected') {
            clearInterval(interval);
            setStep('connecting');
            setTimeout(() => {
              onConnect();
            }, 1000);
          }
        }
      } catch (error) {
        console.error('Erro ao verificar status:', error);
      }
    }, 3000);

    // Limpar interval após 5 minutos
    setTimeout(() => {
      clearInterval(interval);
      if (step === 'qr') {
        setError('Tempo limite para conexão excedido');
        setStep('initial');
      }
    }, 300000);
  };

  const renderStep = () => {
    switch (step) {
      case 'initial':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-green-100 mb-6">
              <svg className="h-10 w-10 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Conectar WhatsApp Business</h3>
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              Para começar a usar o WhatsApp Business, você precisa conectar sua conta. 
              Clique no botão abaixo para gerar o QR Code.
            </p>
            
            <button
              onClick={createInstance}
              disabled={isLoading}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Conectando...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Conectar WhatsApp
                </>
              )}
            </button>
          </div>
        );

      case 'creating':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-blue-100 mb-6">
              <svg className="animate-spin h-10 w-10 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Criando Instância</h3>
            <p className="text-gray-600">
              Aguarde enquanto criamos sua instância do WhatsApp Business...
            </p>
          </div>
        );

      case 'qr':
        return (
          <div className="text-center">
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Escaneie o QR Code</h3>
            <p className="text-gray-600 mb-6">
              Abra o WhatsApp no seu celular, vá em <strong>Dispositivos conectados</strong> e escaneie este código:
            </p>
            
            {qrCode ? (
              <div className="flex justify-center mb-6">
                <div className="bg-white p-4 rounded-lg shadow-lg border">
                  <img 
                    src={`data:image/png;base64,${qrCode}`} 
                    alt="QR Code WhatsApp" 
                    className="w-64 h-64"
                  />
                </div>
              </div>
            ) : (
              <div className="flex justify-center mb-6">
                <div className="bg-gray-100 p-4 rounded-lg w-64 h-64 flex items-center justify-center">
                  <svg className="animate-spin h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              </div>
            )}
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-blue-700">
                  Aguardando conexão... O QR Code expira em 5 minutos.
                </p>
              </div>
            </div>
          </div>
        );

      case 'connecting':
        return (
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-green-100 mb-6">
              <svg className="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">Conectado com Sucesso!</h3>
            <p className="text-gray-600">
              Seu WhatsApp Business foi conectado. Redirecionando para o chat...
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-2xl mx-auto py-8">
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}
      
      {renderStep()}
    </div>
  );
}
