'use client';

import React, { useState, useEffect } from 'react';
import WhatsAppChat from './whatsapp/WhatsAppChat';
import WhatsAppSetup from './whatsapp/WhatsAppSetup';

export default function WhatsAppSection() {
  const [isConnected, setIsConnected] = useState(false);
  const [instanceName, setInstanceName] = useState('amvox_instance');
  const [stats, setStats] = useState({
    total_conversations: 0,
    active_conversations: 0,
    waiting_conversations: 0,
    unread_messages: 0
  });

  // Verificar status da instância ao carregar
  useEffect(() => {
    checkInstanceStatus();
    loadStats();
  }, []);

  const checkInstanceStatus = async () => {
    try {
      const response = await fetch(`/api/v1/whatsapp/instances/${instanceName}/status`);
      if (response.ok) {
        const data = await response.json();
        setIsConnected(data.status === 'connected');
      }
    } catch (error) {
      console.error('Erro ao verificar status:', error);
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch('/api/v1/whatsapp/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <svg className="w-6 h-6 mr-2 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                  </svg>
                  WhatsApp Business
                </h2>
                <p className="text-sm text-gray-500">
                  {isConnected ? 'Conectado e pronto para atender' : 'Aguardando integração'}
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.total_conversations}</div>
                <div className="text-xs text-gray-500">Conversas</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.waiting_conversations}</div>
                <div className="text-xs text-gray-500">Pendentes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.unread_messages}</div>
                <div className="text-xs text-gray-500">Não lidas</div>
              </div>
            </div>
            <button
              onClick={() => window.location.reload()}
              className={`inline-flex items-center px-3 py-2 border shadow-sm text-sm leading-4 font-medium rounded-md ${
                isConnected
                  ? 'border-green-300 text-green-700 bg-green-50 hover:bg-green-100'
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {isConnected ? 'Atualizar' : 'Conectar'}
            </button>
          </div>
        </div>
      </div>

      {/* Conteúdo Principal */}
      <div className="p-6">
        {isConnected ? (
          <WhatsAppChat
            instanceName={instanceName}
            onStatsUpdate={setStats}
          />
        ) : (
          <WhatsAppSetup
            instanceName={instanceName}
            onConnect={() => {
              setIsConnected(true);
              loadStats();
            }}
          />
        )}
      </div>
    </div>
  );
}
