'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import OutlookIsolatedWorkspace from '@/components/outlook/OutlookIsolatedWorkspace';

export default function EmailSection() {
  const { token } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    console.log('🎯 EmailSection inicializado');
    setIsInitialized(true);
  }, []);

  if (!isInitialized) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Inicializando sistema de email...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <svg className="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          Email - Microsoft Outlook
        </h2>
        <p className="text-sm text-gray-600 mt-1">
          Workspace isolado para gerenciamento de emails corporativos
        </p>
      </div>
      
      <div className="p-6">
        <OutlookIsolatedWorkspace />
      </div>
    </div>
  );
}
