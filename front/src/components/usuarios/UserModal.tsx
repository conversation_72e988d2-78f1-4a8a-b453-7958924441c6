'use client';

import React, { useState, useEffect } from 'react';
import { X, Save, Eye, EyeOff, AlertCircle } from 'lucide-react';

interface Usuario {
  id?: number;
  nome: string;
  sobrenome: string;
  login: string;
  nivel_usuario: 'administrador' | 'agente';
  email_corporativo: string;
  ativo: boolean;
  data_criacao?: string;
  ultimo_login?: string;
}

interface UserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (userData: any) => Promise<void>;
  user?: Usuario | null;
  isLoading: boolean;
  mode: 'create' | 'edit';
}

export default function UserModal({ 
  isOpen, 
  onClose, 
  onSave, 
  user, 
  isLoading, 
  mode 
}: UserModalProps) {
  const [formData, setFormData] = useState({
    nome: '',
    sobrenome: '',
    login: '',
    senha: '',
    nivel_usuario: 'agente' as 'administrador' | 'agente',
    email_corporativo: '',
    senha_email_corporativo: '',
    ativo: true
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showEmailPassword, setShowEmailPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Resetar formulário quando modal abre/fecha ou usuário muda
  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && user) {
        setFormData({
          nome: user.nome || '',
          sobrenome: user.sobrenome || '',
          login: user.login || '',
          senha: '', // Sempre vazio para edição
          nivel_usuario: user.nivel_usuario || 'agente',
          email_corporativo: user.email_corporativo || '',
          senha_email_corporativo: '', // Sempre vazio para edição
          ativo: user.ativo ?? true
        });
      } else {
        setFormData({
          nome: '',
          sobrenome: '',
          login: '',
          senha: '',
          nivel_usuario: 'agente',
          email_corporativo: '',
          senha_email_corporativo: '',
          ativo: true
        });
      }
      setErrors({});
    }
  }, [isOpen, mode, user]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.nome.trim()) {
      newErrors.nome = 'Nome é obrigatório';
    }

    if (!formData.sobrenome.trim()) {
      newErrors.sobrenome = 'Sobrenome é obrigatório';
    }

    if (!formData.login.trim()) {
      newErrors.login = 'Login é obrigatório';
    } else if (formData.login.length < 3) {
      newErrors.login = 'Login deve ter pelo menos 3 caracteres';
    }

    if (mode === 'create' && !formData.senha) {
      newErrors.senha = 'Senha é obrigatória';
    } else if (formData.senha && formData.senha.length < 6) {
      newErrors.senha = 'Senha deve ter pelo menos 6 caracteres';
    }

    if (!formData.email_corporativo.trim()) {
      newErrors.email_corporativo = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email_corporativo)) {
      newErrors.email_corporativo = 'Email inválido';
    }

    if (mode === 'create' && !formData.senha_email_corporativo) {
      newErrors.senha_email_corporativo = 'Senha do email é obrigatória';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Preparar dados para envio
    const submitData = { ...formData };
    
    // Para edição, remover campos vazios de senha
    if (mode === 'edit') {
      if (!submitData.senha) {
        delete submitData.senha;
      }
      if (!submitData.senha_email_corporativo) {
        delete submitData.senha_email_corporativo;
      }
    }

    await onSave(submitData);
  };

  const handleClose = () => {
    setFormData({
      nome: '',
      sobrenome: '',
      login: '',
      senha: '',
      nivel_usuario: 'agente',
      email_corporativo: '',
      senha_email_corporativo: '',
      ativo: true
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'create' ? 'Criar Usuário' : `Editar ${user?.nome}`}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Nome e Sobrenome */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome *
              </label>
              <input
                type="text"
                value={formData.nome}
                onChange={(e) => setFormData({...formData, nome: e.target.value})}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.nome ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Digite o nome"
              />
              {errors.nome && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.nome}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sobrenome *
              </label>
              <input
                type="text"
                value={formData.sobrenome}
                onChange={(e) => setFormData({...formData, sobrenome: e.target.value})}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.sobrenome ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Digite o sobrenome"
              />
              {errors.sobrenome && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.sobrenome}
                </p>
              )}
            </div>
          </div>

          {/* Login e Nível */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Login *
              </label>
              <input
                type="text"
                value={formData.login}
                onChange={(e) => setFormData({...formData, login: e.target.value.toLowerCase()})}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.login ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Digite o login"
              />
              {errors.login && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.login}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nível de Usuário
              </label>
              <select
                value={formData.nivel_usuario}
                onChange={(e) => setFormData({...formData, nivel_usuario: e.target.value as 'administrador' | 'agente'})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="agente">Agente</option>
                <option value="administrador">Administrador</option>
              </select>
            </div>
          </div>

          {/* Senha */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {mode === 'create' ? 'Senha *' : 'Nova Senha (deixe vazio para manter atual)'}
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={formData.senha}
                onChange={(e) => setFormData({...formData, senha: e.target.value})}
                className={`w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.senha ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={mode === 'create' ? 'Digite a senha' : 'Digite nova senha (opcional)'}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {errors.senha && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.senha}
              </p>
            )}
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Corporativo *
            </label>
            <input
              type="email"
              value={formData.email_corporativo}
              onChange={(e) => setFormData({...formData, email_corporativo: e.target.value})}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors.email_corporativo ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Digite o email corporativo"
            />
            {errors.email_corporativo && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.email_corporativo}
              </p>
            )}
          </div>

          {/* Senha do Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {mode === 'create' ? 'Senha do Email *' : 'Nova Senha do Email (deixe vazio para manter atual)'}
            </label>
            <div className="relative">
              <input
                type={showEmailPassword ? 'text' : 'password'}
                value={formData.senha_email_corporativo}
                onChange={(e) => setFormData({...formData, senha_email_corporativo: e.target.value})}
                className={`w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.senha_email_corporativo ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={mode === 'create' ? 'Digite a senha do email' : 'Digite nova senha do email (opcional)'}
              />
              <button
                type="button"
                onClick={() => setShowEmailPassword(!showEmailPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                {showEmailPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {errors.senha_email_corporativo && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.senha_email_corporativo}
              </p>
            )}
          </div>

          {/* Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="ativo"
              checked={formData.ativo}
              onChange={(e) => setFormData({...formData, ativo: e.target.checked})}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="ativo" className="ml-2 block text-sm text-gray-900">
              Usuário ativo
            </label>
          </div>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-colors"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {mode === 'create' ? 'Criando...' : 'Salvando...'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {mode === 'create' ? 'Criar' : 'Salvar'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
