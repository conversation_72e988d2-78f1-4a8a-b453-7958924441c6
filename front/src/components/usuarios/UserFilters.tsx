'use client';

import React from 'react';
import { Search, Filter, X } from 'lucide-react';

interface UserFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  statusFilter: 'all' | 'active' | 'inactive';
  onStatusFilterChange: (value: 'all' | 'active' | 'inactive') => void;
  levelFilter: 'all' | 'administrador' | 'agente';
  onLevelFilterChange: (value: 'all' | 'administrador' | 'agente') => void;
  onClearFilters: () => void;
  totalUsers: number;
  filteredUsers: number;
}

export default function UserFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  levelFilter,
  onLevelFilterChange,
  onClearFilters,
  totalUsers,
  filteredUsers
}: UserFiltersProps) {
  const hasActiveFilters = searchTerm || statusFilter !== 'all' || levelFilter !== 'all';

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Busca */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Buscar por nome, login ou email..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
            {searchTerm && (
              <button
                onClick={() => onSearchChange('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>

        {/* Filtros */}
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Filtro de Status */}
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={statusFilter}
              onChange={(e) => onStatusFilterChange(e.target.value as 'all' | 'active' | 'inactive')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
            >
              <option value="all">Todos os Status</option>
              <option value="active">Apenas Ativos</option>
              <option value="inactive">Apenas Inativos</option>
            </select>
          </div>

          {/* Filtro de Nível */}
          <div className="flex items-center space-x-2">
            <select
              value={levelFilter}
              onChange={(e) => onLevelFilterChange(e.target.value as 'all' | 'administrador' | 'agente')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"
            >
              <option value="all">Todos os Níveis</option>
              <option value="administrador">Administradores</option>
              <option value="agente">Agentes</option>
            </select>
          </div>

          {/* Limpar Filtros */}
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors flex items-center space-x-1"
            >
              <X className="h-4 w-4" />
              <span>Limpar</span>
            </button>
          )}
        </div>
      </div>

      {/* Contador de resultados */}
      <div className="mt-3 pt-3 border-t border-gray-100">
        <p className="text-sm text-gray-600">
          {hasActiveFilters ? (
            <>
              Mostrando <span className="font-medium text-gray-900">{filteredUsers}</span> de{' '}
              <span className="font-medium text-gray-900">{totalUsers}</span> usuários
              {searchTerm && (
                <span className="ml-2 text-blue-600">
                  • Busca: "{searchTerm}"
                </span>
              )}
            </>
          ) : (
            <>
              Total: <span className="font-medium text-gray-900">{totalUsers}</span> usuários
            </>
          )}
        </p>
      </div>
    </div>
  );
}
