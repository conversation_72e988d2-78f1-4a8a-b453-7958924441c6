import React from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: {
    value: string | number;
    type: 'increase' | 'decrease';
  };
  bgColor?: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  change, 
  bgColor = 'bg-white' 
}) => {
  return (
    <div className={`${bgColor} rounded-lg shadow-md p-6 flex flex-col`}>
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="mt-2 text-3xl font-semibold text-gray-900">{value}</p>
        </div>
        <div className="p-2 rounded-md bg-blue-50 text-blue-600">
          {icon}
        </div>
      </div>
      
      {change && (
        <div className="mt-4">
          <span className={`text-sm font-medium ${
            change.type === 'increase' ? 'text-green-600' : 'text-red-600'
          }`}>
            {change.type === 'increase' ? '↑' : '↓'} {change.value}
          </span>
          <span className="text-sm text-gray-500 ml-1">desde o último período</span>
        </div>
      )}
    </div>
  );
};

export default StatCard;
