'use client';

import React from 'react';

interface MetricsData {
  threecx: {
    total_calls: number;
    active_calls: number;
    answered_calls: number;
    missed_calls: number;
    answer_rate: number;
  };
  whatsapp: {
    total_chats: number;
    active_chats: number;
    waiting_chats: number;
    total_unread_messages: number;
  };
}

interface MetricsChartProps {
  data: MetricsData;
}

const MetricsChart: React.FC<MetricsChartProps> = ({ data }) => {
  // Calcular percentuais para visualização
  const callsAnsweredPercent = data.threecx.total_calls > 0 
    ? (data.threecx.answered_calls / data.threecx.total_calls) * 100 
    : 0;
  
  const callsMissedPercent = data.threecx.total_calls > 0 
    ? (data.threecx.missed_calls / data.threecx.total_calls) * 100 
    : 0;

  const whatsappActivePercent = data.whatsapp.total_chats > 0 
    ? (data.whatsapp.active_chats / data.whatsapp.total_chats) * 100 
    : 0;

  const whatsappWaitingPercent = data.whatsapp.total_chats > 0 
    ? (data.whatsapp.waiting_chats / data.whatsapp.total_chats) * 100 
    : 0;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Distribuição de Atendimentos</h3>
      
      <div className="space-y-6">
        {/* Métricas 3CX */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Chamadas 3CX</span>
            <span className="text-sm text-gray-500">{data.threecx.total_calls} total</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
            <div className="flex h-3 rounded-full overflow-hidden">
              <div 
                className="bg-green-500 transition-all duration-300"
                style={{ width: `${callsAnsweredPercent}%` }}
                title={`${data.threecx.answered_calls} atendidas`}
              ></div>
              <div 
                className="bg-red-500 transition-all duration-300"
                style={{ width: `${callsMissedPercent}%` }}
                title={`${data.threecx.missed_calls} perdidas`}
              ></div>
            </div>
          </div>
          
          <div className="flex justify-between text-xs text-gray-600">
            <span className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Atendidas: {data.threecx.answered_calls}
            </span>
            <span className="flex items-center">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
              Perdidas: {data.threecx.missed_calls}
            </span>
          </div>
        </div>

        {/* Métricas WhatsApp */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Chats WhatsApp</span>
            <span className="text-sm text-gray-500">{data.whatsapp.total_chats} total</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
            <div className="flex h-3 rounded-full overflow-hidden">
              <div 
                className="bg-blue-500 transition-all duration-300"
                style={{ width: `${whatsappActivePercent}%` }}
                title={`${data.whatsapp.active_chats} ativos`}
              ></div>
              <div 
                className="bg-yellow-500 transition-all duration-300"
                style={{ width: `${whatsappWaitingPercent}%` }}
                title={`${data.whatsapp.waiting_chats} aguardando`}
              ></div>
            </div>
          </div>
          
          <div className="flex justify-between text-xs text-gray-600">
            <span className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
              Ativos: {data.whatsapp.active_chats}
            </span>
            <span className="flex items-center">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
              Aguardando: {data.whatsapp.waiting_chats}
            </span>
          </div>
        </div>

        {/* Resumo Geral */}
        <div className="pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {data.threecx.answer_rate.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Taxa de Resposta</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {data.whatsapp.total_unread_messages}
              </div>
              <div className="text-xs text-gray-500">Mensagens Não Lidas</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MetricsChart;
