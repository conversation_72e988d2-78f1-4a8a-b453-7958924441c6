'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface EmailMessage {
  id: string;
  subject: string;
  sender: string;
  date: string;
  is_read: boolean;
  has_attachments: boolean;
}

interface EmailStats {
  total_emails: number;
  unread_emails: number;
  connection_status: string;
}

export default function EmailWidget() {
  const [recentEmails, setRecentEmails] = useState<EmailMessage[]>([]);
  const [stats, setStats] = useState<EmailStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isConfigured, setIsConfigured] = useState(false);

  useEffect(() => {
    loadEmailData();
  }, []);

  const loadEmailData = async () => {
    try {
      setIsLoading(true);

      // Verificar se Outlook está configurado
      const configResponse = await fetch('http://localhost:8001/api/v1/outlook/config/status');
      const configData = await configResponse.json();

      if (!configData.is_configured) {
        setIsConfigured(false);
        setIsLoading(false);
        return;
      }

      setIsConfigured(true);

      // Não tentar carregar emails automaticamente - apenas mostrar que está configurado
      setIsConfigured(true);
      setStats({
        total_emails: 0,
        unread_emails: 0,
        connection_status: 'Configurado - Faça login no Outlook'
      });
      setIsLoading(false);
      return;

      let emailsData: EmailMessage[] = [];

      if (emailsResponse.ok) {
        const messages = await emailsResponse.json();
        emailsData = messages.map((msg: any) => ({
          id: msg.id,
          subject: msg.subject || '(Sem assunto)',
          sender: msg.from?.email_address?.name || msg.from?.email_address?.address || 'Desconhecido',
          date: msg.received_date_time || msg.sent_date_time,
          is_read: msg.is_read || false,
          body: msg.body_content || ''
        }));
        setRecentEmails(emailsData);
      }

      // Calcular estatísticas dos emails carregados
      setStats({
        total_emails: emailsData.length,
        unread_emails: emailsData?.filter((e: EmailMessage) => !e.is_read).length || 0,
        connection_status: 'OK'
      });

    } catch (error) {
      console.error('Erro ao carregar dados de email:', error);
      setIsConfigured(false);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Últimos Emails</h3>
          <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"></div>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!isConfigured) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Últimos Emails</h3>
          <svg className="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 8L10.89 13.26C11.2 13.09 11.54 13 11.89 13C12.24 13 12.58 13.09 12.89 13.26L21 8V6L12 11L3 6V8ZM0 4V20C0 21.1 0.9 22 2 22H22C23.1 22 24 21.1 24 20V4C24 2.9 23.1 2 22 2H2C0.9 2 0 2.9 0 4Z"/>
          </svg>
        </div>

        <div className="text-center py-8">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
            <svg className="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Email não configurado</h3>
          <p className="mt-1 text-sm text-gray-500">
            Configure a integração de email para ver os últimos emails aqui.
          </p>
          <div className="mt-6">
            <Link
              href="/configuracoes"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Configurar Email
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* Cabeçalho */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Últimos Emails</h3>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${
            stats?.connection_status === 'OK' ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          <Link
            href="/atendimento"
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            Ver todos
          </Link>
        </div>
      </div>

      {/* Estatísticas Rápidas */}
      {stats && (
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-900">Total</p>
                <p className="text-lg font-semibold text-blue-900">{stats.total_emails}</p>
              </div>
            </div>
          </div>

          <div className="bg-red-50 rounded-lg p-3">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-900">Não Lidos</p>
                <p className="text-lg font-semibold text-red-900">{stats.unread_emails}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Lista de Emails Recentes */}
      <div className="space-y-3">
        {recentEmails.length === 0 ? (
          <div className="text-center py-4">
            <p className="text-sm text-gray-500">Nenhum email recente encontrado.</p>
          </div>
        ) : (
          recentEmails.map((email) => (
            <div
              key={email.id}
              className={`p-3 rounded-lg border transition-colors hover:bg-gray-50 ${
                !email.is_read ? 'bg-blue-50 border-blue-200' : 'bg-white border-gray-200'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    {!email.is_read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                    )}
                    <p className={`text-sm ${!email.is_read ? 'font-semibold' : 'font-medium'} text-gray-900 truncate`}>
                      {email.sender}
                    </p>
                    {email.has_attachments && (
                      <svg className="w-3 h-3 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                    )}
                  </div>
                  <p className="mt-1 text-sm text-gray-900 truncate">
                    {email.subject || '(Sem assunto)'}
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    {new Date(email.date).toLocaleString('pt-BR', {
                      day: '2-digit',
                      month: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Ações Rápidas */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex space-x-3">
          <Link
            href="/atendimento"
            className="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            Ver Todos
          </Link>
          <button
            onClick={loadEmailData}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
