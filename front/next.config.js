/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configuração para produção
  output: 'standalone',

  // Configurações de imagem
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
    ],
    unoptimized: true,
  },

  // Headers de segurança
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-src 'self' https://callcentermobile.ddns.net https://*.ddns.net; frame-ancestors 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://callcentermobile.ddns.net; connect-src 'self' http://localhost:8001 http://backend:8001 https://callcentermobile.ddns.net;",
          },
        ],
      },
    ];
  },

  // Proxy para o backend
  async rewrites() {
    return [
      {
        source: '/api/backend/:path*',
        destination: 'http://backend:8001/:path*',
      },
      {
        source: '/api/v1/whatsapp/:path*',
        destination: 'http://backend:8001/api/v1/whatsapp/:path*',
      },
    ];
  },
};

module.exports = nextConfig;
