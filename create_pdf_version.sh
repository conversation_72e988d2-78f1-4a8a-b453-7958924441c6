#!/bin/bash

# Script para criar versão PDF sem emojis

echo "Criando versão para PDF..."

# Criar cópia do documento
cp WHATSAPP_COMPLETE_DOCUMENTATION.md WHATSAPP_PDF_VERSION.md

# Remover emojis e substituir por texto
sed -i 's/📱/[MOBILE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🌐/[WEB]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🖥️/[DESKTOP]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/💻/[COMPUTER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🗄️/[DATABASE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📊/[CHART]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📈/[TRENDING]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📉/[DECLINING]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/💰/[MONEY]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎯/[TARGET]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⚡/[LIGHTNING]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🤖/[ROBOT]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/😊/[SMILE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🕐/[CLOCK]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔧/[WRENCH]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔄/[REFRESH]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔔/[BELL]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔒/[LOCK]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🛡️/[SHIELD]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📋/[CLIPBOARD]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📝/[MEMO]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📁/[FOLDER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📷/[CAMERA]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎵/[MUSIC]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📹/[VIDEO]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📄/[DOCUMENT]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/👥/[USERS]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/👤/[USER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/👨‍💼/[BUSINESSMAN]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/💬/[CHAT]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📡/[SATELLITE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔗/[LINK]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔑/[KEY]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⚙️/[GEAR]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎛️/[CONTROL]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🛠️/[TOOLS]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📞/[PHONE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🚀/[ROCKET]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/✅/[CHECK]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/❌/[X]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⚠️/[WARNING]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🆘/[SOS]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔍/[SEARCH]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/💾/[DISK]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🏗️/[CONSTRUCTION]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🏭/[FACTORY]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎨/[ART]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔮/[CRYSTAL]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📚/[BOOKS]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🆕/[NEW]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🟢/[GREEN]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🟡/[YELLOW]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔴/[RED]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⚪/[WHITE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔵/[BLUE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⏰/[ALARM]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📛/[NAME]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📅/[CALENDAR]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⭐/[STAR]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🚨/[SIREN]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📦/[PACKAGE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎉/[PARTY]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/💡/[BULB]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🔥/[FIRE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/⏱️/[TIMER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/📂/[FOLDER_OPEN]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎪/[CIRCUS]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎭/[THEATER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎬/[MOVIE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎮/[GAME]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎲/[DICE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎯/[DART]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎪/[TENT]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎨/[PALETTE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎭/[MASKS]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎬/[CLAPPER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎮/[CONTROLLER]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎲/[GAME_DIE]/g' WHATSAPP_PDF_VERSION.md
sed -i 's/🎯/[BULLSEYE]/g' WHATSAPP_PDF_VERSION.md

# Remover âncoras de seção
sed -i 's/{#[^}]*}//g' WHATSAPP_PDF_VERSION.md

# Ajustar formatação de código
sed -i 's/```typescript/```/g' WHATSAPP_PDF_VERSION.md
sed -i 's/```python/```/g' WHATSAPP_PDF_VERSION.md
sed -i 's/```bash/```/g' WHATSAPP_PDF_VERSION.md
sed -i 's/```sql/```/g' WHATSAPP_PDF_VERSION.md
sed -i 's/```javascript/```/g' WHATSAPP_PDF_VERSION.md
sed -i 's/```yaml/```/g' WHATSAPP_PDF_VERSION.md
sed -i 's/```nginx/```/g' WHATSAPP_PDF_VERSION.md

echo "Versão para PDF criada: WHATSAPP_PDF_VERSION.md"

# Gerar PDF
echo "Gerando PDF..."
mkdir -p output

pandoc WHATSAPP_PDF_VERSION.md \
    --pdf-engine=pdflatex \
    --variable=geometry:margin=2cm \
    --variable=fontsize:11pt \
    --variable=documentclass:article \
    --variable=title:"Amvox Omnichannel - WhatsApp Integration" \
    --variable=author:"Amvox Tecnologia" \
    --variable=date:"$(date +%d/%m/%Y)" \
    --toc \
    --toc-depth=3 \
    --number-sections \
    --highlight-style=tango \
    -o "output/Amvox_WhatsApp_Documentation.pdf"

if [ $? -eq 0 ]; then
    echo "✅ PDF gerado com sucesso!"
    echo "📄 Arquivo: output/Amvox_WhatsApp_Documentation.pdf"
    echo "📊 Tamanho: $(du -h output/Amvox_WhatsApp_Documentation.pdf | cut -f1)"
    
    # Limpar arquivo temporário
    rm WHATSAPP_PDF_VERSION.md
    
    echo ""
    echo "Para abrir o PDF:"
    echo "xdg-open output/Amvox_WhatsApp_Documentation.pdf"
else
    echo "❌ Erro ao gerar PDF"
    exit 1
fi
