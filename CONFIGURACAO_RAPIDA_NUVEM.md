# ⚡ **Configuração Rápida - Evolution API na Nuvem**

## 🎯 **Situação Atual**
- ✅ Backend Amvox funcionando
- ✅ API WhatsApp respondendo  
- 🌐 Precisa conectar com Evolution API na nuvem

---

## **🚀 Opção 1: Configuração Automática (Recomendado)**

```bash
# Executar script de configuração
./configure_evolution_cloud.sh
```

O script irá pedir:
1. **URL da Evolution API** (ex: `https://api.exemplo.com`)
2. **API Key** da Evolution API
3. **Configuração do Webhook** (domínio público ou ngrok)

---

## **🔧 Opção 2: Configuração Manual**

### **Passo 1: Obter Informações**

Você precisa de:
- 🌐 **URL da Evolution API**: `https://sua-api.com`
- 🔑 **API Key**: Sua chave de acesso
- 🔗 **Webhook URL**: Como a Evolution API vai enviar dados

### **Passo 2: Testar Evolution API**

```bash
# Substituir pelos seus dados
EVOLUTION_URL="https://sua-evolution-api.com"
API_KEY="sua_api_key_aqui"

# Testar conectividade
curl -X GET "$EVOLUTION_URL/manager/instance/fetchInstances" \
  -H "apikey: $API_KEY"

# Deve retornar: [] ou lista de instâncias
```

### **Passo 3: Configurar Webhook**

#### **Opção A: Domínio Público (Produção)**
```bash
# Se você tem um domínio
WEBHOOK_URL="https://seu-dominio.com.br/api/v1/whatsapp/webhook"
```

#### **Opção B: ngrok (Testes Locais)**
```bash
# Instalar ngrok
sudo apt update
sudo apt install snapd
sudo snap install ngrok

# Expor porta local
ngrok http 8001

# Usar URL gerada (ex: https://abc123.ngrok.io)
WEBHOOK_URL="https://abc123.ngrok.io/api/v1/whatsapp/webhook"
```

### **Passo 4: Atualizar Backend**

```bash
# Editar configurações
cd back
nano .env

# Adicionar/atualizar estas linhas:
EVOLUTION_API_URL=https://sua-evolution-api.com
EVOLUTION_API_KEY=sua_api_key_aqui
EVOLUTION_WEBHOOK_URL=https://seu-webhook-url/api/v1/whatsapp/webhook
```

### **Passo 5: Reiniciar Backend**

```bash
# Reiniciar container
docker-compose restart backend

# Aguardar inicialização
sleep 15

# Testar
curl -s "http://localhost:3000/api/v1/whatsapp/stats"
```

### **Passo 6: Configurar Webhook na Evolution API**

```bash
# Para cada instância existente
curl -X POST "$EVOLUTION_URL/webhook/set/NOME_DA_INSTANCIA" \
  -H "apikey: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "'$WEBHOOK_URL'",
    "events": [
      "messages.upsert",
      "connection.update", 
      "messages.update"
    ]
  }'
```

---

## **🧪 Teste Final**

### **1. Testar API Local**
```bash
curl -s "http://localhost:3000/api/v1/whatsapp/stats" | jq .
```

### **2. Criar Instância de Teste**
```bash
curl -X POST "http://localhost:3000/api/v1/whatsapp/instances" \
  -H "Content-Type: application/json" \
  -d '{
    "instance_name": "teste_nuvem",
    "webhook_url": "'$WEBHOOK_URL'"
  }'
```

### **3. Obter QR Code**
```bash
curl -s "http://localhost:3000/api/v1/whatsapp/instances/teste_nuvem/qr" | jq .
```

### **4. Testar Interface**
1. Acessar: http://localhost:3000/atendimento
2. Clicar na aba "WhatsApp"
3. Clicar em "Conectar WhatsApp"
4. Verificar se QR Code aparece

---

## **📋 Checklist de Verificação**

- [ ] Evolution API na nuvem acessível
- [ ] API Key funcionando
- [ ] Webhook URL configurada
- [ ] Backend atualizado e reiniciado
- [ ] API local respondendo
- [ ] Interface WhatsApp carregando
- [ ] QR Code sendo gerado

---

## **🆘 Problemas Comuns**

### **1. Evolution API não responde**
```bash
# Verificar URL e API Key
curl -I https://sua-evolution-api.com
curl -X GET "https://sua-evolution-api.com/manager/instance/fetchInstances" \
  -H "apikey: sua_api_key"
```

### **2. Webhook não funciona**
```bash
# Verificar se URL está acessível
curl -I https://seu-webhook-url/api/v1/whatsapp/webhook

# Se usando ngrok, verificar se está rodando
curl http://localhost:4040/api/tunnels
```

### **3. Backend não conecta**
```bash
# Verificar logs
docker-compose logs backend

# Verificar configurações
cat back/.env | grep EVOLUTION

# Reiniciar
docker-compose restart backend
```

### **4. QR Code não aparece**
```bash
# Verificar instância na Evolution API
curl -X GET "$EVOLUTION_URL/instance/connectionState/NOME_INSTANCIA" \
  -H "apikey: $API_KEY"

# Verificar logs do backend
docker-compose logs backend | grep -i whatsapp
```

---

## **🔧 Comandos de Diagnóstico**

```bash
# Status geral
docker-compose ps

# Logs em tempo real
docker-compose logs -f backend

# Testar conectividade
curl -I http://localhost:3000
curl -I http://localhost:8001
curl -I https://sua-evolution-api.com

# Verificar configurações
cat back/.env | grep EVOLUTION
```

---

## **📞 Informações Necessárias**

Para configurar, você precisa fornecer:

1. **URL da Evolution API**: `https://...`
2. **API Key**: Chave de acesso
3. **Método de Webhook**:
   - Domínio público (produção)
   - ngrok (testes)
   - URL específica

---

## **🎯 Próximo Passo**

Execute o script de configuração automática:

```bash
./configure_evolution_cloud.sh
```

Ou siga os passos manuais acima com suas credenciais específicas.

**Tempo estimado:** 5-10 minutos ⏱️
