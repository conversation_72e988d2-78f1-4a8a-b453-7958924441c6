import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.api.v1 import threecx, reports, pdf_reports, outlook, mock_data, auth
from app.database.connection import create_tables, SessionLocal
from app.crud.configuracao import get_configuracoes_microsoft_graph
from app.config.settings import settings

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG if settings.DEBUG else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gerenciar ciclo de vida da aplicação."""
    # Startup
    print("🚀 Iniciando Amvox Omnichannel API...")
    try:
        create_tables()
        print("✅ Tabelas do banco de dados verificadas/criadas!")

        # Criar usuário admin padrão se não existir
        try:
            from app.crud.usuario import usuario_crud
            from app.schemas.usuario import UsuarioCreate

            db = SessionLocal()

            # Verificar se já existe usuário admin
            existing_admin = usuario_crud.get_by_login(db, login="admin")

            if not existing_admin:
                # Criar usuário admin padrão
                admin_data = UsuarioCreate(
                    nome="Administrador",
                    sobrenome="Sistema",
                    login="admin",
                    senha="admin123",
                    nivel_usuario="administrador",
                    email_corporativo="<EMAIL>",
                    senha_email_corporativo="@mv0x@posvenda"
                )

                usuario_crud.create(db, admin_data)
                print("✅ Usuário admin padrão criado com sucesso!")
            else:
                print("✅ Usuário admin já existe")

            db.close()
        except Exception as e:
            print(f"❌ Erro ao criar usuário admin: {e}")

        # Carregar configurações do Microsoft Graph do banco
        try:
            db = SessionLocal()
            configs = get_configuracoes_microsoft_graph(db)

            if configs.get("microsoft_graph_client_id"):
                from app.config.settings import settings
                settings.MICROSOFT_CLIENT_ID = configs.get("microsoft_graph_client_id")
                settings.MICROSOFT_CLIENT_SECRET = configs.get("microsoft_graph_client_secret")
                settings.MICROSOFT_TENANT_ID = configs.get("microsoft_graph_tenant_id")
                print("✅ Configurações Microsoft Graph carregadas do banco!")
            else:
                print("ℹ️ Nenhuma configuração Microsoft Graph encontrada no banco")

            db.close()
        except Exception as e:
            print(f"⚠️ Erro ao carregar configurações do banco: {e}")

    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {e}")

    yield

    # Shutdown
    print("🛑 Encerrando Amvox Omnichannel API...")

app = FastAPI(
    title="Amvox Omnichannel API",
    description="API para o sistema Omnichannel da Amvox",
    version="0.1.0",
    lifespan=lifespan
)

# Configuração de CORS para permitir requisições do frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost",
        "http://127.0.0.1:3000",
        "http://127.0.0.1",
        "*"  # Permitir todas as origens em desenvolvimento
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Endpoint raiz para verificar se a API está funcionando."""
    return {
        "message": "Bem-vindo à API do Sistema Omnichannel Amvox",
        "status": "online"
    }

@app.get("/health")
async def health_check():
    """Endpoint para verificar a saúde da API."""
    return {
        "status": "healthy",
        "version": "0.1.0"
    }

# Incluir os routers da API
app.include_router(auth.router, prefix="/api/v1")
app.include_router(threecx.router, prefix="/api/v1")
app.include_router(reports.router, prefix="/api/v1")
app.include_router(pdf_reports.router, prefix="/api/v1")
app.include_router(outlook.router, prefix="/api/v1")
app.include_router(mock_data.router, prefix="/api/v1")
