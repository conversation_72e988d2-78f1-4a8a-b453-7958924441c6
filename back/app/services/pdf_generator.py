"""
Serviço para geração de relatórios em PDF.
"""

import io
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak, KeepTogether
from reportlab.graphics.shapes import Drawing, Rect
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics import renderPDF

class PDFGenerator:
    """Classe para geração de relatórios em PDF."""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_styles()

    def _setup_styles(self):
        """Configura os estilos personalizados para o PDF."""

        # Estilo para título principal
        self.styles['Title'].alignment = 1  # Centralizado
        self.styles['Title'].fontSize = 20
        self.styles['Title'].spaceAfter = 20
        self.styles['Title'].textColor = colors.darkblue
        self.styles['Title'].fontName = 'Helvetica-Bold'

        # Estilo para subtítulo
        if 'Subtitle' not in self.styles:
            self.styles.add(
                ParagraphStyle(
                    name='Subtitle',
                    parent=self.styles['Heading2'],
                    fontSize=14,
                    alignment=1,
                    spaceAfter=15,
                    textColor=colors.blue,
                    fontName = 'Helvetica-Bold'
                )
            )

        # Estilo para seções
        if 'SectionTitle' not in self.styles:
            self.styles.add(
                ParagraphStyle(
                    name='SectionTitle',
                    parent=self.styles['Heading2'],
                    fontSize=16,
                    spaceAfter=12,
                    spaceBefore=20,
                    textColor=colors.darkblue,
                    fontName = 'Helvetica-Bold',
                    borderWidth=1,
                    borderColor=colors.darkblue,
                    borderPadding=5
                )
            )

        # Estilo para subseções
        if 'SubsectionTitle' not in self.styles:
            self.styles.add(
                ParagraphStyle(
                    name='SubsectionTitle',
                    parent=self.styles['Heading3'],
                    fontSize=12,
                    spaceAfter=8,
                    spaceBefore=12,
                    textColor=colors.blue,
                    fontName = 'Helvetica-Bold'
                )
            )

        # Estilo para texto normal
        self.styles['Normal'].fontSize = 10
        self.styles['Normal'].spaceAfter = 6
        self.styles['Normal'].fontName = 'Helvetica'

        # Estilo para informações importantes
        if 'Important' not in self.styles:
            self.styles.add(
                ParagraphStyle(
                    name='Important',
                    parent=self.styles['Normal'],
                    fontSize=11,
                    textColor=colors.red,
                    fontName = 'Helvetica-Bold'
                )
            )

        # Estilo para rodapé
        if 'Footer' not in self.styles:
            self.styles.add(
                ParagraphStyle(
                    name='Footer',
                    parent=self.styles['Normal'],
                    fontSize=8,
                    alignment=1,
                    textColor=colors.grey
                )
            )

    def _create_header(self, title: str, subtitle: Optional[str] = None, period: Optional[str] = None, logo_path: Optional[str] = None) -> List:
        """Cria o cabeçalho do relatório."""
        elements = []

        # Criar linha de separação superior
        elements.append(Spacer(1, 0.1*inch))

        # Adicionar logo se fornecida
        if logo_path and os.path.exists(logo_path):
            try:
                img = Image(logo_path, width=2*inch, height=0.8*inch)
                elements.append(img)
                elements.append(Spacer(1, 0.2*inch))
            except Exception as e:
                # Log do erro mas continua sem o logo
                print(f"Aviso: Não foi possível carregar o logo {logo_path}: {e}")
                pass

        # Adicionar título principal
        elements.append(Paragraph(title, self.styles['Title']))

        # Adicionar subtítulo se fornecido
        if subtitle:
            elements.append(Paragraph(subtitle, self.styles['Subtitle']))

        # Adicionar período se fornecido
        if period:
            elements.append(Paragraph(f"<b>Período:</b> {period}", self.styles['Normal']))

        # Adicionar informações de geração
        date_str = datetime.now().strftime("%d/%m/%Y às %H:%M:%S")
        elements.append(Paragraph(f"<b>Relatório gerado em:</b> {date_str}", self.styles['Normal']))

        # Linha de separação
        elements.append(Spacer(1, 0.3*inch))

        return elements

    def _create_summary_cards(self, summary_data: Dict[str, Any], title: str = "Resumo Executivo") -> List:
        """Cria cards de resumo com métricas principais."""
        elements = []

        elements.append(Paragraph(title, self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Criar tabela de resumo em formato de cards
        if summary_data:
            # Dividir os dados em grupos de 2 para criar layout de cards
            items = list(summary_data.items())

            for i in range(0, len(items), 2):
                row_data = []
                for j in range(2):
                    if i + j < len(items):
                        key, value = items[i + j]
                        if isinstance(value, dict):
                            # Se for um dicionário, pegar o primeiro valor ou fazer um resumo
                            if value:
                                first_key = list(value.keys())[0]
                                display_value = str(value[first_key])
                            else:
                                display_value = "N/A"
                        else:
                            display_value = str(value)

                        row_data.append([
                            Paragraph(f"<b>{key}</b>", self.styles['Normal']),
                            Paragraph(display_value, self.styles['Normal'])
                        ])
                    else:
                        row_data.append(["", ""])

                if row_data:
                    # Criar tabela para esta linha de cards
                    table_data = []
                    if len(row_data) == 2:
                        table_data.append([row_data[0][0], row_data[0][1], row_data[1][0], row_data[1][1]])
                    else:
                        table_data.append([row_data[0][0], row_data[0][1], "", ""])

                    table = Table(table_data, colWidths=[2.5*inch, 1.5*inch, 2.5*inch, 1.5*inch])
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (1, 0), colors.lightblue),
                        ('BACKGROUND', (2, 0), (3, 0), colors.lightblue),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 9),
                        ('TOPPADDING', (0, 0), (-1, -1), 8),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                    ]))
                    elements.append(table)
                    elements.append(Spacer(1, 0.1*inch))

        elements.append(Spacer(1, 0.2*inch))
        return elements

    def _create_table(self, data: List[List], title: str = "", col_widths: Optional[List] = None, header: bool = True, keep_together: bool = True, max_rows_per_page: int = 25) -> List:
        """Cria uma tabela com os dados fornecidos com controle de paginação melhorado."""
        elements = []

        if title:
            elements.append(Paragraph(title, self.styles['SubsectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

        if not data:
            elements.append(Paragraph("Nenhum dado disponível", self.styles['Normal']))
            return elements

        # Calcular larguras das colunas automaticamente se não fornecidas
        if col_widths is None:
            num_cols = len(data[0]) if data else 1
            available_width = 10*inch  # Largura disponível
            col_widths = [available_width / num_cols] * num_cols

        # Se a tabela for muito grande, dividir em páginas
        if len(data) > max_rows_per_page + 1:  # +1 para o cabeçalho
            header_row = data[0] if header else []
            data_rows = data[1:] if header else data

            # Dividir dados em chunks
            for i in range(0, len(data_rows), max_rows_per_page):
                chunk_data = [header_row] + data_rows[i:i + max_rows_per_page] if header else data_rows[i:i + max_rows_per_page]

                # Adicionar indicador de página se necessário
                if i > 0:
                    elements.append(PageBreak())
                    if title:
                        page_title = f"{title} (Página {(i // max_rows_per_page) + 1})"
                        elements.append(Paragraph(page_title, self.styles['SubsectionTitle']))
                        elements.append(Spacer(1, 0.1*inch))

                table = self._create_single_table(chunk_data, col_widths, keep_together)
                elements.extend(table)
        else:
            # Tabela pequena, criar normalmente
            table = self._create_single_table(data, col_widths, keep_together)
            elements.extend(table)

        return elements

    def _create_single_table(self, data: List[List], col_widths: List, keep_together: bool = True) -> List:
        """Cria uma única tabela com estilo melhorado."""
        elements = []

        table = Table(data, colWidths=col_widths, repeatRows=1)

        # Estilo básico da tabela
        style = [
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('TOPPADDING', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('TOPPADDING', (0, 1), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            # Melhor controle de quebra de linha
            ('WORDWRAP', (0, 0), (-1, -1), True),
            ('SPLITLONGWORDS', (0, 0), (-1, -1), True),
        ]

        # Aplicar estilo zebrado nas linhas
        for i in range(1, len(data)):
            if i % 2 == 0:
                style.append(('BACKGROUND', (0, i), (-1, i), colors.lightgrey))

        table.setStyle(TableStyle(style))

        # Usar KeepTogether para evitar quebra de tabela pequena
        if keep_together and len(data) <= 10:
            elements.append(KeepTogether([table]))
        else:
            elements.append(table)

        elements.append(Spacer(1, 0.2*inch))

        return elements

    def _create_metrics_table(self, metrics: Dict[str, Any], title: str) -> List:
        """Cria uma tabela de métricas formatada."""
        elements = []

        elements.append(Paragraph(title, self.styles['SubsectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        if not metrics:
            elements.append(Paragraph("Nenhuma métrica disponível", self.styles['Normal']))
            return elements

        # Converter métricas em dados de tabela
        table_data = [['Métrica', 'Valor']]

        for key, value in metrics.items():
            if isinstance(value, dict):
                # Se for um dicionário, adicionar cada item
                for sub_key, sub_value in value.items():
                    table_data.append([f"{key} - {sub_key}", str(sub_value)])
            else:
                table_data.append([key, str(value)])

        table = Table(table_data, colWidths=[4*inch, 2*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica'),
            ('FONTNAME', (1, 1), (1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ]))

        # Aplicar estilo zebrado
        for i in range(1, len(table_data)):
            if i % 2 == 0:
                table.setStyle(TableStyle([('BACKGROUND', (0, i), (-1, i), colors.lightgrey)]))

        elements.append(table)
        elements.append(Spacer(1, 0.2*inch))

        return elements

    def _create_section_with_page_control(self, title: str, content_elements: List, force_new_page: bool = False) -> List:
        """Cria uma seção com controle inteligente de página."""
        elements = []

        if force_new_page:
            elements.append(PageBreak())

        # Criar seção com KeepTogether para evitar quebra do título
        section_content = [
            Paragraph(title, self.styles['SectionTitle']),
            Spacer(1, 0.1*inch)
        ]

        # Adicionar conteúdo da seção
        section_content.extend(content_elements)

        # Usar KeepTogether apenas para seções pequenas
        if len(content_elements) <= 3:
            elements.append(KeepTogether(section_content))
        else:
            elements.extend(section_content)

        return elements

    def _create_footer(self) -> List:
        """Cria o rodapé do relatório."""
        elements = []

        elements.append(Spacer(1, 0.5*inch))
        elements.append(Paragraph("_" * 80, self.styles['Footer']))
        elements.append(Paragraph("Relatório gerado automaticamente pelo Sistema de Relatórios 3CX", self.styles['Footer']))
        elements.append(Paragraph("Para mais informações, entre em contato com o suporte técnico.", self.styles['Footer']))

        return elements

    def generate_distribution_report(self, data: Dict[str, Any], logo_path: Optional[str] = None) -> bytes:
        """
        Gera um relatório completo de distribuição em PDF.
        """
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=landscape(letter),
            rightMargin=0.5*inch,
            leftMargin=0.5*inch,
            topMargin=0.5*inch,
            bottomMargin=0.5*inch
        )

        elements = []

        # Extrair informações do período
        period = "N/A"
        if ('relatorio de distribuição' in data and
            'sumario' in data['relatorio de distribuição'] and
            'Informações do relatório' in data['relatorio de distribuição']['sumario']):
            info = data['relatorio de distribuição']['sumario']['Informações do relatório']
            start_date = info.get('Data de início', 'N/A')
            end_date = info.get('Data de fim', 'N/A')
            period = f"{start_date} a {end_date}"

        # Cabeçalho
        elements.extend(self._create_header(
            "RELATÓRIO DE DISTRIBUIÇÃO DE CHAMADAS - 3CX",
            "Análise Completa da Distribuição de Chamadas",
            period,
            logo_path
        ))

        # Resumo Executivo
        if 'relatorio de distribuição' in data and 'sumario' in data['relatorio de distribuição']:
            summary_data = data['relatorio de distribuição']['sumario']
            elements.extend(self._create_summary_cards(summary_data, "📊 RESUMO EXECUTIVO"))

        # Seção 1: Distribuição por Hora
        if ('relatorio de distribuição' in data and
            'Chamadas por hora' in data['relatorio de distribuição']):

            elements.append(Paragraph("🕐 DISTRIBUIÇÃO DE CHAMADAS POR HORA", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            hour_data = data['relatorio de distribuição']['Chamadas por hora']
            if hour_data:
                table_data = [['Hora', 'Total Recebidas', 'Atendidas', 'Não Atendidas', 'Nível de Serviço', 'Taxa de Atendimento']]

                for item in hour_data:
                    table_data.append([
                        str(item.get('Hora', 'N/A')),
                        str(item.get('Recebidas', 0)),
                        str(item.get('Atendidas', 0)),
                        str(item.get('Não-Atendidas', 0)),
                        str(item.get('Nível de serviço', 'N/A')),
                        str(item.get('Taxa de Atendidas', 'N/A'))
                    ])

                col_widths = [1*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch]
                elements.extend(self._create_table(table_data, col_widths=col_widths))

            # Análise dos horários de pico
            elements.append(Paragraph("📈 Análise de Horários de Pico", self.styles['SubsectionTitle']))
            if hour_data:
                # Encontrar horário com mais chamadas
                max_calls = max(hour_data, key=lambda x: x.get('Recebidas', 0))
                min_calls = min(hour_data, key=lambda x: x.get('Recebidas', 0))

                analysis_text = f"""
                <b>Horário de Maior Volume:</b> {max_calls.get('Hora', 'N/A')} com {max_calls.get('Recebidas', 0)} chamadas<br/>
                <b>Horário de Menor Volume:</b> {min_calls.get('Hora', 'N/A')} com {min_calls.get('Recebidas', 0)} chamadas<br/>
                <b>Recomendação:</b> Considere ajustar a escala de agentes nos horários de pico identificados.
                """
                elements.append(Paragraph(analysis_text, self.styles['Normal']))
                elements.append(Spacer(1, 0.2*inch))

        # Seção 2: Distribuição por Dia da Semana
        if ('relatorio de distribuição' in data and
            'Chamadas por dia da semana' in data['relatorio de distribuição']):

            elements.append(Paragraph("📅 DISTRIBUIÇÃO POR DIA DA SEMANA", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            weekday_data = data['relatorio de distribuição']['Chamadas por dia da semana']
            if weekday_data:
                table_data = [['Dia da Semana', 'Total Recebidas', 'Atendidas', 'Não Atendidas', 'Nível de Serviço', 'Taxa de Atendimento']]

                for item in weekday_data:
                    table_data.append([
                        str(item.get('Dia', 'N/A')),
                        str(item.get('Recebidas', 0)),
                        str(item.get('Atendidas', 0)),
                        str(item.get('Não-Atendidas', 0)),
                        str(item.get('Nível de serviço', 'N/A')),
                        str(item.get('Taxa de Atendidas', 'N/A'))
                    ])

                col_widths = [1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch]
                elements.extend(self._create_table(table_data, col_widths=col_widths))

        # Nova página para próximas seções
        elements.append(PageBreak())

        # Seção 3: Distribuição por Dia
        if ('relatorio de distribuição' in data and
            'Chamadas por dia' in data['relatorio de distribuição']):

            elements.append(Paragraph("📆 DISTRIBUIÇÃO POR DIA", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            daily_data = data['relatorio de distribuição']['Chamadas por dia']
            if daily_data:
                table_data = [['Data', 'Total Recebidas', 'Atendidas', 'Não Atendidas', 'Nível de Serviço', 'Taxa de Atendimento']]

                for item in daily_data:
                    table_data.append([
                        str(item.get('Data', 'N/A')),
                        str(item.get('Recebidas', 0)),
                        str(item.get('Atendidas', 0)),
                        str(item.get('Não-Atendidas', 0)),
                        str(item.get('Nível de serviço', 'N/A')),
                        str(item.get('Taxa de Atendidas', 'N/A'))
                    ])

                col_widths = [1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch]
                # Usar paginação automática para tabelas grandes
                elements.extend(self._create_table(table_data, col_widths=col_widths, max_rows_per_page=20))

                if len(daily_data) > 20:
                    elements.append(Paragraph(f"<i>Nota: Dados distribuídos em múltiplas páginas para melhor visualização. Total de {len(daily_data)} registros.</i>", self.styles['Normal']))
                    elements.append(Spacer(1, 0.1*inch))

        # Seção 4: Detalhes por Agente
        if ('relatorio de distribuição' in data and
            'Detalhes da Distribuição' in data['relatorio de distribuição']):

            elements.append(Paragraph("👥 ANÁLISE POR AGENTE", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            details_data = data['relatorio de distribuição']['Detalhes da Distribuição']
            if details_data:
                # Agrupar por agente
                agent_summary = {}
                for item in details_data:
                    agent = item.get('Agente', 'N/A')
                    if agent not in agent_summary:
                        agent_summary[agent] = {
                            'total_calls': 0,
                            'total_duration': 0,
                            'calls_with_duration': 0
                        }

                    agent_summary[agent]['total_calls'] += 1

                    # Processar duração se disponível
                    duration_str = item.get('Tempo das chamadas', '')
                    if duration_str and ':' in duration_str:
                        try:
                            parts = duration_str.split(':')
                            if len(parts) == 3:
                                seconds = int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
                                agent_summary[agent]['total_duration'] += seconds
                                agent_summary[agent]['calls_with_duration'] += 1
                        except:
                            pass

                # Criar tabela de resumo por agente
                table_data = [['Agente', 'Total de Chamadas', 'Tempo Total (HH:MM:SS)', 'Tempo Médio (HH:MM:SS)']]

                for agent, summary in agent_summary.items():
                    if agent != 'N/A' and summary['total_calls'] > 0:
                        total_duration = summary['total_duration']
                        avg_duration = total_duration / summary['calls_with_duration'] if summary['calls_with_duration'] > 0 else 0

                        # Converter segundos para HH:MM:SS
                        def seconds_to_hms(seconds):
                            hours = int(seconds // 3600)
                            minutes = int((seconds % 3600) // 60)
                            secs = int(seconds % 60)
                            return f"{hours:02d}:{minutes:02d}:{secs:02d}"

                        table_data.append([
                            agent,
                            str(summary['total_calls']),
                            seconds_to_hms(total_duration),
                            seconds_to_hms(avg_duration)
                        ])

                col_widths = [2.5*inch, 2*inch, 2*inch, 2*inch]
                elements.extend(self._create_table(table_data, col_widths=col_widths))

        # Rodapé
        elements.extend(self._create_footer())

        # Construir o PDF
        doc.build(elements)
        buffer.seek(0)
        return buffer.getvalue()

    def generate_satisfaction_survey_report(self, data: Dict[str, Any], logo_path: Optional[str] = None) -> bytes:
        """
        Gera um relatório completo de pesquisa de satisfação em PDF.
        """
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=landscape(letter),
            rightMargin=0.5*inch,
            leftMargin=0.5*inch,
            topMargin=0.5*inch,
            bottomMargin=0.5*inch
        )

        elements = []

        # Extrair informações do período
        period = "N/A"
        if ('Pesquisa Satisfação' in data and
            'sumario' in data['Pesquisa Satisfação'] and
            'Informações do relatório' in data['Pesquisa Satisfação']['sumario']):
            info = data['Pesquisa Satisfação']['sumario']['Informações do relatório']
            start_date = info.get('Data de início', 'N/A')
            end_date = info.get('Data de fim', 'N/A')
            period = f"{start_date} a {end_date}"

        # Cabeçalho
        elements.extend(self._create_header(
            "RELATÓRIO DE PESQUISA DE SATISFAÇÃO - 3CX",
            "Análise Completa da Satisfação dos Clientes",
            period,
            logo_path
        ))

        # Resumo Executivo
        if 'Pesquisa Satisfação' in data and 'sumario' in data['Pesquisa Satisfação']:
            summary_data = data['Pesquisa Satisfação']['sumario']
            elements.extend(self._create_summary_cards(summary_data, "📊 RESUMO EXECUTIVO"))

        # Seção 1: Avaliação do Atendente
        if ('Pesquisa Satisfação' in data and
            'Pesquisa por Agente' in data['Pesquisa Satisfação'] and
            'Pesquisa Amvox' in data['Pesquisa Satisfação']['Pesquisa por Agente'] and
            'Av-1-Avalia Atendente' in data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']):

            elements.append(Paragraph("👤 AVALIAÇÃO DO ATENDENTE", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            agent_data = data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-1-Avalia Atendente']
            if agent_data:
                table_data = [['Agente', 'Total Avaliadas', 'Satisfeito', 'Insatisfeito', '% Avaliadas', '% Meta', 'Taxa de Satisfação']]

                total_evaluated = 0
                total_satisfied = 0

                for item in agent_data:
                    evaluated = item.get('Avaliadas', 0)
                    satisfied = item.get('Satisfeito', 0)
                    dissatisfied = item.get('Insatisfeito', 0)

                    total_evaluated += evaluated
                    total_satisfied += satisfied

                    # Calcular taxa de satisfação
                    satisfaction_rate = (satisfied / evaluated * 100) if evaluated > 0 else 0

                    table_data.append([
                        str(item.get('Agente', 'N/A')),
                        str(evaluated),
                        str(satisfied),
                        str(dissatisfied),
                        str(item.get('% Avaliadas', 'N/A')),
                        str(item.get('% Meta(2.0)', 'N/A')),
                        f"{satisfaction_rate:.1f}%"
                    ])

                col_widths = [1.5*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.5*inch]
                elements.extend(self._create_table(table_data, col_widths=col_widths))

                # Análise da satisfação do atendente
                elements.append(Paragraph("📈 Análise da Satisfação do Atendente", self.styles['SubsectionTitle']))
                overall_satisfaction = (total_satisfied / total_evaluated * 100) if total_evaluated > 0 else 0

                # Encontrar melhor e pior agente
                best_agent = max(agent_data, key=lambda x: (x.get('Satisfeito', 0) / x.get('Avaliadas', 1)) if x.get('Avaliadas', 0) > 0 else 0)
                worst_agent = min(agent_data, key=lambda x: (x.get('Satisfeito', 0) / x.get('Avaliadas', 1)) if x.get('Avaliadas', 0) > 0 else 1)

                best_rate = (best_agent.get('Satisfeito', 0) / best_agent.get('Avaliadas', 1) * 100) if best_agent.get('Avaliadas', 0) > 0 else 0
                worst_rate = (worst_agent.get('Satisfeito', 0) / worst_agent.get('Avaliadas', 1) * 100) if worst_agent.get('Avaliadas', 0) > 0 else 0

                analysis_text = f"""
                <b>Taxa Geral de Satisfação:</b> {overall_satisfaction:.1f}%<br/>
                <b>Total de Avaliações:</b> {total_evaluated}<br/>
                <b>Melhor Desempenho:</b> {best_agent.get('Agente', 'N/A')} ({best_rate:.1f}%)<br/>
                <b>Necessita Atenção:</b> {worst_agent.get('Agente', 'N/A')} ({worst_rate:.1f}%)<br/>
                <b>Recomendação:</b> {'Excelente desempenho geral!' if overall_satisfaction >= 80 else 'Considere treinamentos para melhorar a satisfação.' if overall_satisfaction >= 60 else 'Ação imediata necessária para melhorar a satisfação.'}
                """
                elements.append(Paragraph(analysis_text, self.styles['Normal']))
                elements.append(Spacer(1, 0.2*inch))

        # Seção 2: Avaliação da Chamada
        if ('Pesquisa Satisfação' in data and
            'Pesquisa por Agente' in data['Pesquisa Satisfação'] and
            'Pesquisa Amvox' in data['Pesquisa Satisfação']['Pesquisa por Agente'] and
            'Av-2-Avalia Chamada' in data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']):

            elements.append(Paragraph("📞 AVALIAÇÃO DA RESOLUÇÃO DA CHAMADA", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            call_data = data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-2-Avalia Chamada']
            if call_data:
                table_data = [['Agente', 'Total Avaliadas', 'Resolvido (Sim)', 'Não Resolvido (Não)', '% Avaliadas', '% Meta', 'Taxa de Resolução']]

                total_evaluated = 0
                total_resolved = 0

                for item in call_data:
                    evaluated = item.get('Avaliadas', 0)
                    resolved = item.get('Sim', 0)
                    not_resolved = item.get('Não', 0)

                    total_evaluated += evaluated
                    total_resolved += resolved

                    # Calcular taxa de resolução
                    resolution_rate = (resolved / evaluated * 100) if evaluated > 0 else 0

                    table_data.append([
                        str(item.get('Agente', 'N/A')),
                        str(evaluated),
                        str(resolved),
                        str(not_resolved),
                        str(item.get('% Avaliadas', 'N/A')),
                        str(item.get('% Meta(2.0)', 'N/A')),
                        f"{resolution_rate:.1f}%"
                    ])

                col_widths = [1.5*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.2*inch, 1.5*inch]
                elements.extend(self._create_table(table_data, col_widths=col_widths, max_rows_per_page=15))

                # Análise da resolução de chamadas
                elements.append(Paragraph("📈 Análise da Resolução de Chamadas", self.styles['SubsectionTitle']))
                overall_resolution = (total_resolved / total_evaluated * 100) if total_evaluated > 0 else 0

                analysis_text = f"""
                <b>Taxa Geral de Resolução:</b> {overall_resolution:.1f}%<br/>
                <b>Total de Avaliações:</b> {total_evaluated}<br/>
                <b>Chamadas Resolvidas:</b> {total_resolved}<br/>
                <b>Chamadas Não Resolvidas:</b> {total_evaluated - total_resolved}<br/>
                <b>Status:</b> {'Excelente!' if overall_resolution >= 90 else 'Bom desempenho' if overall_resolution >= 75 else 'Necessita melhorias' if overall_resolution >= 60 else 'Crítico - ação imediata necessária'}
                """
                elements.append(Paragraph(analysis_text, self.styles['Normal']))
                elements.append(Spacer(1, 0.2*inch))

        # Nova página para próximas seções
        elements.append(PageBreak())

        # Seção 3: Avaliação da Empresa
        if ('Pesquisa Satisfação' in data and
            'Pesquisa por Agente' in data['Pesquisa Satisfação'] and
            'Pesquisa Amvox' in data['Pesquisa Satisfação']['Pesquisa por Agente'] and
            'Av-3-Avalia Empresa' in data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']):

            elements.append(Paragraph("🏢 AVALIAÇÃO DA EMPRESA", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            company_data = data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-3-Avalia Empresa']
            if company_data:
                table_data = [['Agente', 'Total Avaliadas', 'Nota 5', 'Nota 4', 'Nota 3', 'Nota 2', 'Nota 1', '% Meta', 'Nota Média']]

                total_evaluated = 0
                total_weighted_score = 0

                for item in company_data:
                    evaluated = item.get('Avaliadas', 0)
                    note5 = item.get('Nota 5', 0)
                    note4 = item.get('Nota 4', 0)
                    note3 = item.get('Nota 3', 0)
                    note2 = item.get('Nota 2', 0)
                    note1 = item.get('Nota 1', 0)

                    total_evaluated += evaluated

                    # Calcular nota média ponderada
                    weighted_score = (note5 * 5 + note4 * 4 + note3 * 3 + note2 * 2 + note1 * 1)
                    total_weighted_score += weighted_score
                    avg_score = weighted_score / evaluated if evaluated > 0 else 0

                    table_data.append([
                        str(item.get('Agente', 'N/A')),
                        str(evaluated),
                        str(note5),
                        str(note4),
                        str(note3),
                        str(note2),
                        str(note1),
                        str(item.get('% Meta(5.0)', 'N/A')),
                        f"{avg_score:.1f}"
                    ])

                col_widths = [1.2*inch, 1*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.8*inch, 1*inch, 1*inch]
                elements.extend(self._create_table(table_data, col_widths=col_widths))

                # Análise da avaliação da empresa
                elements.append(Paragraph("📈 Análise da Avaliação da Empresa", self.styles['SubsectionTitle']))
                overall_avg = total_weighted_score / total_evaluated if total_evaluated > 0 else 0

                analysis_text = f"""
                <b>Nota Média Geral:</b> {overall_avg:.1f}/5.0<br/>
                <b>Total de Avaliações:</b> {total_evaluated}<br/>
                <b>Classificação:</b> {'Excelente (4.5+)' if overall_avg >= 4.5 else 'Muito Bom (4.0+)' if overall_avg >= 4.0 else 'Bom (3.5+)' if overall_avg >= 3.5 else 'Regular (3.0+)' if overall_avg >= 3.0 else 'Necessita Melhorias'}<br/>
                <b>Recomendação:</b> {'Manter o excelente trabalho!' if overall_avg >= 4.0 else 'Identificar oportunidades de melhoria na experiência do cliente.' if overall_avg >= 3.0 else 'Revisar processos e implementar melhorias urgentes.'}
                """
                elements.append(Paragraph(analysis_text, self.styles['Normal']))
                elements.append(Spacer(1, 0.2*inch))

        # Seção 4: Resumo por Fila (se disponível)
        if ('Pesquisa Satisfação' in data and
            'Pesquisa por fila' in data['Pesquisa Satisfação']):

            elements.append(Paragraph("📋 RESUMO POR FILA", self.styles['SectionTitle']))
            elements.append(Spacer(1, 0.1*inch))

            queue_data = data['Pesquisa Satisfação']['Pesquisa por fila']
            if queue_data and 'Pesquisa Amvox' in queue_data:
                # Processar dados por fila se disponível
                elements.append(Paragraph("Dados de satisfação agrupados por fila de atendimento.", self.styles['Normal']))
                elements.append(Spacer(1, 0.1*inch))

        # Seção 5: Conclusões e Recomendações
        elements.append(Paragraph("📝 CONCLUSÕES E RECOMENDAÇÕES", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        conclusions_text = """
        <b>Pontos Fortes Identificados:</b><br/>
        • Agentes com alta taxa de satisfação demonstram excelência no atendimento<br/>
        • Processos de resolução de problemas funcionando adequadamente<br/>
        • Sistema de avaliação fornece feedback valioso dos clientes<br/><br/>

        <b>Oportunidades de Melhoria:</b><br/>
        • Padronizar treinamentos para agentes com menor desempenho<br/>
        • Implementar coaching individualizado baseado nos resultados<br/>
        • Revisar processos para aumentar a taxa de resolução na primeira chamada<br/><br/>

        <b>Ações Recomendadas:</b><br/>
        • Realizar reuniões mensais de feedback com a equipe<br/>
        • Implementar programa de reconhecimento para agentes de destaque<br/>
        • Monitorar tendências de satisfação ao longo do tempo<br/>
        • Investigar causas de insatisfação para implementar melhorias
        """
        elements.append(Paragraph(conclusions_text, self.styles['Normal']))

        # Rodapé
        elements.extend(self._create_footer())

        # Construir o PDF
        doc.build(elements)
        buffer.seek(0)
        return buffer.getvalue()

    def generate_dashboard_report(self, distribution_data: Dict[str, Any], satisfaction_data: Dict[str, Any], logo_path: Optional[str] = None) -> bytes:
        """
        Gera um relatório completo de dashboard em PDF.
        """
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=landscape(letter),
            rightMargin=0.5*inch,
            leftMargin=0.5*inch,
            topMargin=0.5*inch,
            bottomMargin=0.5*inch
        )

        elements = []

        # Extrair informações do período
        period = "N/A"
        if ('relatorio de distribuição' in distribution_data and
            'sumario' in distribution_data['relatorio de distribuição'] and
            'Informações do relatório' in distribution_data['relatorio de distribuição']['sumario']):
            info = distribution_data['relatorio de distribuição']['sumario']['Informações do relatório']
            start_date = info.get('Data de início', 'N/A')
            end_date = info.get('Data de fim', 'N/A')
            period = f"{start_date} a {end_date}"

        # Cabeçalho
        elements.extend(self._create_header(
            "DASHBOARD EXECUTIVO - 3CX",
            "Visão Geral Completa do Sistema Telefônico",
            period,
            logo_path
        ))

        # Seção 1: Resumo Executivo
        elements.append(Paragraph("📊 RESUMO EXECUTIVO", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Extrair métricas principais
        executive_summary = {}

        # Métricas de chamadas
        if ('relatorio de distribuição' in distribution_data and
            'sumario' in distribution_data['relatorio de distribuição'] and
            'Total de chamadas' in distribution_data['relatorio de distribuição']['sumario']):
            total_calls = distribution_data['relatorio de distribuição']['sumario']['Total de chamadas']
            executive_summary.update({
                'Total de Chamadas': total_calls.get('Número de chamadas conectadas', 0),
                'Chamadas Atendidas': total_calls.get('Número de chamadas atendidas', 0),
                'Chamadas Perdidas': total_calls.get('Número de chamadas não-atendidas pela PA', 0),
            })

        # Métricas de satisfação
        if ('Pesquisa Satisfação' in satisfaction_data and
            'sumario' in satisfaction_data['Pesquisa Satisfação'] and
            'Pesquisas Efetuadas' in satisfaction_data['Pesquisa Satisfação']['sumario']):
            surveys = satisfaction_data['Pesquisa Satisfação']['sumario']['Pesquisas Efetuadas']
            if surveys and len(surveys) > 0:
                executive_summary.update({
                    'Total de Pesquisas': surveys[0].get('Avaliadas', 0),
                    'Taxa de Avaliação': f"{surveys[0].get('% Avaliadas', 0)}%"
                })

        # Métricas de filas
        if ('relatorio de distribuição' in distribution_data and
            'chamadas_por_filas' in distribution_data['relatorio de distribuição']):
            queues = distribution_data['relatorio de distribuição']['chamadas_por_filas']
            if queues and len(queues) > 0:
                executive_summary.update({
                    'Nível de Serviço': f"{queues[0].get('Nível de serviço', 0)}%",
                    'Tempo Médio de Espera': queues[0].get('Espera Média', '00:00:00'),
                    'Tempo Médio de Conversa': queues[0].get('Duração Média', '00:00:00')
                })

        elements.extend(self._create_summary_cards(executive_summary, ""))

        # Seção 2: Análise de Chamadas
        elements.append(Paragraph("📞 ANÁLISE DETALHADA DE CHAMADAS", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Tabela de chamadas por hora (resumida)
        if ('relatorio de distribuição' in distribution_data and
            'Chamadas por hora' in distribution_data['relatorio de distribuição']):
            hour_data = distribution_data['relatorio de distribuição']['Chamadas por hora']
            if hour_data:
                # Mostrar apenas horários de pico (top 10)
                sorted_hours = sorted(hour_data, key=lambda x: x.get('Recebidas', 0), reverse=True)[:10]

                table_data = [['Hora', 'Recebidas', 'Atendidas', 'Não Atendidas', 'Nível de Serviço']]
                for item in sorted_hours:
                    table_data.append([
                        str(item.get('Hora', 'N/A')),
                        str(item.get('Recebidas', 0)),
                        str(item.get('Atendidas', 0)),
                        str(item.get('Não-Atendidas', 0)),
                        str(item.get('Nível de serviço', 'N/A'))
                    ])

                col_widths = [1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch, 2*inch]
                elements.extend(self._create_table(table_data, "Top 10 Horários por Volume de Chamadas", col_widths))

        # Seção 3: Análise de Satisfação
        elements.append(Paragraph("😊 ANÁLISE DE SATISFAÇÃO DOS CLIENTES", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Tabela de satisfação por agente (top 10)
        if ('Pesquisa Satisfação' in satisfaction_data and
            'Pesquisa por Agente' in satisfaction_data['Pesquisa Satisfação'] and
            'Pesquisa Amvox' in satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente'] and
            'Av-1-Avalia Atendente' in satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']):

            agent_data = satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-1-Avalia Atendente']
            if agent_data:
                # Ordenar por taxa de satisfação
                sorted_agents = sorted(agent_data,
                    key=lambda x: (x.get('Satisfeito', 0) / x.get('Avaliadas', 1)) if x.get('Avaliadas', 0) > 0 else 0,
                    reverse=True)[:10]

                table_data = [['Agente', 'Avaliadas', 'Satisfeito', 'Insatisfeito', 'Taxa de Satisfação']]
                for item in sorted_agents:
                    evaluated = item.get('Avaliadas', 0)
                    satisfied = item.get('Satisfeito', 0)
                    satisfaction_rate = (satisfied / evaluated * 100) if evaluated > 0 else 0

                    table_data.append([
                        str(item.get('Agente', 'N/A')),
                        str(evaluated),
                        str(satisfied),
                        str(item.get('Insatisfeito', 0)),
                        f"{satisfaction_rate:.1f}%"
                    ])

                col_widths = [2*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch]
                elements.extend(self._create_table(table_data, "Top 10 Agentes por Taxa de Satisfação", col_widths))

        # Nova página para próximas seções
        elements.append(PageBreak())

        # Seção 4: Análise de Agentes
        elements.append(Paragraph("👥 ANÁLISE DE DESEMPENHO DOS AGENTES", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Combinar dados de distribuição e satisfação por agente
        if ('relatorio de distribuição' in distribution_data and
            'Detalhes da Distribuição' in distribution_data['relatorio de distribuição']):

            details_data = distribution_data['relatorio de distribuição']['Detalhes da Distribuição']
            agent_summary = {}

            # Processar dados de distribuição
            for item in details_data:
                agent = item.get('Agente', 'N/A')
                if agent != 'N/A' and agent.strip():
                    if agent not in agent_summary:
                        agent_summary[agent] = {
                            'total_calls': 0,
                            'total_duration': 0,
                            'calls_with_duration': 0,
                            'satisfaction_data': None
                        }

                    agent_summary[agent]['total_calls'] += 1

                    # Processar duração
                    duration_str = item.get('Tempo das chamadas', '')
                    if duration_str and ':' in duration_str:
                        try:
                            parts = duration_str.split(':')
                            if len(parts) == 3:
                                seconds = int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
                                agent_summary[agent]['total_duration'] += seconds
                                agent_summary[agent]['calls_with_duration'] += 1
                        except:
                            pass

            # Adicionar dados de satisfação
            if ('Pesquisa Satisfação' in satisfaction_data and
                'Pesquisa por Agente' in satisfaction_data['Pesquisa Satisfação'] and
                'Pesquisa Amvox' in satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente'] and
                'Av-1-Avalia Atendente' in satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']):

                satisfaction_agents = satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']['Av-1-Avalia Atendente']
                for item in satisfaction_agents:
                    agent = item.get('Agente', 'N/A')
                    if agent in agent_summary:
                        agent_summary[agent]['satisfaction_data'] = item

            # Criar tabela consolidada
            if agent_summary:
                table_data = [['Agente', 'Total Chamadas', 'Tempo Médio', 'Avaliações', 'Taxa Satisfação']]

                # Ordenar por total de chamadas
                sorted_agents = sorted(agent_summary.items(), key=lambda x: x[1]['total_calls'], reverse=True)[:15]

                for agent, data in sorted_agents:
                    # Calcular tempo médio
                    avg_duration = data['total_duration'] / data['calls_with_duration'] if data['calls_with_duration'] > 0 else 0
                    avg_duration_str = f"{int(avg_duration//3600):02d}:{int((avg_duration%3600)//60):02d}:{int(avg_duration%60):02d}"

                    # Dados de satisfação
                    satisfaction_info = "N/A"
                    if data['satisfaction_data']:
                        evaluated = data['satisfaction_data'].get('Avaliadas', 0)
                        satisfied = data['satisfaction_data'].get('Satisfeito', 0)
                        if evaluated > 0:
                            satisfaction_rate = (satisfied / evaluated) * 100
                            satisfaction_info = f"{satisfaction_rate:.1f}%"

                    table_data.append([
                        agent,
                        str(data['total_calls']),
                        avg_duration_str,
                        str(data['satisfaction_data'].get('Avaliadas', 0) if data['satisfaction_data'] else 0),
                        satisfaction_info
                    ])

                col_widths = [2*inch, 1.5*inch, 1.5*inch, 1.5*inch, 1.5*inch]
                elements.extend(self._create_table(table_data, "Resumo Consolidado por Agente", col_widths))

        # Seção 5: Insights e Recomendações
        elements.append(Paragraph("💡 INSIGHTS E RECOMENDAÇÕES", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        insights_text = """
        <b>Principais Insights Identificados:</b><br/>
        • Análise dos horários de pico para otimização da escala de agentes<br/>
        • Identificação dos agentes com melhor desempenho em satisfação<br/>
        • Monitoramento do nível de serviço e tempos de espera<br/>
        • Avaliação da eficiência operacional por agente<br/><br/>

        <b>Recomendações Estratégicas:</b><br/>
        • Implementar escala dinâmica baseada nos horários de maior demanda<br/>
        • Desenvolver programa de mentoria com agentes de alto desempenho<br/>
        • Estabelecer metas específicas de tempo de atendimento por fila<br/>
        • Criar dashboard em tempo real para monitoramento contínuo<br/><br/>

        <b>Próximos Passos:</b><br/>
        • Análise mensal de tendências e sazonalidade<br/>
        • Implementação de alertas automáticos para métricas críticas<br/>
        • Treinamento focado em agentes com menor taxa de satisfação<br/>
        • Revisão periódica dos processos de atendimento
        """
        elements.append(Paragraph(insights_text, self.styles['Normal']))

        # Rodapé
        elements.extend(self._create_footer())

        # Construir o PDF
        doc.build(elements)
        buffer.seek(0)
        return buffer.getvalue()

    def generate_agent_report(self, agent_name: str, distribution_data: Dict[str, Any], satisfaction_data: Dict[str, Any], logo_path: Optional[str] = None) -> bytes:
        """
        Gera um relatório completo por agente em PDF.
        """
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=landscape(letter),
            rightMargin=0.5*inch,
            leftMargin=0.5*inch,
            topMargin=0.5*inch,
            bottomMargin=0.5*inch
        )

        elements = []

        # Extrair informações do período
        period = "N/A"
        if ('relatorio de distribuição' in distribution_data and
            'sumario' in distribution_data['relatorio de distribuição'] and
            'Informações do relatório' in distribution_data['relatorio de distribuição']['sumario']):
            info = distribution_data['relatorio de distribuição']['sumario']['Informações do relatório']
            start_date = info.get('Data de início', 'N/A')
            end_date = info.get('Data de fim', 'N/A')
            period = f"{start_date} a {end_date}"

        # Cabeçalho
        elements.extend(self._create_header(
            f"RELATÓRIO INDIVIDUAL - AGENTE: {agent_name.upper()}",
            "Análise Detalhada de Desempenho Individual",
            period,
            logo_path
        ))

        # Filtrar dados do agente específico
        agent_calls = []
        if ('relatorio de distribuição' in distribution_data and
            'Detalhes da Distribuição' in distribution_data['relatorio de distribuição']):
            details_data = distribution_data['relatorio de distribuição']['Detalhes da Distribuição']
            agent_calls = [item for item in details_data if item.get('Agente') == agent_name]

        # Dados de satisfação do agente
        agent_satisfaction = {
            'avaliacao': None,
            'chamada': None,
            'empresa': None
        }

        if ('Pesquisa Satisfação' in satisfaction_data and
            'Pesquisa por Agente' in satisfaction_data['Pesquisa Satisfação'] and
            'Pesquisa Amvox' in satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']):

            amvox_data = satisfaction_data['Pesquisa Satisfação']['Pesquisa por Agente']['Pesquisa Amvox']

            # Avaliação do atendente
            if 'Av-1-Avalia Atendente' in amvox_data:
                agent_satisfaction['avaliacao'] = next(
                    (item for item in amvox_data['Av-1-Avalia Atendente'] if item.get('Agente') == agent_name),
                    None
                )

            # Avaliação da chamada
            if 'Av-2-Avalia Chamada' in amvox_data:
                agent_satisfaction['chamada'] = next(
                    (item for item in amvox_data['Av-2-Avalia Chamada'] if item.get('Agente') == agent_name),
                    None
                )

            # Avaliação da empresa
            if 'Av-3-Avalia Empresa' in amvox_data:
                agent_satisfaction['empresa'] = next(
                    (item for item in amvox_data['Av-3-Avalia Empresa'] if item.get('Agente') == agent_name),
                    None
                )

        # Seção 1: Resumo Executivo do Agente
        elements.append(Paragraph("📊 RESUMO EXECUTIVO DO AGENTE", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Calcular métricas do agente
        total_calls = len(agent_calls)

        # Calcular tempo total e médio
        total_duration = 0
        calls_with_duration = 0
        for call in agent_calls:
            duration_str = call.get('Tempo das chamadas', '')
            if duration_str and ':' in duration_str:
                try:
                    parts = duration_str.split(':')
                    if len(parts) == 3:
                        seconds = int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
                        total_duration += seconds
                        calls_with_duration += 1
                except:
                    pass

        avg_duration = total_duration / calls_with_duration if calls_with_duration > 0 else 0
        avg_duration_str = f"{int(avg_duration//3600):02d}:{int((avg_duration%3600)//60):02d}:{int(avg_duration%60):02d}"
        total_duration_str = f"{int(total_duration//3600):02d}:{int((total_duration%3600)//60):02d}:{int(total_duration%60):02d}"

        # Contar eventos
        event_counts = {}
        for call in agent_calls:
            event = call.get('Evento', 'Desconhecido')
            event_counts[event] = event_counts.get(event, 0) + 1

        # Métricas de satisfação
        satisfaction_summary = {}
        agent_satisfaction_rate = 0
        if agent_satisfaction['avaliacao']:
            evaluated = agent_satisfaction['avaliacao'].get('Avaliadas', 0)
            satisfied = agent_satisfaction['avaliacao'].get('Satisfeito', 0)
            agent_satisfaction_rate = (satisfied / evaluated * 100) if evaluated > 0 else 0
            satisfaction_summary.update({
                'Avaliações Recebidas': evaluated,
                'Clientes Satisfeitos': satisfied,
                'Taxa de Satisfação': f"{agent_satisfaction_rate:.1f}%"
            })

        # Criar resumo executivo
        executive_summary = {
            'Total de Chamadas': total_calls,
            'Tempo Total de Atendimento': total_duration_str,
            'Tempo Médio por Chamada': avg_duration_str,
            'Chamadas por Dia': f"{total_calls / 30:.1f}" if total_calls > 0 else "0"
        }
        executive_summary.update(satisfaction_summary)

        elements.extend(self._create_summary_cards(executive_summary, ""))

        # Seção 2: Análise de Chamadas
        elements.append(Paragraph("📞 ANÁLISE DETALHADA DE CHAMADAS", self.styles['SectionTitle']))
        elements.append(Spacer(1, 0.1*inch))

        # Tabela de distribuição por evento
        if event_counts:
            table_data = [['Tipo de Evento', 'Quantidade', 'Percentual']]
            for event, count in sorted(event_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total_calls * 100) if total_calls > 0 else 0
                table_data.append([
                    event,
                    str(count),
                    f"{percentage:.1f}%"
                ])

            col_widths = [3*inch, 2*inch, 2*inch]
            elements.extend(self._create_table(table_data, "Distribuição de Chamadas por Tipo de Evento", col_widths))

        # Análise temporal (últimas 20 chamadas)
        if agent_calls:
            recent_calls = agent_calls[-20:] if len(agent_calls) > 20 else agent_calls
            table_data = [['Data', 'Número', 'Evento', 'Duração']]

            for call in recent_calls:
                table_data.append([
                    str(call.get('Data', 'N/A')),
                    str(call.get('Número telefônico', 'N/A')),
                    str(call.get('Evento', 'N/A')),
                    str(call.get('Tempo das chamadas', 'N/A'))
                ])

            col_widths = [2*inch, 2.5*inch, 2*inch, 1.5*inch]
            elements.extend(self._create_table(table_data, f"Histórico de Chamadas do Agente", col_widths, max_rows_per_page=20))

        # Rodapé
        elements.extend(self._create_footer())

        # Construir o PDF
        doc.build(elements)
        buffer.seek(0)
        return buffer.getvalue()