"""
Serviço para gerenciar mídia do WhatsApp
"""

import os
import uuid
import aiofiles
import httpx
from typing import Optional, Tuple
from pathlib import Path
import mimetypes
import logging

logger = logging.getLogger(__name__)


class WhatsAppMediaService:
    """Serviço para gerenciar mídia do WhatsApp"""
    
    def __init__(self, media_dir: str = "media/whatsapp"):
        self.media_dir = Path(media_dir)
        self.media_dir.mkdir(parents=True, exist_ok=True)
        
        # Tipos de mídia suportados
        self.supported_types = {
            'image': ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'audio': ['mp3', 'wav', 'ogg', 'aac', 'm4a'],
            'video': ['mp4', 'avi', 'mov', 'wmv', 'webm'],
            'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
        }
        
        # Tamanhos máximos (em bytes)
        self.max_sizes = {
            'image': 5 * 1024 * 1024,    # 5MB
            'audio': 16 * 1024 * 1024,   # 16MB
            'video': 64 * 1024 * 1024,   # 64MB
            'document': 100 * 1024 * 1024 # 100MB
        }
    
    def get_media_type(self, filename: str) -> Optional[str]:
        """Determinar tipo de mídia baseado na extensão"""
        extension = filename.lower().split('.')[-1]
        
        for media_type, extensions in self.supported_types.items():
            if extension in extensions:
                return media_type
        
        return None
    
    def is_supported_file(self, filename: str, file_size: int) -> Tuple[bool, str]:
        """Verificar se arquivo é suportado"""
        media_type = self.get_media_type(filename)
        
        if not media_type:
            return False, "Tipo de arquivo não suportado"
        
        max_size = self.max_sizes.get(media_type, 0)
        if file_size > max_size:
            return False, f"Arquivo muito grande. Máximo: {max_size // (1024*1024)}MB"
        
        return True, "OK"
    
    async def save_uploaded_file(self, file_content: bytes, filename: str, 
                               conversation_id: str) -> Tuple[str, str]:
        """Salvar arquivo enviado pelo usuário"""
        # Gerar nome único
        file_extension = filename.split('.')[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        
        # Criar diretório da conversa
        conversation_dir = self.media_dir / conversation_id
        conversation_dir.mkdir(exist_ok=True)
        
        # Caminho completo
        file_path = conversation_dir / unique_filename
        
        # Salvar arquivo
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        # Retornar caminho relativo e URL
        relative_path = str(file_path.relative_to(self.media_dir))
        file_url = f"/media/whatsapp/{relative_path}"
        
        logger.info(f"Arquivo salvo: {file_path}")
        return str(file_path), file_url
    
    async def download_media_from_evolution(self, media_url: str, 
                                          conversation_id: str, 
                                          filename: str = None) -> Tuple[str, str]:
        """Baixar mídia da Evolution API"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(media_url)
                response.raise_for_status()
                
                # Determinar nome do arquivo
                if not filename:
                    content_type = response.headers.get('content-type', '')
                    extension = mimetypes.guess_extension(content_type) or '.bin'
                    filename = f"media_{uuid.uuid4()}{extension}"
                
                # Salvar arquivo
                return await self.save_uploaded_file(
                    response.content, filename, conversation_id
                )
                
        except Exception as e:
            logger.error(f"Erro ao baixar mídia: {e}")
            raise
    
    def get_file_info(self, file_path: str) -> dict:
        """Obter informações do arquivo"""
        path = Path(file_path)
        
        if not path.exists():
            return None
        
        stat = path.stat()
        mimetype, _ = mimetypes.guess_type(str(path))
        
        return {
            'filename': path.name,
            'size': stat.st_size,
            'mimetype': mimetype,
            'media_type': self.get_media_type(path.name),
            'created_at': stat.st_ctime,
            'modified_at': stat.st_mtime
        }
    
    async def delete_file(self, file_path: str) -> bool:
        """Deletar arquivo"""
        try:
            path = Path(file_path)
            if path.exists():
                path.unlink()
                logger.info(f"Arquivo deletado: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Erro ao deletar arquivo: {e}")
            return False
    
    def cleanup_old_files(self, days: int = 30):
        """Limpar arquivos antigos (executar periodicamente)"""
        import time
        
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        deleted_count = 0
        
        for file_path in self.media_dir.rglob('*'):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"Erro ao deletar arquivo antigo {file_path}: {e}")
        
        logger.info(f"Limpeza concluída: {deleted_count} arquivos deletados")
        return deleted_count


# Instância global do serviço
media_service = WhatsAppMediaService()
