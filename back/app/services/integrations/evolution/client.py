"""
Cliente para integração com Evolution API
"""

import httpx
import logging
from typing import Dict, List, Optional, Any
from app.schemas.whatsapp import (
    EvolutionInstanceInfo,
    EvolutionSendMessageResponse,
    EvolutionQRCodeResponse
)

logger = logging.getLogger(__name__)


class EvolutionAPIClient:
    """Cliente para comunicação com Evolution API"""
    
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.headers = {
            "apikey": api_key,
            "Content-Type": "application/json"
        }
    
    async def create_instance(self, instance_name: str) -> Dict[str, Any]:
        """Criar nova instância WhatsApp"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/instance/create",
                    json={"instanceName": instance_name},
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Erro ao criar instância {instance_name}: {e}")
            raise
    
    async def get_instance_info(self, instance_name: str) -> Optional[Dict[str, Any]]:
        """Obter informações da instância"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/instance/fetchInstances",
                    params={"instanceName": instance_name},
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                data = response.json()
                
                # A API retorna uma lista, pegar o primeiro item se existir
                if isinstance(data, list) and len(data) > 0:
                    return data[0]
                return data
        except Exception as e:
            logger.error(f"Erro ao obter info da instância {instance_name}: {e}")
            return None
    
    async def get_qr_code(self, instance_name: str) -> Optional[str]:
        """Obter QR Code para conectar WhatsApp"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/instance/connect/{instance_name}",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                data = response.json()
                
                # Retornar o QR code base64
                if "base64" in data:
                    return data["base64"]
                return None
        except Exception as e:
            logger.error(f"Erro ao obter QR code da instância {instance_name}: {e}")
            return None
    
    async def send_text_message(self, instance_name: str, number: str, text: str) -> Optional[Dict[str, Any]]:
        """Enviar mensagem de texto"""
        try:
            # Limpar e formatar número
            clean_number = number.replace("+", "").replace("-", "").replace(" ", "")
            if not clean_number.endswith("@s.whatsapp.net"):
                clean_number = f"{clean_number}@s.whatsapp.net"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/message/sendText/{instance_name}",
                    json={
                        "number": clean_number,
                        "text": text
                    },
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem para {number}: {e}")
            raise
    
    async def set_webhook(self, instance_name: str, webhook_url: str) -> bool:
        """Configurar webhook para receber mensagens"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/webhook/set/{instance_name}",
                    json={
                        "url": webhook_url,
                        "events": [
                            "messages.upsert",
                            "connection.update",
                            "messages.update"
                        ]
                    },
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                return True
        except Exception as e:
            logger.error(f"Erro ao configurar webhook para {instance_name}: {e}")
            return False
    
    async def delete_instance(self, instance_name: str) -> bool:
        """Deletar instância"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{self.base_url}/instance/delete/{instance_name}",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                return True
        except Exception as e:
            logger.error(f"Erro ao deletar instância {instance_name}: {e}")
            return False
    
    async def get_instance_status(self, instance_name: str) -> Optional[str]:
        """Obter status da conexão da instância"""
        try:
            info = await self.get_instance_info(instance_name)
            if info:
                return info.get("status", "disconnected")
            return "disconnected"
        except Exception as e:
            logger.error(f"Erro ao obter status da instância {instance_name}: {e}")
            return "disconnected"
    
    async def logout_instance(self, instance_name: str) -> bool:
        """Fazer logout da instância"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{self.base_url}/instance/logout/{instance_name}",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                return True
        except Exception as e:
            logger.error(f"Erro ao fazer logout da instância {instance_name}: {e}")
            return False
