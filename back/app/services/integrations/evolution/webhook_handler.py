"""
Handler para processar webhooks da Evolution API
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from app.schemas.whatsapp import (
    WhatsAppMessage,
    WhatsAppConversation,
    MessageType,
    MessageStatus,
    ConversationStatus
)

logger = logging.getLogger(__name__)


class EvolutionWebhookHandler:
    """Handler para processar webhooks da Evolution API"""

    def __init__(self):
        # Em uma implementação real, isso seria conectado ao banco de dados
        self.conversations: Dict[str, WhatsAppConversation] = {}
        self.messages: Dict[str, list] = {}

    async def process_webhook(self, payload: Dict[str, Any]) -> bool:
        """Processar webhook recebido da Evolution API"""
        try:
            event_type = payload.get("event")
            instance = payload.get("instance")
            data = payload.get("data", {})

            logger.info(f"Processando webhook: {event_type} para instância {instance}")

            if event_type == "messages.upsert":
                return await self._handle_message_upsert(instance, data)
            elif event_type == "connection.update":
                return await self._handle_connection_update(instance, data)
            elif event_type == "messages.update":
                return await self._handle_message_update(instance, data)
            else:
                logger.warning(f"Evento não reconhecido: {event_type}")
                return False

        except Exception as e:
            logger.error(f"Erro ao processar webhook: {e}")
            return False

    async def _handle_message_upsert(self, instance: str, data: Dict[str, Any]) -> bool:
        """Processar nova mensagem recebida"""
        try:
            # Extrair informações da mensagem
            key = data.get("key", {})
            message = data.get("message", {})

            # Verificar se é mensagem de entrada (do cliente)
            from_me = key.get("fromMe", False)
            if from_me:
                # Ignorar mensagens enviadas por nós
                return True

            # Extrair dados da mensagem
            remote_jid = key.get("remoteJid", "")
            message_id = key.get("id", "")
            timestamp = data.get("messageTimestamp", 0)

            # Extrair conteúdo da mensagem
            content = ""
            message_type = MessageType.TEXT

            if "conversation" in message:
                content = message["conversation"]
            elif "extendedTextMessage" in message:
                content = message["extendedTextMessage"].get("text", "")
            elif "imageMessage" in message:
                content = "[Imagem]"
                message_type = MessageType.IMAGE
            elif "audioMessage" in message:
                content = "[Áudio]"
                message_type = MessageType.AUDIO
            elif "videoMessage" in message:
                content = "[Vídeo]"
                message_type = MessageType.VIDEO
            elif "documentMessage" in message:
                content = "[Documento]"
                message_type = MessageType.DOCUMENT

            # Extrair número e nome do contato
            contact_number = remote_jid.replace("@s.whatsapp.net", "")
            contact_name = self._extract_contact_name(data) or contact_number

            # Criar ou obter conversa
            conversation_id = f"{instance}_{contact_number}"
            conversation = await self._get_or_create_conversation(
                conversation_id, contact_name, contact_number, instance
            )

            # Criar mensagem
            whatsapp_message = WhatsAppMessage(
                id=message_id,
                conversation_id=conversation_id,
                sender_type="customer",
                sender_name=contact_name,
                sender_number=contact_number,
                content=content,
                message_type=message_type,
                status=MessageStatus.DELIVERED,
                timestamp=datetime.fromtimestamp(timestamp),
                evolution_message_id=message_id
            )

            # Salvar mensagem
            if conversation_id not in self.messages:
                self.messages[conversation_id] = []
            self.messages[conversation_id].append(whatsapp_message)

            # Atualizar conversa
            conversation.last_message = content
            conversation.last_message_time = whatsapp_message.timestamp
            conversation.unread_count += 1
            conversation.updated_at = datetime.now()

            logger.info(f"Nova mensagem processada: {contact_name} -> {content[:50]}...")

            # Aqui você pode adicionar lógica para notificar atendentes via WebSocket
            # await self._notify_new_message(conversation, whatsapp_message)

            return True

        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {e}")
            return False

    async def _handle_connection_update(self, instance: str, data: Dict[str, Any]) -> bool:
        """Processar atualização de conexão"""
        try:
            state = data.get("state", "")
            logger.info(f"Atualização de conexão para {instance}: {state}")

            # Aqui você pode atualizar o status da instância no banco de dados
            # await self._update_instance_status(instance, state)

            return True
        except Exception as e:
            logger.error(f"Erro ao processar atualização de conexão: {e}")
            return False

    async def _handle_message_update(self, instance: str, data: Dict[str, Any]) -> bool:
        """Processar atualização de status de mensagem"""
        try:
            # Processar atualizações de status (entregue, lida, etc.)
            logger.info(f"Atualização de mensagem para {instance}")
            return True
        except Exception as e:
            logger.error(f"Erro ao processar atualização de mensagem: {e}")
            return False

    async def _get_or_create_conversation(
        self,
        conversation_id: str,
        contact_name: str,
        contact_number: str,
        instance: str
    ) -> WhatsAppConversation:
        """Obter ou criar conversa"""
        if conversation_id in self.conversations:
            return self.conversations[conversation_id]

        # Criar nova conversa
        conversation = WhatsAppConversation(
            id=conversation_id,
            customer_name=contact_name,
            customer_number=contact_number,
            instance_id=instance,
            status=ConversationStatus.WAITING,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        self.conversations[conversation_id] = conversation
        return conversation

    def _extract_contact_name(self, data: Dict[str, Any]) -> Optional[str]:
        """Extrair nome do contato dos dados da mensagem"""
        try:
            # Tentar extrair nome do pushName ou outros campos
            push_name = data.get("pushName")
            if push_name:
                return push_name

            # Outros campos possíveis para nome
            key = data.get("key", {})
            participant = key.get("participant")
            if participant:
                return participant.replace("@s.whatsapp.net", "")

            return None
        except Exception:
            return None

    async def get_conversations(self, status: Optional[str] = None) -> List[WhatsAppConversation]:
        """Obter lista de conversas"""
        conversations = list(self.conversations.values())

        if status:
            conversations = [c for c in conversations if c.status == status]

        # Ordenar por última mensagem
        conversations.sort(key=lambda x: x.last_message_time or x.created_at, reverse=True)

        return conversations

    async def get_conversation_messages(self, conversation_id: str) -> List[WhatsAppMessage]:
        """Obter mensagens de uma conversa"""
        return self.messages.get(conversation_id, [])

    async def add_agent_message(
        self,
        conversation_id: str,
        content: str,
        agent_name: str = "Agente"
    ) -> WhatsAppMessage:
        """Adicionar mensagem do agente"""
        message = WhatsAppMessage(
            id=f"agent_{datetime.now().timestamp()}",
            conversation_id=conversation_id,
            sender_type="agent",
            sender_name=agent_name,
            sender_number="",
            content=content,
            message_type=MessageType.TEXT,
            status=MessageStatus.SENT,
            timestamp=datetime.now()
        )

        # Salvar mensagem
        if conversation_id not in self.messages:
            self.messages[conversation_id] = []
        self.messages[conversation_id].append(message)

        # Atualizar conversa
        if conversation_id in self.conversations:
            conversation = self.conversations[conversation_id]
            conversation.last_message = content
            conversation.last_message_time = message.timestamp
            conversation.updated_at = datetime.now()

        return message


# Instância global do handler
webhook_handler = EvolutionWebhookHandler()
