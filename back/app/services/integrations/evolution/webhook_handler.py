"""
Handler para processar webhooks da Evolution API
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.orm import Session

from app.schemas.whatsapp import (
    WhatsAppMessage,
    WhatsAppConversation,
    MessageType,
    MessageStatus,
    ConversationStatus
)
from app.database.connection import get_db
from app.crud.whatsapp import (
    instance_crud,
    conversation_crud,
    message_crud
)
from app.database.models.whatsapp import (
    MessageType as DBMessageType,
    MessageStatus as DBMessageStatus,
    ConversationStatus as DBConversationStatus,
    InstanceStatus as DBInstanceStatus
)

logger = logging.getLogger(__name__)


class EvolutionWebhookHandler:
    """Handler para processar webhooks da Evolution API"""

    def __init__(self):
        # Agora usando banco PostgreSQL
        pass

    async def process_webhook(self, payload: Dict[str, Any]) -> bool:
        """Processar webhook recebido da Evolution API"""
        db = next(get_db())
        try:
            event_type = payload.get("event")
            instance = payload.get("instance")
            data = payload.get("data", {})

            logger.info(f"Processando webhook: {event_type} para instância {instance}")
            logger.info(f"Payload completo: {payload}")

            if event_type == "messages.upsert":
                return await self._handle_message_upsert(db, instance, data, payload)
            elif event_type == "connection.update":
                return await self._handle_connection_update(db, instance, data)
            elif event_type == "messages.update":
                return await self._handle_message_update(db, instance, data)
            else:
                logger.warning(f"Evento não reconhecido: {event_type}")
                return False

        except Exception as e:
            logger.error(f"Erro ao processar webhook: {e}")
            return False
        finally:
            db.close()

    async def _handle_message_upsert(self, db: Session, instance: str, data: Dict[str, Any], full_payload: Dict[str, Any]) -> bool:
        """Processar nova mensagem recebida"""
        try:
            # Extrair informações da mensagem
            key = data.get("key", {})
            message = data.get("message", {})

            # Verificar se é mensagem de entrada (do cliente)
            from_me = key.get("fromMe", False)
            if from_me:
                # Ignorar mensagens enviadas por nós
                logger.info(f"Ignorando mensagem enviada por nós: {key.get('id')}")
                return True

            # Extrair dados da mensagem
            remote_jid = key.get("remoteJid", "")
            message_id = key.get("id", "")
            timestamp = data.get("messageTimestamp", 0)

            # Extrair conteúdo da mensagem
            content = ""
            db_message_type = DBMessageType.TEXT

            if "conversation" in message:
                content = message["conversation"]
            elif "extendedTextMessage" in message:
                content = message["extendedTextMessage"].get("text", "")
            elif "imageMessage" in message:
                content = "[Imagem]"
                db_message_type = DBMessageType.IMAGE
            elif "audioMessage" in message:
                content = "[Áudio]"
                db_message_type = DBMessageType.AUDIO
            elif "videoMessage" in message:
                content = "[Vídeo]"
                db_message_type = DBMessageType.VIDEO
            elif "documentMessage" in message:
                content = "[Documento]"
                db_message_type = DBMessageType.DOCUMENT
            else:
                content = "[Mensagem não suportada]"
                logger.warning(f"Tipo de mensagem não reconhecido: {list(message.keys())}")

            # Extrair número e nome do contato
            contact_number = remote_jid.replace("@s.whatsapp.net", "")
            contact_name = self._extract_contact_name(data) or f"Cliente {contact_number[-4:]}"

            logger.info(f"Processando mensagem de {contact_name} ({contact_number}): {content[:50]}...")

            # Obter ou criar instância
            try:
                db_instance = instance_crud.get_instance_by_name(db, instance)
                if not db_instance:
                    logger.info(f"Criando nova instância: {instance}")
                    db_instance = instance_crud.create_instance(db, instance)
                    logger.info(f"✅ Instância criada: ID={db_instance.id}")
                else:
                    logger.info(f"✅ Instância encontrada: ID={db_instance.id}")
            except Exception as e:
                logger.error(f"❌ Erro ao obter/criar instância: {e}")
                raise

            # Criar ou obter conversa
            external_id = f"{instance}_{contact_number}"
            try:
                db_conversation = conversation_crud.create_or_get_conversation(
                    db, external_id, contact_name, contact_number, db_instance.id
                )
                logger.info(f"✅ Conversa obtida/criada: ID={db_conversation.id}, External={external_id}")
            except Exception as e:
                logger.error(f"❌ Erro ao obter/criar conversa: {e}")
                raise

            # Verificar se mensagem já existe (evitar duplicatas)
            from app.database.models.whatsapp import WhatsAppMessage as DBWhatsAppMessage
            existing_message = db.query(DBWhatsAppMessage).filter(DBWhatsAppMessage.external_id == message_id).first()
            if existing_message:
                logger.info(f"Mensagem já existe: {message_id}")
                return True

            # Criar mensagem no banco
            try:
                db_message = message_crud.create_message(
                    db=db,
                    external_id=message_id,
                    conversation_id=db_conversation.id,
                    sender_type="customer",
                    sender_name=contact_name,
                    sender_number=contact_number,
                    content=content,
                    message_type=db_message_type,
                    timestamp=datetime.fromtimestamp(timestamp)
                )
                logger.info(f"✅ Mensagem criada: ID={db_message.id}")
            except Exception as e:
                logger.error(f"❌ Erro ao criar mensagem: {e}")
                raise

            # Atualizar última mensagem da conversa
            conversation_crud.update_last_message(
                db, db_conversation.id, content, datetime.fromtimestamp(timestamp), increment_unread=True
            )

            logger.info(f"✅ Nova mensagem salva: ID={db_message.id}, Conversa={db_conversation.id}")

            # TODO: Aqui você pode adicionar lógica para notificar atendentes via WebSocket
            # await self._notify_new_message(db_conversation, db_message)

            return True

        except Exception as e:
            logger.error(f"❌ Erro ao processar mensagem: {e}")
            logger.error(f"Payload que causou erro: {full_payload}")
            return False

    async def _handle_connection_update(self, db: Session, instance: str, data: Dict[str, Any]) -> bool:
        """Processar atualização de conexão"""
        try:
            state = data.get("state", "")
            logger.info(f"Atualização de conexão para {instance}: {state}")

            # Atualizar status da instância no banco de dados
            db_instance = instance_crud.get_instance_by_name(db, instance)
            if db_instance:
                # Mapear estado para enum
                status_map = {
                    "open": DBInstanceStatus.CONNECTED,
                    "close": DBInstanceStatus.DISCONNECTED,
                    "connecting": DBInstanceStatus.CONNECTING,
                    "qr": DBInstanceStatus.QR_CODE
                }
                new_status = status_map.get(state, DBInstanceStatus.DISCONNECTED)
                instance_crud.update_instance_status(db, db_instance.id, new_status)
                logger.info(f"Status da instância {instance} atualizado para: {new_status}")

            return True
        except Exception as e:
            logger.error(f"Erro ao processar atualização de conexão: {e}")
            return False

    async def _handle_message_update(self, db: Session, instance: str, data: Dict[str, Any]) -> bool:
        """Processar atualização de status de mensagem"""
        try:
            # Processar atualizações de status (entregue, lida, etc.)
            logger.info(f"Atualização de mensagem para {instance}: {data}")

            # TODO: Implementar atualização de status de mensagem
            # key = data.get("key", {})
            # message_id = key.get("id", "")
            # status = data.get("status", "")

            return True
        except Exception as e:
            logger.error(f"Erro ao processar atualização de mensagem: {e}")
            return False

    # Método removido - agora usando conversation_crud.create_or_get_conversation

    def _extract_contact_name(self, data: Dict[str, Any]) -> Optional[str]:
        """Extrair nome do contato dos dados da mensagem"""
        try:
            # Tentar extrair nome do pushName ou outros campos
            push_name = data.get("pushName")
            if push_name:
                return push_name

            # Outros campos possíveis para nome
            key = data.get("key", {})
            participant = key.get("participant")
            if participant:
                return participant.replace("@s.whatsapp.net", "")

            return None
        except Exception:
            return None

    async def get_conversations(self, status: Optional[str] = None) -> List[WhatsAppConversation]:
        """Obter lista de conversas"""
        db = next(get_db())
        try:
            # Converter string status para enum se necessário
            db_status = None
            if status:
                status_map = {
                    "open": DBConversationStatus.OPEN,
                    "closed": DBConversationStatus.CLOSED,
                    "waiting": DBConversationStatus.WAITING,
                    "assigned": DBConversationStatus.ASSIGNED
                }
                db_status = status_map.get(status)

            db_conversations = conversation_crud.get_conversations(db, status=db_status)

            # Converter para schema
            conversations = []
            for db_conv in db_conversations:
                conv = WhatsAppConversation(
                    id=db_conv.external_id,
                    customer_name=db_conv.customer_name,
                    customer_number=db_conv.customer_number,
                    instance_id=str(db_conv.instance_id),
                    status=ConversationStatus(db_conv.status.value),
                    last_message=db_conv.last_message,
                    last_message_time=db_conv.last_message_time,
                    unread_count=db_conv.unread_count,
                    created_at=db_conv.created_at,
                    updated_at=db_conv.updated_at,
                    assigned_user_id=db_conv.assigned_user_id
                )
                conversations.append(conv)

            return conversations
        finally:
            db.close()

    async def get_conversation_messages(self, conversation_id: str) -> List[WhatsAppMessage]:
        """Obter mensagens de uma conversa"""
        db = next(get_db())
        try:
            # Buscar conversa pelo external_id
            from app.database.models.whatsapp import WhatsAppConversation as DBWhatsAppConversation
            db_conversation = db.query(DBWhatsAppConversation).filter(
                DBWhatsAppConversation.external_id == conversation_id
            ).first()

            if not db_conversation:
                return []

            db_messages = message_crud.get_conversation_messages(db, db_conversation.id)

            # Converter para schema
            messages = []
            for db_msg in db_messages:
                msg = WhatsAppMessage(
                    id=db_msg.external_id,
                    conversation_id=conversation_id,
                    sender_type=db_msg.sender_type,
                    sender_name=db_msg.sender_name,
                    sender_number=db_msg.sender_number,
                    content=db_msg.content,
                    message_type=MessageType(db_msg.message_type.value),
                    status=MessageStatus(db_msg.status.value),
                    timestamp=db_msg.timestamp,
                    evolution_message_id=db_msg.external_id
                )
                messages.append(msg)

            return messages
        finally:
            db.close()

    async def add_agent_message(
        self,
        conversation_id: str,
        content: str,
        agent_name: str = "Agente"
    ) -> WhatsAppMessage:
        """Adicionar mensagem do agente"""
        db = next(get_db())
        try:
            # Buscar conversa pelo external_id
            from app.database.models.whatsapp import WhatsAppConversation as DBWhatsAppConversation
            db_conversation = db.query(DBWhatsAppConversation).filter(
                DBWhatsAppConversation.external_id == conversation_id
            ).first()

            if not db_conversation:
                raise ValueError(f"Conversa não encontrada: {conversation_id}")

            # Criar ID único para mensagem do agente
            message_id = f"agent_{int(datetime.now().timestamp() * 1000)}"

            # Criar mensagem no banco
            db_message = message_crud.create_message(
                db=db,
                external_id=message_id,
                conversation_id=db_conversation.id,
                sender_type="agent",
                sender_name=agent_name,
                sender_number="",
                content=content,
                message_type=DBMessageType.TEXT,
                timestamp=datetime.now()
            )

            # Atualizar última mensagem da conversa (sem incrementar unread)
            conversation_crud.update_last_message(
                db, db_conversation.id, content, datetime.now(), increment_unread=False
            )

            # Converter para schema
            message = WhatsAppMessage(
                id=db_message.external_id,
                conversation_id=conversation_id,
                sender_type="agent",
                sender_name=agent_name,
                sender_number="",
                content=content,
                message_type=MessageType.TEXT,
                status=MessageStatus.SENT,
                timestamp=db_message.timestamp,
                evolution_message_id=db_message.external_id
            )

            return message
        finally:
            db.close()


# Instância global do handler
webhook_handler = EvolutionWebhookHandler()
