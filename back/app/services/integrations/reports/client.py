import httpx
import logging
from typing import Dict, Any, List, Optional
from datetime import date

from app.config.settings import settings

logger = logging.getLogger(__name__)

class ReportsClient:
    """
    Cliente para interagir com a API de relatórios.
    """

    def __init__(self):
        """
        Inicializa o cliente de relatórios.
        """
        self.base_url = "http://ticmobilerb.ddns.net/gdacip/apijson.php"
        self.user = settings.REPORTS_USER
        self.password = settings.REPORTS_PASSWORD
        self.headers = {
            "Content-Type": "application/json",
        }

    async def _make_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Faz uma requisição para a API de relatórios.

        Args:
            data: Dados para enviar no corpo da requisição

        Returns:
            Resposta da API como um dicionário
        """
        # Adiciona as credenciais aos dados
        data["user"] = self.user
        data["password"] = self.password

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.base_url,
                    headers=self.headers,
                    json=data,
                    timeout=30.0  # Timeout de 30 segundos
                )

                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP ao acessar a API de relatórios: {e}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Erro de requisição ao acessar a API de relatórios: {e}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao acessar a API de relatórios: {e}")
            raise

    async def get_distribution_report(
        self,
        start_date: date,
        end_date: date,
        queues: List[int]
    ) -> Dict[str, Any]:
        """
        Obtém o relatório de distribuição.

        Args:
            start_date: Data inicial do relatório
            end_date: Data final do relatório
            queues: Lista de IDs das filas

        Returns:
            Dados do relatório de distribuição
        """
        data = {
            "data_inicial": start_date.strftime("%Y-%m-%d"),
            "data_final": end_date.strftime("%Y-%m-%d"),
            "filas": queues,
            "relatorio": "Distribuição"
        }

        logger.info(f"Solicitando relatório de distribuição para o período de {start_date} a {end_date}")
        response = await self._make_request(data)

        return response

    async def get_satisfaction_survey_report(
        self,
        start_date: date,
        end_date: date,
        queues: List[int]
    ) -> Dict[str, Any]:
        """
        Obtém o relatório de pesquisa de satisfação.

        Args:
            start_date: Data inicial do relatório
            end_date: Data final do relatório
            queues: Lista de IDs das filas

        Returns:
            Dados do relatório de pesquisa de satisfação
        """
        data = {
            "data_inicial": start_date.strftime("%Y-%m-%d"),
            "data_final": end_date.strftime("%Y-%m-%d"),
            "filas": queues,
            "relatorio": "Pesquisa Satisfação"
        }

        logger.info(f"Solicitando relatório de pesquisa de satisfação para o período de {start_date} a {end_date}")
        response = await self._make_request(data)

        return response

    async def get_call_details_report(
        self,
        start_date: date,
        end_date: date,
        queues: List[int]
    ) -> Dict[str, Any]:
        """
        Obtém relatório detalhado de chamadas para cálculos de métricas.

        Args:
            start_date: Data inicial do relatório
            end_date: Data final do relatório
            queues: Lista de IDs das filas

        Returns:
            Dados detalhados das chamadas
        """
        data = {
            "data_inicial": start_date.strftime("%Y-%m-%d"),
            "data_final": end_date.strftime("%Y-%m-%d"),
            "filas": queues,
            "relatorio": "Detalhes Chamadas"
        }

        logger.info(f"Solicitando relatório de detalhes de chamadas para o período de {start_date} a {end_date}")
        response = await self._make_request(data)

        return response

    async def get_agent_activity_report(
        self,
        start_date: date,
        end_date: date,
        agent_ids: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Obtém relatório de atividade dos agentes.

        Args:
            start_date: Data inicial do relatório
            end_date: data final do relatório
            agent_ids: Lista de IDs dos agentes (opcional)

        Returns:
            Dados de atividade dos agentes
        """
        data = {
            "data_inicial": start_date.strftime("%Y-%m-%d"),
            "data_final": end_date.strftime("%Y-%m-%d"),
            "relatorio": "Atividade Agentes"
        }

        if agent_ids:
            data["agentes"] = agent_ids

        logger.info(f"Solicitando relatório de atividade de agentes para o período de {start_date} a {end_date}")
        response = await self._make_request(data)

        return response

    async def get_queue_statistics_report(
        self,
        start_date: date,
        end_date: date,
        queue_ids: Optional[List[int]] = None
    ) -> Dict[str, Any]:
        """
        Obtém estatísticas detalhadas por fila.

        Args:
            start_date: Data inicial do relatório
            end_date: Data final do relatório
            queue_ids: Lista de IDs das filas (opcional)

        Returns:
            Estatísticas por fila
        """
        data = {
            "data_inicial": start_date.strftime("%Y-%m-%d"),
            "data_final": end_date.strftime("%Y-%m-%d"),
            "relatorio": "Estatísticas Filas"
        }

        if queue_ids:
            data["filas"] = queue_ids

        logger.info(f"Solicitando estatísticas de filas para o período de {start_date} a {end_date}")
        response = await self._make_request(data)

        return response
