import httpx
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.schemas.threecx import (
    CallRecord, 
    CallRecordResponse,
    AgentStatus,
    AgentStatusEnum,
    WebhookPayload
)

logger = logging.getLogger(__name__)

class ThreeCXClient:
    """
    Cliente para interagir com a API do 3CX.
    """
    
    def __init__(self, base_url: str, api_key: str, tenant_id: str):
        """
        Inicializa o cliente 3CX.
        
        Args:
            base_url: URL base da API do 3CX
            api_key: Chave de API para autenticação
            tenant_id: ID do tenant no 3CX
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.tenant_id = tenant_id
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}",
            "X-Tenant-Id": tenant_id
        }
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Faz uma requisição para a API do 3CX.
        
        Args:
            method: Método HTTP (GET, POST, PUT, DELETE)
            endpoint: Endpoint da API
            params: Parâmetros de consulta
            data: Dados para enviar no corpo da requisição
            
        Returns:
            Resposta da API como um dicionário
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=self.headers, params=params)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=self.headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=self.headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=self.headers, params=params)
                else:
                    raise ValueError(f"Método HTTP não suportado: {method}")
                
                response.raise_for_status()
                return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"Erro HTTP ao acessar a API do 3CX: {e}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Erro de requisição ao acessar a API do 3CX: {e}")
            raise
        except Exception as e:
            logger.error(f"Erro inesperado ao acessar a API do 3CX: {e}")
            raise
    
    async def get_calls(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        agent_id: Optional[str] = None,
        client_number: Optional[str] = None
    ) -> List[CallRecordResponse]:
        """
        Recupera o histórico de chamadas com base nos filtros fornecidos.
        
        Args:
            start_date: Data de início no formato ISO (YYYY-MM-DD)
            end_date: Data de fim no formato ISO (YYYY-MM-DD)
            agent_id: ID do agente para filtrar
            client_number: Número do cliente para filtrar
            
        Returns:
            Lista de registros de chamadas
        """
        params = {}
        if start_date:
            params["startDate"] = start_date
        if end_date:
            params["endDate"] = end_date
        if agent_id:
            params["agentId"] = agent_id
        if client_number:
            params["clientNumber"] = client_number
            
        response = await self._make_request("GET", "/api/calls", params=params)
        
        # Converter a resposta para objetos CallRecordResponse
        calls = []
        for call_data in response.get("calls", []):
            # Ajustar os campos conforme necessário para corresponder ao esquema
            call_record = CallRecordResponse(
                id=call_data["id"],
                caller_number=call_data["callerNumber"],
                caller_name=call_data.get("callerName"),
                called_number=call_data["calledNumber"],
                called_name=call_data.get("calledName"),
                direction=call_data["direction"],
                duration=call_data["duration"],
                status=call_data["status"],
                agent_id=call_data.get("agentId"),
                agent_name=call_data.get("agentName"),
                recording_url=call_data.get("recordingUrl"),
                notes=call_data.get("notes"),
                start_time=datetime.fromisoformat(call_data["startTime"]),
                end_time=datetime.fromisoformat(call_data["endTime"]) if call_data.get("endTime") else None,
                created_at=datetime.fromisoformat(call_data["createdAt"]),
                updated_at=datetime.fromisoformat(call_data["updatedAt"]) if call_data.get("updatedAt") else None
            )
            calls.append(call_record)
            
        return calls
    
    async def get_call(self, call_id: str) -> Optional[CallRecordResponse]:
        """
        Recupera os detalhes de uma chamada específica pelo ID.
        
        Args:
            call_id: ID da chamada
            
        Returns:
            Detalhes da chamada ou None se não encontrada
        """
        try:
            response = await self._make_request("GET", f"/api/calls/{call_id}")
            
            # Converter a resposta para um objeto CallRecordResponse
            call_data = response.get("call")
            if not call_data:
                return None
                
            call_record = CallRecordResponse(
                id=call_data["id"],
                caller_number=call_data["callerNumber"],
                caller_name=call_data.get("callerName"),
                called_number=call_data["calledNumber"],
                called_name=call_data.get("calledName"),
                direction=call_data["direction"],
                duration=call_data["duration"],
                status=call_data["status"],
                agent_id=call_data.get("agentId"),
                agent_name=call_data.get("agentName"),
                recording_url=call_data.get("recordingUrl"),
                notes=call_data.get("notes"),
                start_time=datetime.fromisoformat(call_data["startTime"]),
                end_time=datetime.fromisoformat(call_data["endTime"]) if call_data.get("endTime") else None,
                created_at=datetime.fromisoformat(call_data["createdAt"]),
                updated_at=datetime.fromisoformat(call_data["updatedAt"]) if call_data.get("updatedAt") else None
            )
            
            return call_record
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return None
            raise
    
    async def get_agents_status(self) -> List[AgentStatus]:
        """
        Recupera o status de todos os agentes.
        
        Returns:
            Lista de status dos agentes
        """
        response = await self._make_request("GET", "/api/agents")
        
        # Converter a resposta para objetos AgentStatus
        agents = []
        for agent_data in response.get("agents", []):
            agent_status = AgentStatus(
                id=agent_data["id"],
                name=agent_data["name"],
                extension=agent_data["extension"],
                status=agent_data["status"],
                current_call_id=agent_data.get("currentCallId"),
                last_activity=datetime.fromisoformat(agent_data["lastActivity"]) if agent_data.get("lastActivity") else None
            )
            agents.append(agent_status)
            
        return agents
    
    async def update_agent_status(self, agent_id: str, status: AgentStatusEnum) -> AgentStatus:
        """
        Atualiza o status de um agente específico.
        
        Args:
            agent_id: ID do agente
            status: Novo status do agente
            
        Returns:
            Status atualizado do agente
        """
        data = {"status": status}
        response = await self._make_request("PUT", f"/api/agents/{agent_id}/status", data=data)
        
        # Converter a resposta para um objeto AgentStatus
        agent_data = response.get("agent")
        agent_status = AgentStatus(
            id=agent_data["id"],
            name=agent_data["name"],
            extension=agent_data["extension"],
            status=agent_data["status"],
            current_call_id=agent_data.get("currentCallId"),
            last_activity=datetime.fromisoformat(agent_data["lastActivity"]) if agent_data.get("lastActivity") else None
        )
        
        return agent_status
    
    async def process_webhook(self, payload: dict) -> None:
        """
        Processa um webhook recebido do 3CX.
        
        Args:
            payload: Dados do webhook
        """
        try:
            # Validar o payload com o esquema WebhookPayload
            webhook = WebhookPayload(**payload)
            
            # Processar o webhook com base no tipo de evento
            event = webhook.event
            data = webhook.data
            
            logger.info(f"Recebido webhook do 3CX: {event}")
            logger.debug(f"Dados do webhook: {json.dumps(data)}")
            
            # Implementar a lógica de processamento específica para cada tipo de evento
            # Esta é uma implementação de exemplo que pode ser expandida conforme necessário
            if event == "call_started":
                # Lógica para chamada iniciada
                pass
            elif event == "call_ended":
                # Lógica para chamada finalizada
                pass
            elif event == "call_answered":
                # Lógica para chamada atendida
                pass
            elif event == "call_missed":
                # Lógica para chamada perdida
                pass
            elif event == "agent_status_changed":
                # Lógica para mudança de status do agente
                pass
            else:
                logger.warning(f"Tipo de evento desconhecido: {event}")
                
        except Exception as e:
            logger.error(f"Erro ao processar webhook do 3CX: {e}")
            raise
