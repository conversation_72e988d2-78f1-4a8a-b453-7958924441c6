"""
Serviço de autenticação OAuth2 para Microsoft Graph API.
"""

import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from urllib.parse import urlencode

import msal
from msal import ConfidentialClientApplication
from app.config.settings import settings
from .models import OutlookTokens, OutlookUser

logger = logging.getLogger(__name__)


class OutlookAuthService:
    """Serviço de autenticação para Microsoft Outlook/Graph API."""

    def __init__(self):
        """Inicializa o serviço de autenticação."""
        self.client_id = settings.MICROSOFT_CLIENT_ID
        self.client_secret = settings.MICROSOFT_CLIENT_SECRET
        self.tenant_id = settings.MICROSOFT_TENANT_ID
        self.redirect_uri = settings.MICROSOFT_REDIRECT_URI
        self.scopes = settings.microsoft_scopes_list
        self.app = None
        self.is_configured = False

        # Verificar se as configurações estão disponíveis
        if all([self.client_id, self.client_secret, self.tenant_id]) and \
           not any([
               self.client_id == "your_client_id_here",
               self.client_secret == "your_client_secret_here",
               self.tenant_id == "your_tenant_id_here"
           ]):
            try:
                # Criar aplicação MSAL apenas se as configurações estão corretas
                self.app = ConfidentialClientApplication(
                    client_id=self.client_id,
                    client_credential=self.client_secret,
                    authority=f"https://login.microsoftonline.com/{self.tenant_id}",
                )
                self.is_configured = True
                logger.info("Serviço de autenticação Outlook configurado com sucesso")
            except Exception as e:
                logger.warning(f"Erro ao configurar autenticação Outlook: {e}")
                self.is_configured = False
        else:
            logger.warning(
                "Configurações do Microsoft Graph não encontradas ou incompletas. "
                "Configure as variáveis MICROSOFT_CLIENT_ID, MICROSOFT_CLIENT_SECRET e MICROSOFT_TENANT_ID."
            )

    def get_authorization_url(self, state: Optional[str] = None, force_account_selection: bool = False) -> str:
        """
        Gera URL de autorização para OAuth2.

        Args:
            state: Estado opcional para validação
            force_account_selection: Se True, força a seleção de conta

        Returns:
            URL de autorização
        """
        if not self.is_configured:
            raise ValueError(
                "Integração Outlook não configurada. "
                "Configure as credenciais do Microsoft Azure primeiro."
            )

        try:
            logger.info(f"Gerando URL de autorização com force_account_selection={force_account_selection}")

            # Gerar URL base
            auth_url = self.app.get_authorization_request_url(
                scopes=self.scopes,
                redirect_uri=self.redirect_uri,
                state=state
            )

            logger.info(f"URL base gerada: {auth_url}")

            # Se forçar seleção de conta, adicionar parâmetro prompt
            if force_account_selection:
                separator = "&" if "?" in auth_url else "?"
                auth_url += f"{separator}prompt=select_account"
                logger.info(f"Parâmetro 'prompt=select_account' adicionado. URL final: {auth_url}")

            logger.info(f"URL de autorização final: {auth_url}")
            return auth_url

        except Exception as e:
            logger.error(f"Erro ao gerar URL de autorização: {e}")
            raise

    def exchange_code_for_tokens(self, authorization_code: str) -> OutlookTokens:
        """
        Troca código de autorização por tokens de acesso.

        Args:
            authorization_code: Código de autorização recebido

        Returns:
            Tokens de acesso
        """
        try:
            result = self.app.acquire_token_by_authorization_code(
                code=authorization_code,
                scopes=self.scopes,
                redirect_uri=self.redirect_uri
            )

            if "error" in result:
                error_msg = f"Erro na autenticação: {result.get('error_description', result['error'])}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Calcular data de expiração com timezone
            expires_in = result.get("expires_in", 3600)
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

            tokens = OutlookTokens(
                access_token=result["access_token"],
                refresh_token=result.get("refresh_token"),
                expires_at=expires_at,
                scope=result.get("scope", "").split(" "),
                token_type=result.get("token_type", "Bearer")
            )

            logger.info("Tokens obtidos com sucesso")
            return tokens

        except Exception as e:
            logger.error(f"Erro ao trocar código por tokens: {e}")
            raise

    def refresh_access_token(self, refresh_token: str) -> OutlookTokens:
        """
        Atualiza token de acesso usando refresh token.

        Args:
            refresh_token: Token de atualização

        Returns:
            Novos tokens de acesso
        """
        try:
            result = self.app.acquire_token_by_refresh_token(
                refresh_token=refresh_token,
                scopes=self.scopes
            )

            if "error" in result:
                error_msg = f"Erro ao atualizar token: {result.get('error_description', result['error'])}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Calcular data de expiração com timezone
            expires_in = result.get("expires_in", 3600)
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)

            tokens = OutlookTokens(
                access_token=result["access_token"],
                refresh_token=result.get("refresh_token", refresh_token),
                expires_at=expires_at,
                scope=result.get("scope", "").split(" "),
                token_type=result.get("token_type", "Bearer")
            )

            logger.info("Token atualizado com sucesso")
            return tokens

        except Exception as e:
            logger.error(f"Erro ao atualizar token: {e}")
            raise

    def is_token_valid(self, tokens: OutlookTokens) -> bool:
        """
        Verifica se o token ainda é válido.

        Args:
            tokens: Tokens para verificar

        Returns:
            True se válido, False caso contrário
        """
        if not tokens.access_token:
            return False

        # Verificar se o token expira em menos de 5 minutos
        buffer_time = timedelta(minutes=5)
        now = datetime.now(timezone.utc)
        expires_at = tokens.expires_at.replace(tzinfo=timezone.utc) if tokens.expires_at.tzinfo is None else tokens.expires_at
        return now + buffer_time < expires_at

    def get_valid_token(self, tokens: OutlookTokens) -> OutlookTokens:
        """
        Obtém um token válido, atualizando se necessário.

        Args:
            tokens: Tokens atuais

        Returns:
            Tokens válidos
        """
        if self.is_token_valid(tokens):
            return tokens

        if not tokens.refresh_token:
            raise ValueError("Token expirado e refresh token não disponível")

        return self.refresh_access_token(tokens.refresh_token)

    def revoke_tokens(self, tokens: OutlookTokens) -> bool:
        """
        Revoga tokens de acesso.

        Args:
            tokens: Tokens para revogar

        Returns:
            True se revogado com sucesso
        """
        try:
            # Microsoft Graph não tem endpoint específico para revogar tokens
            # Os tokens expiram automaticamente
            logger.info("Tokens marcados para expiração")
            return True

        except Exception as e:
            logger.error(f"Erro ao revogar tokens: {e}")
            return False
