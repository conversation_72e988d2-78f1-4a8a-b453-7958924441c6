"""
Serviço de sincronização para integração com Outlook.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from .models import (
    OutlookTokens, OutlookConfig, SyncResult, 
    OutlookIntegrationStatus, EmailMessage, EmailFolder
)
from .client import OutlookGraphClient

logger = logging.getLogger(__name__)


class OutlookSyncService:
    """Serviço de sincronização com Outlook."""
    
    def __init__(self):
        """Inicializa o serviço de sincronização."""
        self.active_syncs: Dict[str, bool] = {}
        
    async def sync_user_data(
        self, 
        tokens: OutlookTokens, 
        config: OutlookConfig
    ) -> SyncResult:
        """
        Sincroniza dados do usuário com Outlook.
        
        Args:
            tokens: Tokens de autenticação
            config: Configuração de sincronização
            
        Returns:
            Resultado da sincronização
        """
        start_time = datetime.utcnow()
        sync_result = SyncResult(
            success=False,
            sync_time=start_time,
            duration_seconds=0.0
        )
        
        # Verificar se já há sincronização em andamento
        if self.active_syncs.get(config.user_id, False):
            sync_result.errors.append("Sincronização já em andamento para este usuário")
            return sync_result
        
        try:
            self.active_syncs[config.user_id] = True
            client = OutlookGraphClient(tokens)
            
            logger.info(f"Iniciando sincronização para usuário {config.user_id}")
            
            # Sincronizar pastas
            folders = await self._sync_folders(client, config)
            sync_result.folders_synced = len(folders)
            
            # Sincronizar mensagens das pastas configuradas
            messages_count = 0
            for folder_name in config.sync_folders:
                folder = self._find_folder_by_name(folders, folder_name)
                if folder:
                    messages = await self._sync_folder_messages(client, folder, config)
                    messages_count += len(messages)
                else:
                    sync_result.errors.append(f"Pasta '{folder_name}' não encontrada")
            
            sync_result.messages_synced = messages_count
            sync_result.success = True
            
            # Atualizar última sincronização
            config.last_sync = datetime.utcnow()
            
            logger.info(
                f"Sincronização concluída: {sync_result.folders_synced} pastas, "
                f"{sync_result.messages_synced} mensagens"
            )
            
        except Exception as e:
            error_msg = f"Erro durante sincronização: {str(e)}"
            logger.error(error_msg)
            sync_result.errors.append(error_msg)
            
        finally:
            self.active_syncs[config.user_id] = False
            end_time = datetime.utcnow()
            sync_result.duration_seconds = (end_time - start_time).total_seconds()
            
        return sync_result
    
    async def _sync_folders(
        self, 
        client: OutlookGraphClient, 
        config: OutlookConfig
    ) -> List[EmailFolder]:
        """
        Sincroniza pastas do usuário.
        
        Args:
            client: Cliente Graph
            config: Configuração
            
        Returns:
            Lista de pastas sincronizadas
        """
        try:
            folders = await client.get_folders()
            logger.info(f"Sincronizadas {len(folders)} pastas")
            return folders
            
        except Exception as e:
            logger.error(f"Erro ao sincronizar pastas: {e}")
            raise
    
    async def _sync_folder_messages(
        self,
        client: OutlookGraphClient,
        folder: EmailFolder,
        config: OutlookConfig,
        limit: int = 100
    ) -> List[EmailMessage]:
        """
        Sincroniza mensagens de uma pasta.
        
        Args:
            client: Cliente Graph
            folder: Pasta para sincronizar
            config: Configuração
            limit: Limite de mensagens
            
        Returns:
            Lista de mensagens sincronizadas
        """
        try:
            # Filtrar mensagens por data se configurado
            filter_query = None
            if config.last_sync:
                # Sincronizar apenas mensagens desde a última sincronização
                last_sync_str = config.last_sync.isoformat()
                filter_query = f"receivedDateTime ge {last_sync_str}"
            
            messages = await client.get_messages(
                folder_id=folder.id,
                limit=limit,
                filter_query=filter_query
            )
            
            logger.info(f"Sincronizadas {len(messages)} mensagens da pasta '{folder.display_name}'")
            return messages
            
        except Exception as e:
            logger.error(f"Erro ao sincronizar mensagens da pasta '{folder.display_name}': {e}")
            raise
    
    def _find_folder_by_name(
        self, 
        folders: List[EmailFolder], 
        folder_name: str
    ) -> Optional[EmailFolder]:
        """
        Encontra pasta por nome.
        
        Args:
            folders: Lista de pastas
            folder_name: Nome da pasta
            
        Returns:
            Pasta encontrada ou None
        """
        folder_name_lower = folder_name.lower()
        
        # Mapear nomes comuns
        folder_mapping = {
            "inbox": ["inbox", "caixa de entrada", "recebidos"],
            "sent": ["sent items", "itens enviados", "enviados"],
            "drafts": ["drafts", "rascunhos"],
            "deleted": ["deleted items", "itens excluídos", "lixeira"],
            "junk": ["junk email", "lixo eletrônico", "spam"]
        }
        
        # Buscar por mapeamento
        possible_names = folder_mapping.get(folder_name_lower, [folder_name_lower])
        
        for folder in folders:
            if folder.display_name.lower() in possible_names:
                return folder
        
        return None
    
    async def get_integration_status(
        self, 
        tokens: OutlookTokens, 
        config: OutlookConfig
    ) -> OutlookIntegrationStatus:
        """
        Obtém status da integração.
        
        Args:
            tokens: Tokens de autenticação
            config: Configuração
            
        Returns:
            Status da integração
        """
        try:
            client = OutlookGraphClient(tokens)
            
            # Obter informações do usuário
            user_info = await client.get_user_info()
            
            # Obter estatísticas básicas
            folders = await client.get_folders()
            
            # Contar mensagens (aproximado)
            total_messages = sum(folder.total_item_count for folder in folders)
            
            # Verificar se há sincronização em andamento
            sync_status = "syncing" if self.active_syncs.get(config.user_id, False) else "idle"
            
            return OutlookIntegrationStatus(
                is_connected=True,
                user_info=user_info,
                last_sync=config.last_sync,
                sync_status=sync_status,
                folders_count=len(folders),
                messages_count=total_messages
            )
            
        except Exception as e:
            logger.error(f"Erro ao obter status da integração: {e}")
            return OutlookIntegrationStatus(
                is_connected=False,
                error_message=str(e)
            )
    
    async def start_auto_sync(
        self, 
        tokens: OutlookTokens, 
        config: OutlookConfig
    ) -> None:
        """
        Inicia sincronização automática.
        
        Args:
            tokens: Tokens de autenticação
            config: Configuração
        """
        if not config.is_enabled:
            return
        
        logger.info(f"Iniciando sincronização automática para usuário {config.user_id}")
        
        while config.is_enabled:
            try:
                await self.sync_user_data(tokens, config)
                await asyncio.sleep(config.auto_sync_interval)
                
            except Exception as e:
                logger.error(f"Erro na sincronização automática: {e}")
                await asyncio.sleep(60)  # Aguardar 1 minuto antes de tentar novamente
