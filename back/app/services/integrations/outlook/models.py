"""
Modelos de dados para integração com Microsoft Outlook/Graph API.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class OutlookTokens(BaseModel):
    """Modelo para tokens de autenticação do Outlook."""
    access_token: str
    refresh_token: Optional[str] = None
    expires_at: datetime
    scope: List[str] = []
    token_type: str = "Bearer"


class OutlookUser(BaseModel):
    """Modelo para informações do usuário do Outlook."""
    id: str
    display_name: str
    email: str
    user_principal_name: str
    given_name: Optional[str] = None
    surname: Optional[str] = None
    job_title: Optional[str] = None
    office_location: Optional[str] = None


class EmailAddress(BaseModel):
    """Modelo para endereço de email."""
    name: Optional[str] = None
    address: str


class EmailRecipient(BaseModel):
    """Modelo para destinatário de email."""
    email_address: EmailAddress


class EmailAttachment(BaseModel):
    """Modelo para anexo de email."""
    id: Optional[str] = None
    name: str
    content_type: str
    size: int
    is_inline: bool = False
    content_bytes: Optional[str] = None  # Base64 encoded


class EmailMessage(BaseModel):
    """Modelo para mensagem de email."""
    id: Optional[str] = None
    subject: str
    body_content: str
    body_content_type: str = "HTML"  # HTML ou Text
    from_: Optional[EmailRecipient] = Field(None, alias="from")
    to_recipients: List[EmailRecipient] = []
    cc_recipients: List[EmailRecipient] = []
    bcc_recipients: List[EmailRecipient] = []
    attachments: List[EmailAttachment] = []
    importance: str = "normal"  # low, normal, high
    is_read: bool = False
    received_date_time: Optional[datetime] = None
    sent_date_time: Optional[datetime] = None
    created_date_time: Optional[datetime] = None
    last_modified_date_time: Optional[datetime] = None
    conversation_id: Optional[str] = None
    internet_message_id: Optional[str] = None
    web_link: Optional[str] = None

    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class EmailFolder(BaseModel):
    """Modelo para pasta de email."""
    id: str
    display_name: str
    parent_folder_id: Optional[str] = None
    child_folder_count: int = 0
    unread_item_count: int = 0
    total_item_count: int = 0


class OutlookConfig(BaseModel):
    """Modelo para configuração da integração Outlook."""
    user_id: str
    is_enabled: bool = True
    sync_folders: List[str] = ["inbox", "sent", "drafts"]
    auto_sync_interval: int = 300  # segundos
    last_sync: Optional[datetime] = None
    sync_direction: str = "bidirectional"  # incoming, outgoing, bidirectional


class SyncResult(BaseModel):
    """Modelo para resultado de sincronização."""
    success: bool
    messages_synced: int = 0
    folders_synced: int = 0
    errors: List[str] = []
    sync_time: datetime
    duration_seconds: float


class OutlookIntegrationStatus(BaseModel):
    """Modelo para status da integração."""
    is_connected: bool
    user_info: Optional[OutlookUser] = None
    last_sync: Optional[datetime] = None
    sync_status: str = "idle"  # idle, syncing, error
    error_message: Optional[str] = None
    folders_count: int = 0
    messages_count: int = 0


class SendEmailRequest(BaseModel):
    """Modelo para requisição de envio de email."""
    to: List[str]
    cc: List[str] = []
    bcc: List[str] = []
    subject: str
    body: str
    body_type: str = "HTML"  # HTML ou Text
    importance: str = "normal"  # low, normal, high
    attachments: List[Dict[str, Any]] = []


class EmailSearchRequest(BaseModel):
    """Modelo para requisição de busca de emails."""
    query: Optional[str] = None
    folder_id: Optional[str] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None
    is_read: Optional[bool] = None
    has_attachments: Optional[bool] = None
    importance: Optional[str] = None
    limit: int = 50
    offset: int = 0


class EmailSearchResponse(BaseModel):
    """Modelo para resposta de busca de emails."""
    messages: List[EmailMessage]
    total_count: int
    has_more: bool
    next_link: Optional[str] = None
