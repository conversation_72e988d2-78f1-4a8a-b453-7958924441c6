"""
Gerenciador de tokens do Outlook com persistência no banco de dados.
"""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Tuple
from sqlalchemy.orm import Session

from app.crud.outlook_tokens import outlook_tokens_crud
from app.models.outlook_tokens import OutlookTokenDB
from app.services.integrations.outlook.models import OutlookTokens, OutlookUser
from app.services.integrations.outlook.auth import OutlookAuthService

logger = logging.getLogger(__name__)


class OutlookTokenManager:
    """Gerenciador de tokens do Outlook com persistência."""
    
    def __init__(self):
        self.auth_service = OutlookAuthService()
    
    def get_valid_tokens(self, db: Session, user_id: int) -> Optional[Tuple[OutlookTokens, OutlookUser]]:
        """
        Obtém tokens válidos para o usuário, renovando se necessário.
        
        Args:
            db: Sessão do banco
            user_id: ID do usuário
            
        Returns:
            Tupla (tokens, user_info) ou None se não autenticado
        """
        try:
            # Buscar tokens no banco
            token_record = outlook_tokens_crud.get_tokens_by_user_id(db, user_id)
            if not token_record:
                logger.debug(f"Nenhum token encontrado para usuário {user_id}")
                return None
            
            # Verificar se o token está válido
            if not token_record.is_expired() and not token_record.expires_soon():
                logger.debug(f"Token válido para usuário {user_id}")
                tokens = outlook_tokens_crud.to_outlook_tokens(token_record)
                user_info = outlook_tokens_crud.to_outlook_user(token_record)
                return tokens, user_info
            
            # Token expirou ou expira em breve, tentar renovar
            if token_record.refresh_token:
                logger.info(f"Renovando token para usuário {user_id}")
                try:
                    new_tokens = self.auth_service.refresh_access_token(token_record.refresh_token)
                    
                    # Atualizar no banco
                    updated_record = outlook_tokens_crud.update_tokens(db, token_record, new_tokens)
                    
                    tokens = outlook_tokens_crud.to_outlook_tokens(updated_record)
                    user_info = outlook_tokens_crud.to_outlook_user(updated_record)
                    
                    logger.info(f"Token renovado com sucesso para usuário {user_id}")
                    return tokens, user_info
                    
                except Exception as e:
                    logger.error(f"Erro ao renovar token para usuário {user_id}: {e}")
                    # Desativar tokens inválidos
                    outlook_tokens_crud.deactivate_tokens(db, user_id)
                    return None
            else:
                logger.warning(f"Token expirado sem refresh token para usuário {user_id}")
                outlook_tokens_crud.deactivate_tokens(db, user_id)
                return None
                
        except Exception as e:
            logger.error(f"Erro ao obter tokens válidos para usuário {user_id}: {e}")
            return None
    
    def store_tokens(
        self, 
        db: Session, 
        user_id: int, 
        tokens: OutlookTokens, 
        outlook_user: OutlookUser
    ) -> bool:
        """
        Armazena tokens no banco de dados.
        
        Args:
            db: Sessão do banco
            user_id: ID do usuário
            tokens: Tokens do Outlook
            outlook_user: Informações do usuário Outlook
            
        Returns:
            True se armazenado com sucesso
        """
        try:
            outlook_tokens_crud.create_or_update_tokens(db, user_id, tokens, outlook_user)
            logger.info(f"Tokens armazenados com sucesso para usuário {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao armazenar tokens para usuário {user_id}: {e}")
            return False
    
    def revoke_tokens(self, db: Session, user_id: int) -> bool:
        """
        Revoga tokens do usuário.
        
        Args:
            db: Sessão do banco
            user_id: ID do usuário
            
        Returns:
            True se revogado com sucesso
        """
        try:
            # Desativar no banco
            success = outlook_tokens_crud.deactivate_tokens(db, user_id)
            
            if success:
                logger.info(f"Tokens revogados para usuário {user_id}")
            else:
                logger.warning(f"Nenhum token encontrado para revogar para usuário {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Erro ao revogar tokens para usuário {user_id}: {e}")
            return False
    
    def is_user_connected(self, db: Session, user_id: int) -> bool:
        """
        Verifica se o usuário está conectado ao Outlook.
        
        Args:
            db: Sessão do banco
            user_id: ID do usuário
            
        Returns:
            True se conectado
        """
        try:
            token_record = outlook_tokens_crud.get_tokens_by_user_id(db, user_id)
            if not token_record:
                return False
            
            # Verificar se não está expirado
            return not token_record.is_expired()
            
        except Exception as e:
            logger.error(f"Erro ao verificar conexão para usuário {user_id}: {e}")
            return False
    
    def get_user_info(self, db: Session, user_id: int) -> Optional[OutlookUser]:
        """
        Obtém informações do usuário Outlook.
        
        Args:
            db: Sessão do banco
            user_id: ID do usuário
            
        Returns:
            Informações do usuário ou None
        """
        try:
            token_record = outlook_tokens_crud.get_tokens_by_user_id(db, user_id)
            if not token_record:
                return None
            
            return outlook_tokens_crud.to_outlook_user(token_record)
            
        except Exception as e:
            logger.error(f"Erro ao obter informações do usuário {user_id}: {e}")
            return None
    
    def cleanup_expired_tokens(self, db: Session) -> int:
        """
        Remove tokens expirados antigos.
        
        Args:
            db: Sessão do banco
            
        Returns:
            Número de tokens removidos
        """
        try:
            count = outlook_tokens_crud.cleanup_expired_tokens(db)
            logger.info(f"Removidos {count} tokens expirados")
            return count
            
        except Exception as e:
            logger.error(f"Erro ao limpar tokens expirados: {e}")
            return 0
    
    def get_connection_status(self, db: Session, user_id: int) -> dict:
        """
        Obtém status detalhado da conexão.
        
        Args:
            db: Sessão do banco
            user_id: ID do usuário
            
        Returns:
            Dicionário com status da conexão
        """
        try:
            token_record = outlook_tokens_crud.get_tokens_by_user_id(db, user_id)
            
            if not token_record:
                return {
                    "authenticated": False,
                    "user_info": None,
                    "expires_at": None,
                    "expires_soon": False,
                    "last_used": None
                }
            
            user_info = outlook_tokens_crud.to_outlook_user(token_record)
            
            return {
                "authenticated": not token_record.is_expired(),
                "user_info": {
                    "id": user_info.id,
                    "name": user_info.display_name,
                    "email": user_info.email
                },
                "expires_at": token_record.expires_at.isoformat(),
                "expires_soon": token_record.expires_soon(),
                "last_used": token_record.last_used.isoformat() if token_record.last_used else None
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter status da conexão para usuário {user_id}: {e}")
            return {
                "authenticated": False,
                "user_info": None,
                "expires_at": None,
                "expires_soon": False,
                "last_used": None,
                "error": str(e)
            }


# Instância global do gerenciador
outlook_token_manager = OutlookTokenManager()
