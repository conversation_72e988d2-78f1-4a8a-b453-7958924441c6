"""
Serviço para gerenciar tokens Outlook por usuário.
"""

import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

# from app.database.connection import get_db
# from app.models.usuario import Usuario
from .models import OutlookTokens, OutlookUser
from .auth import OutlookAuthService

logger = logging.getLogger(__name__)


class UserOutlookTokenService:
    """Serviço para gerenciar tokens Outlook por usuário."""

    def __init__(self):
        """Inicializa o serviço."""
        self.auth_service = OutlookAuthService()
        # Em produção, usar Redis ou banco de dados
        self._user_tokens: Dict[int, OutlookTokens] = {}
        self._user_outlook_info: Dict[int, OutlookUser] = {}

    def get_user_tokens(self, user_id: int) -> Optional[OutlookTokens]:
        """
        Obtém tokens do usuário.

        Args:
            user_id: ID do usuário

        Returns:
            Tokens se existirem e válidos, None caso contrário
        """
        tokens = self._user_tokens.get(user_id)
        if not tokens:
            return None

        # Verificar se o token ainda é válido
        if tokens.expires_at and tokens.expires_at <= datetime.utcnow():
            # Tentar renovar o token
            if tokens.refresh_token:
                try:
                    new_tokens = self.auth_service.refresh_token(tokens.refresh_token)
                    self._user_tokens[user_id] = new_tokens
                    logger.info(f"Token renovado para usuário {user_id}")
                    return new_tokens
                except Exception as e:
                    logger.error(f"Erro ao renovar token para usuário {user_id}: {e}")
                    # Remover tokens inválidos
                    self.remove_user_tokens(user_id)
                    return None
            else:
                # Token expirado sem refresh token
                self.remove_user_tokens(user_id)
                return None

        return tokens

    def store_user_tokens(self, user_id: int, tokens: OutlookTokens, outlook_user: OutlookUser):
        """
        Armazena tokens do usuário.

        Args:
            user_id: ID do usuário
            tokens: Tokens de acesso
            outlook_user: Informações do usuário Outlook
        """
        self._user_tokens[user_id] = tokens
        self._user_outlook_info[user_id] = outlook_user
        logger.info(f"Tokens armazenados para usuário {user_id} - Email: {outlook_user.email}")

    def remove_user_tokens(self, user_id: int) -> bool:
        """
        Remove tokens do usuário.

        Args:
            user_id: ID do usuário

        Returns:
            True se havia tokens para remover, False caso contrário
        """
        had_tokens = user_id in self._user_tokens
        self._user_tokens.pop(user_id, None)
        self._user_outlook_info.pop(user_id, None)

        if had_tokens:
            logger.info(f"Tokens removidos para usuário {user_id}")
        else:
            logger.info(f"Nenhum token encontrado para usuário {user_id}")

        return had_tokens

    def get_user_outlook_info(self, user_id: int) -> Optional[OutlookUser]:
        """
        Obtém informações do Outlook do usuário.

        Args:
            user_id: ID do usuário

        Returns:
            Informações do usuário Outlook se existirem
        """
        return self._user_outlook_info.get(user_id)

    def is_user_authenticated(self, user_id: int) -> bool:
        """
        Verifica se o usuário está autenticado no Outlook.

        Args:
            user_id: ID do usuário

        Returns:
            True se autenticado, False caso contrário
        """
        tokens = self.get_user_tokens(user_id)
        return tokens is not None

    def validate_email_domain(self, email: str) -> bool:
        """
        Valida se o email pertence ao domínio permitido.

        Args:
            email: Email a ser validado

        Returns:
            True se o domínio for válido, False caso contrário
        """
        allowed_domains = ["amvox.com.br", "amvoxdev.onmicrosoft.com"]
        email_domain = email.split("@")[-1].lower()
        return email_domain in allowed_domains

    def get_authorization_url_for_user(self, user_id: int) -> str:
        """
        Gera URL de autorização para um usuário específico.

        Args:
            user_id: ID do usuário

        Returns:
            URL de autorização
        """
        state = f"user_{user_id}"
        return self.auth_service.get_authorization_url(state=state)

    async def process_callback_for_user(self, user_id: int, authorization_code: str) -> Dict[str, Any]:
        """
        Processa callback de autenticação para um usuário.

        Args:
            user_id: ID do usuário
            authorization_code: Código de autorização

        Returns:
            Resultado do processamento
        """
        try:
            # Trocar código por tokens
            tokens = self.auth_service.exchange_code_for_tokens(authorization_code)

            # Obter informações do usuário
            from .client import OutlookGraphClient
            client = OutlookGraphClient(tokens)

            # Usar método assíncrono para obter informações do usuário
            outlook_user = await client.get_user_info()

            # Validar domínio do email
            if not self.validate_email_domain(outlook_user.email):
                raise ValueError(f"Email {outlook_user.email} não pertence ao domínio permitido (@amvox.com.br)")

            # Armazenar tokens
            self.store_user_tokens(user_id, tokens, outlook_user)

            return {
                "success": True,
                "message": "Autenticação Outlook realizada com sucesso",
                "user_email": outlook_user.email,
                "user_name": outlook_user.display_name
            }

        except Exception as e:
            logger.error(f"Erro no callback para usuário {user_id}: {e}")
            return {
                "success": False,
                "message": str(e)
            }


# Instância global do serviço
user_outlook_service = UserOutlookTokenService()
