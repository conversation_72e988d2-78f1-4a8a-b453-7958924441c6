"""
Cliente para Microsoft Graph API - Integração com Outlook.
"""

import json
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
import httpx
from .models import (
    OutlookTokens, OutlookUser, EmailMessage, EmailFolder,
    EmailRecipient, EmailAddress, SendEmailRequest, EmailSearchRequest,
    EmailSearchResponse
)
from .auth import OutlookAuthService

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class OutlookGraphClient:
    """Cliente para Microsoft Graph API."""

    def __init__(self, tokens: OutlookTokens):
        """
        Inicializa o cliente Graph.

        Args:
            tokens: Tokens de autenticação
        """
        self.tokens = tokens
        self.auth_service = OutlookAuthService()
        self.base_url = "https://graph.microsoft.com/v1.0"

    def _get_headers(self) -> Dict[str, str]:
        """Obtém headers para requisições."""
        # Garantir que temos um token válido
        valid_tokens = self.auth_service.get_valid_token(self.tokens)
        self.tokens = valid_tokens

        return {
            "Authorization": f"Bearer {self.tokens.access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

    async def get_user_info(self) -> OutlookUser:
        """
        Obtém informações do usuário autenticado.

        Returns:
            Informações do usuário
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/me",
                    headers=self._get_headers()
                )
                response.raise_for_status()

                data = response.json()
                return OutlookUser(
                    id=data["id"],
                    display_name=data["displayName"],
                    email=data["mail"] or data["userPrincipalName"],
                    user_principal_name=data["userPrincipalName"],
                    given_name=data.get("givenName"),
                    surname=data.get("surname"),
                    job_title=data.get("jobTitle"),
                    office_location=data.get("officeLocation")
                )

        except Exception as e:
            logger.error(f"Erro ao obter informações do usuário: {e}")
            raise

    async def get_folders(self) -> List[EmailFolder]:
        """
        Obtém pastas de email do usuário.

        Returns:
            Lista de pastas
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/me/mailFolders",
                    headers=self._get_headers()
                )
                response.raise_for_status()

                data = response.json()
                folders = []

                for folder_data in data.get("value", []):
                    folder = EmailFolder(
                        id=folder_data["id"],
                        display_name=folder_data["displayName"],
                        parent_folder_id=folder_data.get("parentFolderId"),
                        child_folder_count=folder_data.get("childFolderCount", 0),
                        unread_item_count=folder_data.get("unreadItemCount", 0),
                        total_item_count=folder_data.get("totalItemCount", 0)
                    )
                    folders.append(folder)

                return folders

        except Exception as e:
            logger.error(f"Erro ao obter pastas: {e}")
            raise

    async def get_messages_raw(
        self,
        folder_id: str = "inbox",
        limit: int = 50,
        filter_query: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Obtém dados brutos das mensagens de uma pasta.

        Args:
            folder_id: ID da pasta (padrão: inbox)
            limit: Limite de mensagens
            filter_query: Filtro OData opcional

        Returns:
            Lista de dados brutos das mensagens
        """
        try:
            url = f"{self.base_url}/me/mailFolders/{folder_id}/messages"
            params = {
                "$top": limit,
                "$orderby": "receivedDateTime desc",
                "$select": "id,subject,body,from,toRecipients,ccRecipients,bccRecipients,importance,isRead,receivedDateTime,sentDateTime,conversationId,internetMessageId,webLink,sender,replyTo,isDraft"
            }

            if filter_query:
                params["$filter"] = filter_query

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers=self._get_headers(),
                    params=params
                )
                response.raise_for_status()

                data = response.json()
                return data.get("value", [])

        except Exception as e:
            logger.error(f"Erro ao obter mensagens brutas: {e}")
            raise

    async def get_messages(
        self,
        folder_id: str = "inbox",
        limit: int = 50,
        filter_query: Optional[str] = None
    ) -> List[EmailMessage]:
        """
        Obtém mensagens de uma pasta.

        Args:
            folder_id: ID da pasta (padrão: inbox)
            limit: Limite de mensagens
            filter_query: Filtro OData opcional

        Returns:
            Lista de mensagens
        """
        try:
            url = f"{self.base_url}/me/mailFolders/{folder_id}/messages"
            params = {
                "$top": limit,
                "$orderby": "receivedDateTime desc",
                "$select": "id,subject,body,from,toRecipients,ccRecipients,bccRecipients,importance,isRead,receivedDateTime,sentDateTime,conversationId,internetMessageId,webLink,sender,replyTo,isDraft"
            }

            if filter_query:
                params["$filter"] = filter_query

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers=self._get_headers(),
                    params=params
                )
                response.raise_for_status()

                data = response.json()
                messages = []

                for msg_data in data.get("value", []):
                    # Log dos dados brutos da mensagem para investigação
                    logger.info(f"🔍 INVESTIGAÇÃO - Mensagem {msg_data.get('id', 'unknown')[:20]}...")
                    logger.info(f"🔍 from={msg_data.get('from')}")
                    logger.info(f"🔍 sender={msg_data.get('sender')}")
                    logger.info(f"🔍 isDraft={msg_data.get('isDraft')}")

                    # Temporariamente usar versão simplificada que funciona
                    message = self._parse_message_simple(msg_data)
                    messages.append(message)

                logger.info(f"Carregadas {len(messages)} mensagens da pasta {folder_id}")
                return messages

        except Exception as e:
            logger.error(f"Erro ao obter mensagens: {e}")
            raise

    async def send_email(self, email_request: SendEmailRequest) -> str:
        """
        Envia um email.

        Args:
            email_request: Dados do email

        Returns:
            ID da mensagem enviada
        """
        try:
            # Construir destinatários
            to_recipients = [
                {"emailAddress": {"address": email}}
                for email in email_request.to
            ]
            cc_recipients = [
                {"emailAddress": {"address": email}}
                for email in email_request.cc
            ]
            bcc_recipients = [
                {"emailAddress": {"address": email}}
                for email in email_request.bcc
            ]

            # Construir corpo da mensagem
            message_data = {
                "message": {
                    "subject": email_request.subject,
                    "body": {
                        "contentType": email_request.body_type,
                        "content": email_request.body
                    },
                    "toRecipients": to_recipients,
                    "ccRecipients": cc_recipients,
                    "bccRecipients": bcc_recipients,
                    "importance": email_request.importance
                }
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/me/sendMail",
                    headers=self._get_headers(),
                    json=message_data
                )
                response.raise_for_status()

                logger.info(f"Email enviado com sucesso para {email_request.to}")
                return "sent"  # Graph API não retorna ID para emails enviados

        except Exception as e:
            logger.error(f"Erro ao enviar email: {e}")
            raise

    async def get_deleted_items_folder_id(self) -> Optional[str]:
        """
        Obtém o ID da pasta de itens excluídos.

        Returns:
            ID da pasta de itens excluídos ou None se não encontrada
        """
        try:
            folders = await self.get_folders()

            # Nomes possíveis para pasta de itens excluídos
            deleted_names = ["deleted items", "itens excluídos", "lixeira", "deletedItems"]

            for folder in folders:
                if folder.display_name.lower() in [name.lower() for name in deleted_names]:
                    logger.info(f"Pasta de itens excluídos encontrada: {folder.display_name} (ID: {folder.id})")
                    return folder.id

            logger.warning("Pasta de itens excluídos não encontrada")
            return None

        except Exception as e:
            logger.error(f"Erro ao obter pasta de itens excluídos: {e}")
            return None

    async def delete_message(self, message_id: str) -> bool:
        """
        Move uma mensagem para a pasta de itens excluídos (exclusão suave).
        Se não conseguir mover, faz exclusão permanente.

        Args:
            message_id: ID da mensagem a ser excluída

        Returns:
            True se a exclusão foi bem-sucedida
        """
        try:
            # Primeiro, tentar mover para pasta de itens excluídos
            deleted_folder_id = await self.get_deleted_items_folder_id()

            if deleted_folder_id:
                # Mover para pasta de itens excluídos (exclusão suave)
                success = await self.move_message(message_id, deleted_folder_id)
                if success:
                    logger.info(f"Mensagem {message_id} movida para itens excluídos")
                    return True
                else:
                    logger.warning(f"Falha ao mover mensagem {message_id} para itens excluídos, tentando exclusão permanente")
            else:
                logger.warning("Pasta de itens excluídos não encontrada, fazendo exclusão permanente")

            # Se não conseguiu mover, fazer exclusão permanente
            url = f"{self.base_url}/me/messages/{message_id}"

            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    url,
                    headers=self._get_headers()
                )
                response.raise_for_status()

                logger.info(f"Mensagem {message_id} excluída permanentemente")
                return True

        except Exception as e:
            logger.error(f"Erro ao excluir mensagem {message_id}: {e}")
            raise

    async def move_message(self, message_id: str, destination_folder_id: str) -> bool:
        """
        Move uma mensagem para outra pasta.

        Args:
            message_id: ID da mensagem a ser movida
            destination_folder_id: ID da pasta de destino

        Returns:
            True se a movimentação foi bem-sucedida
        """
        try:
            url = f"{self.base_url}/me/messages/{message_id}/move"
            payload = {"destinationId": destination_folder_id}

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    headers=self._get_headers(),
                    json=payload
                )
                response.raise_for_status()

                logger.info(f"Mensagem {message_id} movida para pasta {destination_folder_id}")
                return True

        except Exception as e:
            logger.error(f"Erro ao mover mensagem {message_id}: {e}")
            raise

    async def mark_as_read(self, message_id: str, is_read: bool = True) -> bool:
        """
        Marca uma mensagem como lida ou não lida.

        Args:
            message_id: ID da mensagem
            is_read: True para marcar como lida, False para não lida

        Returns:
            True se a operação foi bem-sucedida
        """
        try:
            url = f"{self.base_url}/me/messages/{message_id}"
            payload = {"isRead": is_read}

            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    url,
                    headers=self._get_headers(),
                    json=payload
                )
                response.raise_for_status()

                status = "lida" if is_read else "não lida"
                logger.info(f"Mensagem {message_id} marcada como {status}")
                return True

        except Exception as e:
            logger.error(f"Erro ao marcar mensagem {message_id}: {e}")
            raise

    def _parse_message_simple(self, msg_data: Dict[str, Any]) -> EmailMessage:
        """
        Versão simplificada do parser de mensagens que funciona.
        """
        logger.info(f"🚀 PARSE_SIMPLE - Iniciando parse da mensagem {msg_data.get('id', 'unknown')[:20]}...")

        # Parsear remetente - versão simplificada
        from_recipient = None
        if msg_data.get("from"):
            from_data = msg_data["from"]["emailAddress"]
            logger.info(f"🔧 PARSE - Criando from_recipient com: name={from_data.get('name')}, address={from_data['address']}")
            from_recipient = EmailRecipient(
                email_address=EmailAddress(
                    name=from_data.get("name"),
                    address=from_data["address"]
                )
            )
            logger.info(f"🔧 PARSE - from_recipient criado: {from_recipient}")
        elif msg_data.get("sender"):
            sender_data = msg_data["sender"]["emailAddress"]
            logger.info(f"🔧 PARSE - Criando from_recipient com sender: name={sender_data.get('name')}, address={sender_data['address']}")
            from_recipient = EmailRecipient(
                email_address=EmailAddress(
                    name=sender_data.get("name"),
                    address=sender_data["address"]
                )
            )
            logger.info(f"🔧 PARSE - from_recipient criado com sender: {from_recipient}")
        else:
            logger.warning(f"🔧 PARSE - Nenhum remetente encontrado para mensagem {msg_data.get('id')}")

        # Parsear destinatários
        to_recipients = []
        if msg_data.get("toRecipients"):
            for recipient in msg_data["toRecipients"]:
                to_recipients.append(EmailRecipient(
                    email_address=EmailAddress(
                        name=recipient["emailAddress"].get("name"),
                        address=recipient["emailAddress"]["address"]
                    )
                ))

        # Parsear CC
        cc_recipients = []
        if msg_data.get("ccRecipients"):
            for recipient in msg_data["ccRecipients"]:
                cc_recipients.append(EmailRecipient(
                    email_address=EmailAddress(
                        name=recipient["emailAddress"].get("name"),
                        address=recipient["emailAddress"]["address"]
                    )
                ))

        # Parsear BCC
        bcc_recipients = []
        if msg_data.get("bccRecipients"):
            for recipient in msg_data["bccRecipients"]:
                bcc_recipients.append(EmailRecipient(
                    email_address=EmailAddress(
                        name=recipient["emailAddress"].get("name"),
                        address=recipient["emailAddress"]["address"]
                    )
                ))

        logger.info(f"🔧 PRE-CREATE - from_recipient antes da criação: {from_recipient}")
        logger.info(f"🔧 PRE-CREATE - type(from_recipient): {type(from_recipient)}")

        try:
            email_message = EmailMessage(
                id=msg_data["id"],
                subject=msg_data.get("subject", ""),
                body_content=msg_data.get("body", {}).get("content", ""),
                body_content_type=msg_data.get("body", {}).get("contentType", "html"),
                from_=from_recipient,
                to_recipients=to_recipients,
                cc_recipients=cc_recipients,
                bcc_recipients=bcc_recipients,
                importance=msg_data.get("importance", "normal"),
                is_read=msg_data.get("isRead", False),
                received_date_time=msg_data.get("receivedDateTime"),
                sent_date_time=msg_data.get("sentDateTime"),
                conversation_id=msg_data.get("conversationId"),
                internet_message_id=msg_data.get("internetMessageId"),
                web_link=msg_data.get("webLink")
            )
            logger.info(f"🔧 CREATE-SUCCESS - EmailMessage criado com sucesso")
        except Exception as e:
            logger.error(f"🔧 CREATE-ERROR - Erro ao criar EmailMessage: {e}")
            logger.error(f"🔧 CREATE-ERROR - Tipo do erro: {type(e)}")
            raise

        # Correção: Definir o campo from_ diretamente se estiver None
        if email_message.from_ is None and from_recipient is not None:
            logger.info(f"🔧 FIX - Corrigindo from_ que estava None")
            email_message.from_ = from_recipient

        # Debug: Verificar se o objeto foi criado corretamente
        logger.info(f"🔧 FINAL - EmailMessage criado: from_={email_message.from_}")
        logger.info(f"🔧 FINAL - Serialização: {email_message.dict(by_alias=True).get('from')}")

        return email_message

    def _parse_message(self, msg_data: Dict[str, Any], folder_id: str = "inbox") -> EmailMessage:
        """
        Converte dados da API em modelo EmailMessage.

        Args:
            msg_data: Dados da mensagem da API
            folder_id: ID da pasta para determinar tipo de email

        Returns:
            Objeto EmailMessage
        """
        # Parsear remetente - lógica melhorada para diferentes tipos de email
        from_recipient = None

        # Verificar se é um email enviado (draft ou na pasta de enviados)
        is_sent_email = msg_data.get("isDraft", False)

        if msg_data.get("from"):
            # Email recebido normal
            from_data = msg_data["from"]["emailAddress"]
            from_recipient = EmailRecipient(
                email_address=EmailAddress(
                    name=from_data.get("name"),
                    address=from_data["address"]
                )
            )
            logger.debug(f"Remetente encontrado no campo 'from': {from_data}")
        elif msg_data.get("sender"):
            # Fallback para campo 'sender'
            sender_data = msg_data["sender"]["emailAddress"]
            from_recipient = EmailRecipient(
                email_address=EmailAddress(
                    name=sender_data.get("name"),
                    address=sender_data["address"]
                )
            )
            logger.debug(f"Usando campo 'sender' como fallback para remetente: {sender_data}")
        elif is_sent_email:
            # Para emails enviados, usar o usuário atual como remetente
            # Isso pode ser melhorado obtendo dados do perfil do usuário
            from_recipient = EmailRecipient(
                email_address=EmailAddress(
                    name="Você",
                    address="me"  # Placeholder - pode ser melhorado
                )
            )
            logger.debug(f"Email enviado detectado, usando 'Você' como remetente")
        else:
            logger.warning(f"Mensagem {msg_data.get('id')} sem informações de remetente (from={msg_data.get('from')}, sender={msg_data.get('sender')}, isDraft={msg_data.get('isDraft')})")

        # Parsear destinatários
        to_recipients = []
        for recipient in msg_data.get("toRecipients", []):
            email_data = recipient["emailAddress"]
            to_recipients.append(EmailRecipient(
                email_address=EmailAddress(
                    name=email_data.get("name"),
                    address=email_data["address"]
                )
            ))

        # Parsear datas
        received_date = None
        if msg_data.get("receivedDateTime"):
            received_date = datetime.fromisoformat(
                msg_data["receivedDateTime"].replace("Z", "+00:00")
            )

        sent_date = None
        if msg_data.get("sentDateTime"):
            sent_date = datetime.fromisoformat(
                msg_data["sentDateTime"].replace("Z", "+00:00")
            )

        return EmailMessage(
            id=msg_data["id"],
            subject=msg_data.get("subject", ""),
            body_content=msg_data.get("body", {}).get("content", ""),
            body_content_type=msg_data.get("body", {}).get("contentType", "HTML"),
            from_=from_recipient,
            to_recipients=to_recipients,
            importance=msg_data.get("importance", "normal"),
            is_read=msg_data.get("isRead", False),
            received_date_time=received_date,
            sent_date_time=sent_date,
            conversation_id=msg_data.get("conversationId"),
            internet_message_id=msg_data.get("internetMessageId"),
            web_link=msg_data.get("webLink")
        )

    async def search_messages(self, search_request: EmailSearchRequest) -> EmailSearchResponse:
        """
        Busca mensagens com filtros.

        Args:
            search_request: Parâmetros de busca

        Returns:
            Resultado da busca
        """
        try:
            url = f"{self.base_url}/me/messages"
            params = {
                "$top": search_request.limit,
                "$skip": search_request.offset
            }

            # Construir filtros
            filters = []

            if search_request.query:
                filters.append(f"contains(subject,'{search_request.query}') or contains(body/content,'{search_request.query}')")

            if search_request.from_date:
                date_str = search_request.from_date.isoformat()
                filters.append(f"receivedDateTime ge {date_str}")

            if search_request.to_date:
                date_str = search_request.to_date.isoformat()
                filters.append(f"receivedDateTime le {date_str}")

            if search_request.is_read is not None:
                filters.append(f"isRead eq {str(search_request.is_read).lower()}")

            if search_request.has_attachments is not None:
                filters.append(f"hasAttachments eq {str(search_request.has_attachments).lower()}")

            if search_request.importance:
                filters.append(f"importance eq '{search_request.importance}'")

            if filters:
                params["$filter"] = " and ".join(filters)

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url,
                    headers=self._get_headers(),
                    params=params
                )
                response.raise_for_status()

                data = response.json()
                messages = []

                for msg_data in data.get("value", []):
                    message = self._parse_message(msg_data, "search")
                    messages.append(message)

                return EmailSearchResponse(
                    messages=messages,
                    total_count=len(messages),  # Graph API não retorna contagem total
                    has_more=len(messages) == search_request.limit,
                    next_link=data.get("@odata.nextLink")
                )

        except Exception as e:
            logger.error(f"Erro ao buscar mensagens: {e}")
            raise
