"""
Serviço mock para simular dados do WhatsApp
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import random
from pydantic import BaseModel
from enum import Enum

class MessageType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    DOCUMENT = "document"
    AUDIO = "audio"

class MessageStatus(str, Enum):
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"

class ChatStatus(str, Enum):
    ACTIVE = "active"
    WAITING = "waiting"
    CLOSED = "closed"

class WhatsAppMessage(BaseModel):
    id: str
    chat_id: str
    contact_name: str
    contact_number: str
    message: str
    timestamp: datetime
    is_from_contact: bool
    message_type: MessageType = MessageType.TEXT
    status: MessageStatus = MessageStatus.DELIVERED

class WhatsAppChat(BaseModel):
    id: str
    contact_name: str
    contact_number: str
    last_message: str
    last_message_time: datetime
    unread_count: int = 0
    status: ChatStatus = ChatStatus.ACTIVE
    messages: List[WhatsAppMessage] = []

class WhatsAppMockService:
    """Serviço mock para simular dados do WhatsApp"""
    
    def __init__(self):
        self.mock_chats = self._generate_mock_chats()
        self.mock_messages = self._generate_mock_messages()
    
    def _generate_mock_chats(self) -> List[Dict[str, Any]]:
        """Gera chats mock para demonstração"""
        chats = [
            {
                "id": "chat_1",
                "contact_name": "Ana Oliveira",
                "contact_number": "+5511999888777",
                "last_message": "Obrigada pelo atendimento!",
                "last_message_time": datetime.now() - timedelta(minutes=5),
                "unread_count": 0,
                "status": ChatStatus.ACTIVE
            },
            {
                "id": "chat_2",
                "contact_name": "Carlos Mendes",
                "contact_number": "+5511888777666",
                "last_message": "Quando vocês abrem?",
                "last_message_time": datetime.now() - timedelta(minutes=2),
                "unread_count": 2,
                "status": ChatStatus.WAITING
            },
            {
                "id": "chat_3",
                "contact_name": "Fernanda Lima",
                "contact_number": "+5511777666555",
                "last_message": "Preciso cancelar meu pedido",
                "last_message_time": datetime.now() - timedelta(hours=1),
                "unread_count": 1,
                "status": ChatStatus.WAITING
            },
            {
                "id": "chat_4",
                "contact_name": "Roberto Silva",
                "contact_number": "+5511666555444",
                "last_message": "Perfeito, muito obrigado!",
                "last_message_time": datetime.now() - timedelta(hours=2),
                "unread_count": 0,
                "status": ChatStatus.CLOSED
            }
        ]
        return chats
    
    def _generate_mock_messages(self) -> Dict[str, List[Dict[str, Any]]]:
        """Gera mensagens mock para cada chat"""
        messages = {
            "chat_1": [
                {
                    "id": "msg_1_1",
                    "chat_id": "chat_1",
                    "contact_name": "Ana Oliveira",
                    "contact_number": "+5511999888777",
                    "message": "Olá, preciso de ajuda com meu pedido",
                    "timestamp": datetime.now() - timedelta(minutes=15),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                },
                {
                    "id": "msg_1_2",
                    "chat_id": "chat_1",
                    "contact_name": "Agente",
                    "contact_number": "",
                    "message": "Olá Ana! Claro, posso te ajudar. Qual o número do seu pedido?",
                    "timestamp": datetime.now() - timedelta(minutes=14),
                    "is_from_contact": False,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                },
                {
                    "id": "msg_1_3",
                    "chat_id": "chat_1",
                    "contact_name": "Ana Oliveira",
                    "contact_number": "+5511999888777",
                    "message": "É o pedido #12345",
                    "timestamp": datetime.now() - timedelta(minutes=13),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                },
                {
                    "id": "msg_1_4",
                    "chat_id": "chat_1",
                    "contact_name": "Agente",
                    "contact_number": "",
                    "message": "Encontrei seu pedido! Ele está sendo preparado para envio. Deve sair hoje.",
                    "timestamp": datetime.now() - timedelta(minutes=10),
                    "is_from_contact": False,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                },
                {
                    "id": "msg_1_5",
                    "chat_id": "chat_1",
                    "contact_name": "Ana Oliveira",
                    "contact_number": "+5511999888777",
                    "message": "Obrigada pelo atendimento!",
                    "timestamp": datetime.now() - timedelta(minutes=5),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                }
            ],
            "chat_2": [
                {
                    "id": "msg_2_1",
                    "chat_id": "chat_2",
                    "contact_name": "Carlos Mendes",
                    "contact_number": "+5511888777666",
                    "message": "Oi, vocês fazem entrega?",
                    "timestamp": datetime.now() - timedelta(minutes=10),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.DELIVERED
                },
                {
                    "id": "msg_2_2",
                    "chat_id": "chat_2",
                    "contact_name": "Carlos Mendes",
                    "contact_number": "+5511888777666",
                    "message": "Quando vocês abrem?",
                    "timestamp": datetime.now() - timedelta(minutes=2),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.DELIVERED
                }
            ],
            "chat_3": [
                {
                    "id": "msg_3_1",
                    "chat_id": "chat_3",
                    "contact_name": "Fernanda Lima",
                    "contact_number": "+5511777666555",
                    "message": "Preciso cancelar meu pedido",
                    "timestamp": datetime.now() - timedelta(hours=1),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.DELIVERED
                }
            ],
            "chat_4": [
                {
                    "id": "msg_4_1",
                    "chat_id": "chat_4",
                    "contact_name": "Roberto Silva",
                    "contact_number": "+5511666555444",
                    "message": "Gostaria de saber sobre garantia",
                    "timestamp": datetime.now() - timedelta(hours=3),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                },
                {
                    "id": "msg_4_2",
                    "chat_id": "chat_4",
                    "contact_name": "Agente",
                    "contact_number": "",
                    "message": "Claro! Nossos produtos têm garantia de 12 meses. Qual produto você gostaria de saber?",
                    "timestamp": datetime.now() - timedelta(hours=2, minutes=30),
                    "is_from_contact": False,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                },
                {
                    "id": "msg_4_3",
                    "chat_id": "chat_4",
                    "contact_name": "Roberto Silva",
                    "contact_number": "+5511666555444",
                    "message": "Perfeito, muito obrigado!",
                    "timestamp": datetime.now() - timedelta(hours=2),
                    "is_from_contact": True,
                    "message_type": MessageType.TEXT,
                    "status": MessageStatus.READ
                }
            ]
        }
        return messages
    
    async def get_chats(self, status: Optional[ChatStatus] = None) -> List[WhatsAppChat]:
        """Retorna lista de chats"""
        chats = []
        
        for chat_data in self.mock_chats:
            if status and chat_data["status"] != status:
                continue
            
            # Adicionar mensagens ao chat
            chat_messages = []
            if chat_data["id"] in self.mock_messages:
                for msg_data in self.mock_messages[chat_data["id"]]:
                    chat_messages.append(WhatsAppMessage(**msg_data))
            
            chat = WhatsAppChat(**chat_data, messages=chat_messages)
            chats.append(chat)
        
        return chats
    
    async def get_chat(self, chat_id: str) -> Optional[WhatsAppChat]:
        """Retorna um chat específico"""
        for chat_data in self.mock_chats:
            if chat_data["id"] == chat_id:
                # Adicionar mensagens ao chat
                chat_messages = []
                if chat_id in self.mock_messages:
                    for msg_data in self.mock_messages[chat_id]:
                        chat_messages.append(WhatsAppMessage(**msg_data))
                
                return WhatsAppChat(**chat_data, messages=chat_messages)
        return None
    
    async def send_message(self, chat_id: str, message: str) -> Optional[WhatsAppMessage]:
        """Simula envio de mensagem"""
        chat = await self.get_chat(chat_id)
        if not chat:
            return None
        
        new_message = {
            "id": f"msg_{chat_id}_{datetime.now().timestamp()}",
            "chat_id": chat_id,
            "contact_name": "Agente",
            "contact_number": "",
            "message": message,
            "timestamp": datetime.now(),
            "is_from_contact": False,
            "message_type": MessageType.TEXT,
            "status": MessageStatus.SENT
        }
        
        # Adicionar mensagem ao chat
        if chat_id not in self.mock_messages:
            self.mock_messages[chat_id] = []
        self.mock_messages[chat_id].append(new_message)
        
        # Atualizar última mensagem do chat
        for chat_data in self.mock_chats:
            if chat_data["id"] == chat_id:
                chat_data["last_message"] = message
                chat_data["last_message_time"] = datetime.now()
                break
        
        return WhatsAppMessage(**new_message)
    
    async def mark_as_read(self, chat_id: str) -> bool:
        """Marca mensagens como lidas"""
        for chat_data in self.mock_chats:
            if chat_data["id"] == chat_id:
                chat_data["unread_count"] = 0
                return True
        return False
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Retorna estatísticas do WhatsApp"""
        total_chats = len(self.mock_chats)
        active_chats = len([chat for chat in self.mock_chats if chat["status"] == ChatStatus.ACTIVE])
        waiting_chats = len([chat for chat in self.mock_chats if chat["status"] == ChatStatus.WAITING])
        total_unread = sum([chat["unread_count"] for chat in self.mock_chats])
        
        return {
            "total_chats": total_chats,
            "active_chats": active_chats,
            "waiting_chats": waiting_chats,
            "total_unread_messages": total_unread
        }

# Instância global do serviço mock
whatsapp_mock_service = WhatsAppMockService()
