"""
Serviço mock para gerar dados realistas de métricas avançadas de call center.
"""

import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class AdvancedMetricsMockService:
    """
    Serviço para gerar dados mock realistas para métricas avançadas.
    """
    
    def __init__(self):
        self.logger = logger
        
        # Configurações realistas
        self.agents = [
            {"id": "agent_001", "name": "<PERSON>", "skill_level": 0.9},
            {"id": "agent_002", "name": "<PERSON>", "skill_level": 0.8},
            {"id": "agent_003", "name": "<PERSON>", "skill_level": 0.95},
            {"id": "agent_004", "name": "<PERSON>", "skill_level": 0.7},
            {"id": "agent_005", "name": "<PERSON>", "skill_level": 0.85},
        ]
        
        self.queues = [
            {"id": 801, "name": "Suporte Técnico", "complexity": 0.8},
            {"id": 802, "name": "Vendas", "complexity": 0.6},
            {"id": 803, "name": "Atendimento Geral", "complexity": 0.5},
            {"id": 804, "name": "Cobrança", "complexity": 0.7},
        ]
    
    def generate_realistic_calls_data(
        self,
        start_date: datetime,
        end_date: datetime,
        queue_ids: Optional[List[int]] = None
    ) -> List[Dict[str, Any]]:
        """
        Gera dados realistas de chamadas para o período especificado.
        """
        calls = []
        current_date = start_date
        
        # Filtrar filas se especificado
        target_queues = self.queues
        if queue_ids:
            target_queues = [q for q in self.queues if q["id"] in queue_ids]
        
        while current_date <= end_date:
            # Gerar chamadas para cada dia
            daily_calls = self._generate_daily_calls(current_date, target_queues)
            calls.extend(daily_calls)
            current_date += timedelta(days=1)
        
        return calls
    
    def _generate_daily_calls(
        self,
        date: datetime,
        queues: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Gera chamadas para um dia específico com padrões realistas.
        """
        calls = []
        
        # Padrão de volume por hora (8h às 18h)
        hourly_volume_pattern = {
            8: 0.3, 9: 0.7, 10: 1.0, 11: 0.9, 12: 0.6,  # Manhã
            13: 0.4, 14: 0.8, 15: 1.0, 16: 0.9, 17: 0.7, 18: 0.3  # Tarde
        }
        
        for hour, volume_factor in hourly_volume_pattern.items():
            # Base de chamadas por hora
            base_calls_per_hour = 15
            calls_this_hour = int(base_calls_per_hour * volume_factor * random.uniform(0.8, 1.2))
            
            for _ in range(calls_this_hour):
                call = self._generate_single_call(date, hour, queues)
                calls.append(call)
        
        return calls
    
    def _generate_single_call(
        self,
        date: datetime,
        hour: int,
        queues: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Gera uma única chamada com dados realistas.
        """
        # Selecionar fila aleatória
        queue = random.choice(queues)
        
        # Selecionar agente aleatório
        agent = random.choice(self.agents)
        
        # Horário da chamada
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        start_time = date.replace(hour=hour, minute=minute, second=second)
        
        # Calcular wait time baseado na complexidade da fila e hora do dia
        base_wait = queue["complexity"] * 30  # 0-24 segundos base
        hour_factor = 1.5 if hour in [10, 11, 15, 16] else 1.0  # Picos
        wait_time = max(0, int(base_wait * hour_factor * random.uniform(0.5, 2.0)))
        
        # Determinar se a chamada foi atendida ou abandonada
        # Chamadas com wait time > 60s têm maior chance de abandono
        abandonment_probability = min(0.4, wait_time / 150)
        is_answered = random.random() > abandonment_probability
        
        if is_answered:
            # Duração da chamada baseada na complexidade e skill do agente
            base_duration = queue["complexity"] * 300  # 0-240 segundos base
            skill_factor = 2.0 - agent["skill_level"]  # Agentes menos hábeis demoram mais
            duration = int(base_duration * skill_factor * random.uniform(0.7, 1.5))
            
            # Talk time é 80-95% da duração total
            talk_time = int(duration * random.uniform(0.8, 0.95))
            
            # Determinar se houve transferência (baseado na complexidade)
            transferred = random.random() < (queue["complexity"] * 0.2)
            
            status = "answered"
            end_time = start_time + timedelta(seconds=wait_time + duration)
        else:
            duration = 0
            talk_time = 0
            transferred = False
            status = "abandoned"
            end_time = start_time + timedelta(seconds=wait_time)
        
        return {
            "id": f"call_{start_time.strftime('%Y%m%d_%H%M%S')}_{random.randint(1000, 9999)}",
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "queue_id": queue["id"],
            "queue_name": queue["name"],
            "agent_id": agent["id"] if is_answered else None,
            "agent_name": agent["name"] if is_answered else None,
            "caller_number": f"+5511{random.randint(90000000, 99999999)}",
            "wait_time": wait_time,
            "duration": duration,
            "talk_time": talk_time,
            "status": status,
            "transferred": transferred,
            "direction": "inbound"
        }
    
    def generate_agent_activity_data(
        self,
        start_date: datetime,
        end_date: datetime,
        agent_ids: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Gera dados de atividade dos agentes.
        """
        target_agents = self.agents
        if agent_ids:
            target_agents = [a for a in self.agents if a["id"] in agent_ids]
        
        activity_data = {}
        
        for agent in target_agents:
            # Calcular dias de trabalho
            current_date = start_date
            total_login_time = 0
            total_break_time = 0
            
            while current_date <= end_date:
                # Apenas dias úteis
                if current_date.weekday() < 5:  # Segunda a sexta
                    # Jornada de trabalho: 8h com variação
                    daily_login = 8 * 3600 * random.uniform(0.9, 1.1)  # 7.2 a 8.8 horas
                    
                    # Pausas: 1-2 horas por dia
                    daily_breaks = random.uniform(1, 2) * 3600
                    
                    total_login_time += daily_login
                    total_break_time += daily_breaks
                
                current_date += timedelta(days=1)
            
            activity_data[agent["id"]] = {
                "agent_id": agent["id"],
                "agent_name": agent["name"],
                "login_time": total_login_time,
                "break_time": total_break_time,
                "available_time": total_login_time - total_break_time,
                "skill_level": agent["skill_level"]
            }
        
        return activity_data
    
    def generate_queue_statistics(
        self,
        start_date: datetime,
        end_date: datetime,
        queue_ids: Optional[List[int]] = None
    ) -> Dict[int, Dict[str, Any]]:
        """
        Gera estatísticas por fila.
        """
        target_queues = self.queues
        if queue_ids:
            target_queues = [q for q in self.queues if q["id"] in queue_ids]
        
        queue_stats = {}
        
        for queue in target_queues:
            # Gerar dados baseados na complexidade da fila
            complexity = queue["complexity"]
            
            # Volume base por dia
            daily_volume = int(50 * (1 + complexity) * random.uniform(0.8, 1.2))
            
            # Calcular total de dias
            days = (end_date - start_date).days + 1
            total_calls = daily_volume * days
            
            # Distribuição de status baseada na complexidade
            answered_rate = 0.95 - (complexity * 0.15)  # Filas complexas têm mais abandono
            answered_calls = int(total_calls * answered_rate)
            abandoned_calls = total_calls - answered_calls
            
            # Métricas de tempo
            avg_wait_time = complexity * 25 * random.uniform(0.8, 1.2)
            service_level = (1 - complexity * 0.3) * 100 * random.uniform(0.9, 1.1)
            
            queue_stats[queue["id"]] = {
                "queue_id": queue["id"],
                "queue_name": queue["name"],
                "total_calls": total_calls,
                "answered_calls": answered_calls,
                "abandoned_calls": abandoned_calls,
                "average_wait_time": round(avg_wait_time, 2),
                "service_level": round(min(100, service_level), 2),
                "complexity": complexity
            }
        
        return queue_stats
    
    def get_realistic_timing_data(
        self,
        calls_data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Processa dados de chamadas para extrair métricas de timing realistas.
        """
        if not calls_data:
            return {
                "total_calls": 0,
                "answered_calls": 0,
                "abandoned_calls": 0,
                "avg_wait_time": 0,
                "avg_handle_time": 0,
                "service_level_20s": 0,
                "abandonment_rate": 0
            }
        
        answered_calls = [call for call in calls_data if call["status"] == "answered"]
        abandoned_calls = [call for call in calls_data if call["status"] == "abandoned"]
        
        # Calcular métricas
        total_calls = len(calls_data)
        avg_wait_time = sum(call["wait_time"] for call in calls_data) / total_calls
        
        if answered_calls:
            avg_handle_time = sum(call["duration"] for call in answered_calls) / len(answered_calls)
        else:
            avg_handle_time = 0
        
        # Service level (20 segundos)
        calls_within_20s = len([call for call in calls_data if call["wait_time"] <= 20])
        service_level_20s = (calls_within_20s / total_calls) * 100
        
        abandonment_rate = (len(abandoned_calls) / total_calls) * 100
        
        return {
            "total_calls": total_calls,
            "answered_calls": len(answered_calls),
            "abandoned_calls": len(abandoned_calls),
            "avg_wait_time": round(avg_wait_time, 2),
            "avg_handle_time": round(avg_handle_time, 2),
            "service_level_20s": round(service_level_20s, 2),
            "abandonment_rate": round(abandonment_rate, 2),
            "calls_data": calls_data
        }

# Instância global do serviço
advanced_metrics_mock = AdvancedMetricsMockService()
