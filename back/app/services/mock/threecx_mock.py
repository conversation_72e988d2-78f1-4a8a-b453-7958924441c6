"""
Serviço mock para simular dados do 3CX
"""

from datetime import datetime, timedelta
from typing import List, Optional
import random
from app.schemas.threecx import (
    CallRecord,
    CallRecordResponse,
    AgentStatus,
    AgentStatusEnum,
    CallDirection,
    CallStatus
)

class ThreeCXMockService:
    """Serviço mock para simular dados do 3CX"""
    
    def __init__(self):
        self.mock_agents = [
            {
                "id": "agent1",
                "name": "<PERSON>",
                "extension": "1001",
                "status": AgentStatusEnum.AVAILABLE,
                "current_call_id": None,
                "last_activity": datetime.now()
            },
            {
                "id": "agent2", 
                "name": "<PERSON>",
                "extension": "1002",
                "status": AgentStatusEnum.ON_CALL,
                "current_call_id": "call_123",
                "last_activity": datetime.now() - timedelta(minutes=5)
            },
            {
                "id": "agent3",
                "name": "<PERSON>", 
                "extension": "1003",
                "status": AgentStatusEnum.AWAY,
                "current_call_id": None,
                "last_activity": datetime.now() - timedelta(minutes=30)
            }
        ]
        
        self.mock_calls = self._generate_mock_calls()
    
    def _generate_mock_calls(self) -> List[dict]:
        """Gera chamadas mock para demonstração"""
        calls = []
        
        # Chamadas ativas
        calls.append({
            "id": "call_active_1",
            "caller_number": "+5511999887766",
            "caller_name": "Cliente Importante",
            "called_number": "1001",
            "called_name": "João Silva",
            "direction": CallDirection.INBOUND,
            "duration": 180,
            "status": CallStatus.ANSWERED,
            "agent_id": "agent1",
            "agent_name": "João Silva",
            "start_time": datetime.now() - timedelta(minutes=3),
            "end_time": None,
            "created_at": datetime.now() - timedelta(minutes=3),
            "updated_at": datetime.now()
        })
        
        calls.append({
            "id": "call_ringing_1",
            "caller_number": "+5511888776655",
            "caller_name": "Novo Cliente",
            "called_number": "1002",
            "called_name": "Maria Santos",
            "direction": CallDirection.INBOUND,
            "duration": 0,
            "status": CallStatus.ANSWERED,
            "agent_id": "agent2",
            "agent_name": "Maria Santos",
            "start_time": datetime.now(),
            "end_time": None,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        })
        
        # Chamadas recentes (finalizadas)
        for i in range(10):
            call_time = datetime.now() - timedelta(hours=random.randint(1, 24))
            duration = random.randint(30, 600)
            
            calls.append({
                "id": f"call_history_{i}",
                "caller_number": f"+5511{random.randint(900000000, 999999999)}",
                "caller_name": f"Cliente {i+1}",
                "called_number": random.choice(["1001", "1002", "1003"]),
                "called_name": random.choice(["João Silva", "Maria Santos", "Pedro Costa"]),
                "direction": random.choice([CallDirection.INBOUND, CallDirection.OUTBOUND]),
                "duration": duration,
                "status": random.choice([CallStatus.ANSWERED, CallStatus.MISSED, CallStatus.VOICEMAIL]),
                "agent_id": random.choice(["agent1", "agent2", "agent3"]),
                "agent_name": random.choice(["João Silva", "Maria Santos", "Pedro Costa"]),
                "start_time": call_time,
                "end_time": call_time + timedelta(seconds=duration),
                "created_at": call_time,
                "updated_at": call_time + timedelta(seconds=duration)
            })
        
        return calls
    
    async def get_calls(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        agent_id: Optional[str] = None,
        client_number: Optional[str] = None,
        limit: int = 50
    ) -> List[CallRecordResponse]:
        """Retorna lista de chamadas mock"""
        
        filtered_calls = self.mock_calls.copy()
        
        # Filtrar por agente
        if agent_id:
            filtered_calls = [call for call in filtered_calls if call["agent_id"] == agent_id]
        
        # Filtrar por número do cliente
        if client_number:
            filtered_calls = [call for call in filtered_calls if client_number in call["caller_number"]]
        
        # Filtrar por data
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            filtered_calls = [call for call in filtered_calls if call["start_time"] >= start_dt]
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            filtered_calls = [call for call in filtered_calls if call["start_time"] <= end_dt]
        
        # Limitar resultados
        filtered_calls = filtered_calls[:limit]
        
        # Converter para CallRecordResponse
        result = []
        for call in filtered_calls:
            result.append(CallRecordResponse(**call))
        
        return result
    
    async def get_call(self, call_id: str) -> Optional[CallRecordResponse]:
        """Retorna uma chamada específica"""
        for call in self.mock_calls:
            if call["id"] == call_id:
                return CallRecordResponse(**call)
        return None
    
    async def get_agents(self) -> List[AgentStatus]:
        """Retorna lista de agentes mock"""
        result = []
        for agent in self.mock_agents:
            result.append(AgentStatus(**agent))
        return result
    
    async def get_agent(self, agent_id: str) -> Optional[AgentStatus]:
        """Retorna um agente específico"""
        for agent in self.mock_agents:
            if agent["id"] == agent_id:
                return AgentStatus(**agent)
        return None
    
    async def update_agent_status(self, agent_id: str, status: AgentStatusEnum) -> Optional[AgentStatus]:
        """Atualiza o status de um agente"""
        for agent in self.mock_agents:
            if agent["id"] == agent_id:
                agent["status"] = status
                agent["last_activity"] = datetime.now()
                if status != AgentStatusEnum.ON_CALL:
                    agent["current_call_id"] = None
                return AgentStatus(**agent)
        return None
    
    async def make_call(self, agent_id: str, number: str) -> Optional[CallRecordResponse]:
        """Simula fazer uma chamada"""
        call_id = f"call_out_{datetime.now().timestamp()}"
        
        new_call = {
            "id": call_id,
            "caller_number": "1001",  # Ramal do agente
            "caller_name": "João Silva",
            "called_number": number,
            "called_name": "Contato Desconhecido",
            "direction": CallDirection.OUTBOUND,
            "duration": 0,
            "status": CallStatus.ANSWERED,
            "agent_id": agent_id,
            "agent_name": "João Silva",
            "start_time": datetime.now(),
            "end_time": None,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        
        self.mock_calls.insert(0, new_call)
        
        # Atualizar status do agente
        await self.update_agent_status(agent_id, AgentStatusEnum.ON_CALL)
        
        return CallRecordResponse(**new_call)
    
    async def end_call(self, call_id: str) -> bool:
        """Simula encerrar uma chamada"""
        for call in self.mock_calls:
            if call["id"] == call_id:
                call["end_time"] = datetime.now()
                call["status"] = CallStatus.ANSWERED
                call["duration"] = int((call["end_time"] - call["start_time"]).total_seconds())
                call["updated_at"] = datetime.now()
                
                # Atualizar status do agente
                if call["agent_id"]:
                    await self.update_agent_status(call["agent_id"], AgentStatusEnum.AVAILABLE)
                
                return True
        return False
    
    async def get_call_statistics(self) -> dict:
        """Retorna estatísticas das chamadas"""
        total_calls = len(self.mock_calls)
        active_calls = len([call for call in self.mock_calls if call["end_time"] is None])
        answered_calls = len([call for call in self.mock_calls if call["status"] == CallStatus.ANSWERED])
        missed_calls = len([call for call in self.mock_calls if call["status"] == CallStatus.MISSED])
        
        return {
            "total_calls": total_calls,
            "active_calls": active_calls,
            "answered_calls": answered_calls,
            "missed_calls": missed_calls,
            "answer_rate": (answered_calls / total_calls * 100) if total_calls > 0 else 0
        }

# Instância global do serviço mock
threecx_mock_service = ThreeCXMockService()
