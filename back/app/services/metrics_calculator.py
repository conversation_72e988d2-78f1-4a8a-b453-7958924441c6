"""
Serviço para calcular métricas avançadas de call center.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from statistics import mean, median

from app.schemas.reports import (
    TimingMetrics,
    AgentPerformance,
    QueueMetrics,
    SLAMetrics
)

logger = logging.getLogger(__name__)

class MetricsCalculator:
    """
    Calculadora de métricas avançadas para call center.
    """
    
    def __init__(self):
        self.logger = logger
    
    def calculate_timing_metrics(
        self,
        calls_data: List[Dict[str, Any]],
        service_level_threshold: int = 20
    ) -> TimingMetrics:
        """
        Calcula métricas de tempo de atendimento.
        
        Args:
            calls_data: Lista de dados de chamadas
            service_level_threshold: Limite em segundos para service level
            
        Returns:
            TimingMetrics com todas as métricas calculadas
        """
        if not calls_data:
            return TimingMetrics(
                asa=0, aht=0, att=0, acw=0, service_level=0,
                abandonment_rate=0, total_calls=0, answered_calls=0, abandoned_calls=0
            )
        
        total_calls = len(calls_data)
        answered_calls = [call for call in calls_data if call.get('status') == 'answered']
        abandoned_calls = [call for call in calls_data if call.get('status') == 'abandoned']
        
        # ASA - Average Speed of Answer
        wait_times = [call.get('wait_time', 0) for call in answered_calls if call.get('wait_time')]
        asa = mean(wait_times) if wait_times else 0
        
        # AHT - Average Handle Time
        handle_times = [call.get('duration', 0) for call in answered_calls if call.get('duration')]
        aht = mean(handle_times) if handle_times else 0
        
        # ATT - Average Talk Time
        talk_times = [call.get('talk_time', 0) for call in answered_calls if call.get('talk_time')]
        att = mean(talk_times) if talk_times else 0
        
        # ACW - After Call Work (estimado como 10% do AHT)
        acw = aht * 0.1 if aht > 0 else 0
        
        # Service Level - % de chamadas atendidas dentro do threshold
        calls_within_threshold = [
            call for call in answered_calls 
            if call.get('wait_time', 0) <= service_level_threshold
        ]
        service_level = (len(calls_within_threshold) / total_calls * 100) if total_calls > 0 else 0
        
        # Abandonment Rate
        abandonment_rate = (len(abandoned_calls) / total_calls * 100) if total_calls > 0 else 0
        
        return TimingMetrics(
            asa=round(asa, 2),
            aht=round(aht, 2),
            att=round(att, 2),
            acw=round(acw, 2),
            service_level=round(service_level, 2),
            abandonment_rate=round(abandonment_rate, 2),
            total_calls=total_calls,
            answered_calls=len(answered_calls),
            abandoned_calls=len(abandoned_calls)
        )
    
    def calculate_agent_performance(
        self,
        agent_id: str,
        agent_name: str,
        calls_data: List[Dict[str, Any]],
        agent_activity: Dict[str, Any]
    ) -> AgentPerformance:
        """
        Calcula métricas de performance de um agente específico.
        
        Args:
            agent_id: ID do agente
            agent_name: Nome do agente
            calls_data: Chamadas do agente
            agent_activity: Dados de atividade do agente
            
        Returns:
            AgentPerformance com métricas calculadas
        """
        if not calls_data:
            return AgentPerformance(
                agent_id=agent_id,
                agent_name=agent_name,
                calls_handled=0,
                talk_time=0,
                break_time=0,
                login_time=0,
                occupancy_rate=0,
                productivity_score=0,
                calls_per_hour=0,
                average_call_duration=0,
                first_call_resolution=0
            )
        
        # Métricas básicas
        calls_handled = len(calls_data)
        talk_time = sum(call.get('duration', 0) for call in calls_data)
        
        # Dados de atividade do agente
        login_time = agent_activity.get('login_time', 8 * 3600)  # 8 horas padrão
        break_time = agent_activity.get('break_time', 0)
        
        # Cálculos avançados
        available_time = login_time - break_time
        occupancy_rate = (talk_time / available_time * 100) if available_time > 0 else 0
        
        # Calls per hour
        hours_worked = login_time / 3600 if login_time > 0 else 1
        calls_per_hour = calls_handled / hours_worked
        
        # Average call duration
        average_call_duration = talk_time / calls_handled if calls_handled > 0 else 0
        
        # First Call Resolution (estimado baseado em transferências)
        transferred_calls = len([call for call in calls_data if call.get('transferred', False)])
        fcr = ((calls_handled - transferred_calls) / calls_handled * 100) if calls_handled > 0 else 0
        
        # Productivity Score (baseado em múltiplos fatores)
        productivity_score = self._calculate_productivity_score(
            occupancy_rate, calls_per_hour, fcr, average_call_duration
        )
        
        return AgentPerformance(
            agent_id=agent_id,
            agent_name=agent_name,
            calls_handled=calls_handled,
            talk_time=round(talk_time, 2),
            break_time=round(break_time, 2),
            login_time=round(login_time, 2),
            occupancy_rate=round(occupancy_rate, 2),
            productivity_score=round(productivity_score, 2),
            calls_per_hour=round(calls_per_hour, 2),
            average_call_duration=round(average_call_duration, 2),
            first_call_resolution=round(fcr, 2)
        )
    
    def calculate_queue_metrics(
        self,
        queue_id: int,
        queue_name: str,
        calls_data: List[Dict[str, Any]],
        service_level_threshold: int = 20
    ) -> QueueMetrics:
        """
        Calcula métricas por fila.
        
        Args:
            queue_id: ID da fila
            queue_name: Nome da fila
            calls_data: Chamadas da fila
            service_level_threshold: Limite para service level
            
        Returns:
            QueueMetrics com métricas da fila
        """
        if not calls_data:
            return QueueMetrics(
                queue_id=queue_id,
                queue_name=queue_name,
                total_calls=0,
                answered_calls=0,
                abandoned_calls=0,
                average_wait_time=0,
                service_level=0,
                peak_hour="00:00",
                peak_calls=0
            )
        
        total_calls = len(calls_data)
        answered_calls = len([call for call in calls_data if call.get('status') == 'answered'])
        abandoned_calls = len([call for call in calls_data if call.get('status') == 'abandoned'])
        
        # Average wait time
        wait_times = [call.get('wait_time', 0) for call in calls_data if call.get('wait_time')]
        average_wait_time = mean(wait_times) if wait_times else 0
        
        # Service level
        calls_within_threshold = len([
            call for call in calls_data 
            if call.get('wait_time', 0) <= service_level_threshold
        ])
        service_level = (calls_within_threshold / total_calls * 100) if total_calls > 0 else 0
        
        # Peak hour analysis
        peak_hour, peak_calls = self._find_peak_hour(calls_data)
        
        return QueueMetrics(
            queue_id=queue_id,
            queue_name=queue_name,
            total_calls=total_calls,
            answered_calls=answered_calls,
            abandoned_calls=abandoned_calls,
            average_wait_time=round(average_wait_time, 2),
            service_level=round(service_level, 2),
            peak_hour=peak_hour,
            peak_calls=peak_calls
        )
    
    def calculate_sla_metrics(
        self,
        calls_data: List[Dict[str, Any]],
        service_level_threshold: int = 20
    ) -> SLAMetrics:
        """
        Calcula métricas de SLA e compliance.
        
        Args:
            calls_data: Lista de chamadas
            service_level_threshold: Meta de service level
            
        Returns:
            SLAMetrics com métricas de SLA
        """
        if not calls_data:
            return SLAMetrics(
                service_level_target=service_level_threshold,
                service_level_achieved=0,
                sla_compliance=0,
                calls_within_sla=0,
                calls_outside_sla=0,
                average_response_time=0,
                worst_performance_hour="00:00",
                best_performance_hour="00:00"
            )
        
        total_calls = len(calls_data)
        
        # Calls within/outside SLA
        calls_within_sla = len([
            call for call in calls_data 
            if call.get('wait_time', 0) <= service_level_threshold
        ])
        calls_outside_sla = total_calls - calls_within_sla
        
        # Service level achieved
        service_level_achieved = (calls_within_sla / total_calls * 100) if total_calls > 0 else 0
        
        # SLA compliance (considerando meta de 80%)
        sla_target = 80.0
        sla_compliance = min(service_level_achieved / sla_target * 100, 100)
        
        # Average response time
        response_times = [call.get('wait_time', 0) for call in calls_data]
        average_response_time = mean(response_times) if response_times else 0
        
        # Best/worst performance hours
        best_hour, worst_hour = self._analyze_hourly_performance(calls_data, service_level_threshold)
        
        return SLAMetrics(
            service_level_target=service_level_threshold,
            service_level_achieved=round(service_level_achieved, 2),
            sla_compliance=round(sla_compliance, 2),
            calls_within_sla=calls_within_sla,
            calls_outside_sla=calls_outside_sla,
            average_response_time=round(average_response_time, 2),
            worst_performance_hour=worst_hour,
            best_performance_hour=best_hour
        )
    
    def _calculate_productivity_score(
        self,
        occupancy_rate: float,
        calls_per_hour: float,
        fcr: float,
        avg_call_duration: float
    ) -> float:
        """
        Calcula score de produtividade baseado em múltiplos fatores.
        """
        # Normalizar métricas (0-100)
        occupancy_score = min(occupancy_rate, 100)
        calls_score = min(calls_per_hour * 10, 100)  # Assumindo 10 calls/hour como excelente
        fcr_score = fcr
        
        # Penalizar chamadas muito longas ou muito curtas
        duration_score = 100
        if avg_call_duration > 600:  # > 10 minutos
            duration_score = max(50, 100 - (avg_call_duration - 600) / 60)
        elif avg_call_duration < 60:  # < 1 minuto
            duration_score = max(50, avg_call_duration / 60 * 100)
        
        # Peso das métricas
        productivity_score = (
            occupancy_score * 0.3 +
            calls_score * 0.25 +
            fcr_score * 0.25 +
            duration_score * 0.2
        )
        
        return min(productivity_score, 100)
    
    def _find_peak_hour(self, calls_data: List[Dict[str, Any]]) -> tuple[str, int]:
        """
        Encontra a hora de pico de chamadas.
        """
        hourly_counts = {}
        
        for call in calls_data:
            if 'start_time' in call:
                try:
                    if isinstance(call['start_time'], str):
                        dt = datetime.fromisoformat(call['start_time'].replace('Z', '+00:00'))
                    else:
                        dt = call['start_time']
                    
                    hour = dt.strftime('%H:00')
                    hourly_counts[hour] = hourly_counts.get(hour, 0) + 1
                except:
                    continue
        
        if not hourly_counts:
            return "00:00", 0
        
        peak_hour = max(hourly_counts, key=hourly_counts.get)
        peak_calls = hourly_counts[peak_hour]
        
        return peak_hour, peak_calls
    
    def _analyze_hourly_performance(
        self,
        calls_data: List[Dict[str, Any]],
        threshold: int
    ) -> tuple[str, str]:
        """
        Analisa performance por hora para encontrar melhor e pior horário.
        """
        hourly_performance = {}
        
        for call in calls_data:
            if 'start_time' in call and 'wait_time' in call:
                try:
                    if isinstance(call['start_time'], str):
                        dt = datetime.fromisoformat(call['start_time'].replace('Z', '+00:00'))
                    else:
                        dt = call['start_time']
                    
                    hour = dt.strftime('%H:00')
                    
                    if hour not in hourly_performance:
                        hourly_performance[hour] = {'total': 0, 'within_sla': 0}
                    
                    hourly_performance[hour]['total'] += 1
                    if call['wait_time'] <= threshold:
                        hourly_performance[hour]['within_sla'] += 1
                except:
                    continue
        
        if not hourly_performance:
            return "00:00", "00:00"
        
        # Calcular percentual de performance por hora
        hour_percentages = {}
        for hour, data in hourly_performance.items():
            if data['total'] > 0:
                hour_percentages[hour] = data['within_sla'] / data['total'] * 100
        
        if not hour_percentages:
            return "00:00", "00:00"
        
        best_hour = max(hour_percentages, key=hour_percentages.get)
        worst_hour = min(hour_percentages, key=hour_percentages.get)
        
        return best_hour, worst_hour
