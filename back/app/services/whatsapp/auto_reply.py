"""
Sistema de respostas automáticas para WhatsApp
"""

import re
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, time
from sqlalchemy.orm import Session

from app.database.models.whatsapp import WhatsAppAutoReply, WhatsAppConversation
from app.services.integrations.evolution.client import EvolutionAPIClient

logger = logging.getLogger(__name__)


class WhatsAppAutoReplyService:
    """Serviço de respostas automáticas"""
    
    def __init__(self):
        self.business_hours = {
            'start': time(8, 0),   # 08:00
            'end': time(18, 0),    # 18:00
            'days': [0, 1, 2, 3, 4]  # Segunda a Sexta (0=Segunda)
        }
        
        # Templates padrão
        self.default_templates = {
            'greeting': "Olá! Obrigado por entrar em contato. Em que posso ajudá-lo?",
            'out_of_hours': "Olá! No momento estamos fora do horário de atendimento (08:00 às 18:00, Segunda a Sexta). Sua mensagem é importante para nós e responderemos assim que possível.",
            'queue': "Obrigado por aguardar! Você está na fila de atendimento. Um de nossos atendentes irá responder em breve.",
            'transfer': "Sua conversa foi transferida para um especialista. Aguarde um momento.",
            'closing': "Obrigado por entrar em contato! Se precisar de mais alguma coisa, estaremos aqui para ajudar."
        }
    
    def is_business_hours(self) -> bool:
        """Verificar se está no horário comercial"""
        now = datetime.now()
        current_time = now.time()
        current_day = now.weekday()
        
        return (
            current_day in self.business_hours['days'] and
            self.business_hours['start'] <= current_time <= self.business_hours['end']
        )
    
    async def process_incoming_message(self, db: Session, conversation: WhatsAppConversation,
                                     message_content: str, evolution_client: EvolutionAPIClient) -> bool:
        """Processar mensagem recebida e verificar se precisa de resposta automática"""
        
        # Verificar se é a primeira mensagem da conversa
        if conversation.unread_count == 1:  # Primeira mensagem
            return await self._handle_first_message(db, conversation, evolution_client)
        
        # Verificar respostas automáticas baseadas em palavras-chave
        auto_reply = await self._find_matching_auto_reply(db, message_content, conversation.instance_id)
        
        if auto_reply:
            return await self._send_auto_reply(conversation, auto_reply.response_message, evolution_client)
        
        return False
    
    async def _handle_first_message(self, db: Session, conversation: WhatsAppConversation,
                                   evolution_client: EvolutionAPIClient) -> bool:
        """Lidar com primeira mensagem da conversa"""
        
        if not self.is_business_hours():
            # Fora do horário comercial
            message = self.default_templates['out_of_hours']
            return await self._send_auto_reply(conversation, message, evolution_client)
        
        # Horário comercial - saudação
        message = self.default_templates['greeting']
        return await self._send_auto_reply(conversation, message, evolution_client)
    
    async def _find_matching_auto_reply(self, db: Session, message_content: str,
                                      instance_id: int) -> Optional[WhatsAppAutoReply]:
        """Encontrar resposta automática que corresponde à mensagem"""
        
        # Buscar respostas automáticas ativas
        auto_replies = db.query(WhatsAppAutoReply).filter(
            WhatsAppAutoReply.is_active == True,
            (WhatsAppAutoReply.instance_id == instance_id) | (WhatsAppAutoReply.instance_id.is_(None))
        ).order_by(WhatsAppAutoReply.priority.desc()).all()
        
        message_lower = message_content.lower()
        
        for auto_reply in auto_replies:
            try:
                keywords = json.loads(auto_reply.trigger_keywords)
                
                for keyword in keywords:
                    # Suporte a regex
                    if keyword.startswith('regex:'):
                        pattern = keyword[6:]  # Remove 'regex:'
                        if re.search(pattern, message_lower, re.IGNORECASE):
                            return auto_reply
                    else:
                        # Busca simples por palavra-chave
                        if keyword.lower() in message_lower:
                            return auto_reply
                            
            except (json.JSONDecodeError, Exception) as e:
                logger.error(f"Erro ao processar auto-reply {auto_reply.id}: {e}")
                continue
        
        return None
    
    async def _send_auto_reply(self, conversation: WhatsAppConversation, message: str,
                             evolution_client: EvolutionAPIClient) -> bool:
        """Enviar resposta automática"""
        try:
            # Personalizar mensagem com variáveis
            personalized_message = self._personalize_message(message, conversation)
            
            # Enviar via Evolution API
            result = await evolution_client.send_text_message(
                conversation.instance.name,
                conversation.customer_number,
                personalized_message
            )
            
            if result:
                logger.info(f"Resposta automática enviada para {conversation.customer_number}")
                return True
            
        except Exception as e:
            logger.error(f"Erro ao enviar resposta automática: {e}")
        
        return False
    
    def _personalize_message(self, message: str, conversation: WhatsAppConversation) -> str:
        """Personalizar mensagem com variáveis"""
        variables = {
            '{customer_name}': conversation.customer_name,
            '{customer_number}': conversation.customer_number,
            '{current_time}': datetime.now().strftime('%H:%M'),
            '{current_date}': datetime.now().strftime('%d/%m/%Y'),
            '{business_hours}': f"{self.business_hours['start'].strftime('%H:%M')} às {self.business_hours['end'].strftime('%H:%M')}"
        }
        
        for variable, value in variables.items():
            message = message.replace(variable, value)
        
        return message
    
    async def create_auto_reply_rule(self, db: Session, trigger_keywords: List[str],
                                   response_message: str, priority: int = 0,
                                   instance_id: int = None) -> WhatsAppAutoReply:
        """Criar nova regra de resposta automática"""
        
        auto_reply = WhatsAppAutoReply(
            trigger_keywords=json.dumps(trigger_keywords),
            response_message=response_message,
            priority=priority,
            instance_id=instance_id
        )
        
        db.add(auto_reply)
        db.commit()
        db.refresh(auto_reply)
        
        logger.info(f"Nova regra de auto-resposta criada: {auto_reply.id}")
        return auto_reply
    
    async def setup_default_rules(self, db: Session, instance_id: int):
        """Configurar regras padrão de resposta automática"""
        
        default_rules = [
            {
                'keywords': ['oi', 'olá', 'bom dia', 'boa tarde', 'boa noite'],
                'response': self.default_templates['greeting'],
                'priority': 1
            },
            {
                'keywords': ['horário', 'funcionamento', 'aberto', 'fechado'],
                'response': f"Nosso horário de atendimento é de {self.business_hours['start'].strftime('%H:%M')} às {self.business_hours['end'].strftime('%H:%M')}, de Segunda a Sexta-feira.",
                'priority': 2
            },
            {
                'keywords': ['preço', 'valor', 'quanto custa', 'orçamento'],
                'response': "Para informações sobre preços e orçamentos, um de nossos consultores irá atendê-lo em breve.",
                'priority': 2
            },
            {
                'keywords': ['regex:^(tchau|até logo|obrigad[oa]|valeu)'],
                'response': self.default_templates['closing'],
                'priority': 1
            }
        ]
        
        for rule in default_rules:
            existing = db.query(WhatsAppAutoReply).filter(
                WhatsAppAutoReply.trigger_keywords == json.dumps(rule['keywords']),
                WhatsAppAutoReply.instance_id == instance_id
            ).first()
            
            if not existing:
                await self.create_auto_reply_rule(
                    db, rule['keywords'], rule['response'],
                    rule['priority'], instance_id
                )


# Instância global do serviço
auto_reply_service = WhatsAppAutoReplyService()
