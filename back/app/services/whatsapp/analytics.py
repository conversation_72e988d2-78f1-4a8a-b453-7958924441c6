"""
Sistema de analytics para WhatsApp
"""

from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, text
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional
import logging

from app.database.models.whatsapp import (
    WhatsAppConversation, WhatsAppMessage, WhatsAppInstance,
    ConversationStatus, MessageType
)

logger = logging.getLogger(__name__)


class WhatsAppAnalyticsService:
    """Serviço de analytics para WhatsApp"""
    
    def get_dashboard_stats(self, db: Session, instance_id: int = None,
                           date_from: date = None, date_to: date = None) -> Dict[str, Any]:
        """Obter estatísticas do dashboard"""
        
        if not date_from:
            date_from = date.today() - timedelta(days=30)
        if not date_to:
            date_to = date.today()
        
        # Query base
        conversations_query = db.query(WhatsAppConversation)
        messages_query = db.query(WhatsAppMessage).join(WhatsAppConversation)
        
        if instance_id:
            conversations_query = conversations_query.filter(
                WhatsAppConversation.instance_id == instance_id
            )
            messages_query = messages_query.filter(
                WhatsAppConversation.instance_id == instance_id
            )
        
        # Filtro de data
        date_filter = and_(
            func.date(WhatsAppConversation.created_at) >= date_from,
            func.date(WhatsAppConversation.created_at) <= date_to
        )
        
        conversations_query = conversations_query.filter(date_filter)
        messages_query = messages_query.filter(
            and_(
                func.date(WhatsAppMessage.timestamp) >= date_from,
                func.date(WhatsAppMessage.timestamp) <= date_to
            )
        )
        
        # Estatísticas básicas
        total_conversations = conversations_query.count()
        active_conversations = conversations_query.filter(
            WhatsAppConversation.status == ConversationStatus.OPEN
        ).count()
        waiting_conversations = conversations_query.filter(
            WhatsAppConversation.status == ConversationStatus.WAITING
        ).count()
        
        total_messages = messages_query.count()
        customer_messages = messages_query.filter(
            WhatsAppMessage.sender_type == 'customer'
        ).count()
        agent_messages = messages_query.filter(
            WhatsAppMessage.sender_type == 'agent'
        ).count()
        
        # Tempo médio de resposta
        avg_response_time = self._calculate_avg_response_time(db, instance_id, date_from, date_to)
        
        # Conversas por dia
        conversations_by_day = self._get_conversations_by_day(db, instance_id, date_from, date_to)
        
        # Mensagens por tipo
        messages_by_type = self._get_messages_by_type(db, instance_id, date_from, date_to)
        
        # Top clientes
        top_customers = self._get_top_customers(db, instance_id, date_from, date_to)
        
        return {
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'conversations': {
                'total': total_conversations,
                'active': active_conversations,
                'waiting': waiting_conversations,
                'closed': total_conversations - active_conversations - waiting_conversations
            },
            'messages': {
                'total': total_messages,
                'from_customers': customer_messages,
                'from_agents': agent_messages,
                'by_type': messages_by_type
            },
            'performance': {
                'avg_response_time_minutes': avg_response_time,
                'conversations_per_day': conversations_by_day
            },
            'customers': {
                'top_customers': top_customers
            }
        }
    
    def _calculate_avg_response_time(self, db: Session, instance_id: int = None,
                                   date_from: date = None, date_to: date = None) -> float:
        """Calcular tempo médio de resposta em minutos"""
        try:
            # Query complexa para calcular tempo de resposta
            # Isso é uma simplificação - em produção seria mais complexo
            
            query = """
            WITH response_times AS (
                SELECT 
                    c.id as conversation_id,
                    LAG(m.timestamp) OVER (PARTITION BY c.id ORDER BY m.timestamp) as prev_message_time,
                    m.timestamp as current_message_time,
                    LAG(m.sender_type) OVER (PARTITION BY c.id ORDER BY m.timestamp) as prev_sender_type,
                    m.sender_type as current_sender_type
                FROM whatsapp_messages m
                JOIN whatsapp_conversations c ON m.conversation_id = c.id
                WHERE DATE(m.timestamp) BETWEEN :date_from AND :date_to
                {instance_filter}
            )
            SELECT AVG(
                EXTRACT(EPOCH FROM (current_message_time - prev_message_time)) / 60
            ) as avg_response_time
            FROM response_times
            WHERE prev_sender_type = 'customer' 
            AND current_sender_type = 'agent'
            AND prev_message_time IS NOT NULL
            """
            
            instance_filter = ""
            params = {'date_from': date_from, 'date_to': date_to}
            
            if instance_id:
                instance_filter = "AND c.instance_id = :instance_id"
                params['instance_id'] = instance_id
            
            query = query.format(instance_filter=instance_filter)
            
            result = db.execute(text(query), params).fetchone()
            return round(result[0] or 0, 2)
            
        except Exception as e:
            logger.error(f"Erro ao calcular tempo médio de resposta: {e}")
            return 0.0
    
    def _get_conversations_by_day(self, db: Session, instance_id: int = None,
                                date_from: date = None, date_to: date = None) -> List[Dict]:
        """Obter conversas por dia"""
        query = db.query(
            func.date(WhatsAppConversation.created_at).label('date'),
            func.count(WhatsAppConversation.id).label('count')
        )
        
        if instance_id:
            query = query.filter(WhatsAppConversation.instance_id == instance_id)
        
        if date_from and date_to:
            query = query.filter(
                and_(
                    func.date(WhatsAppConversation.created_at) >= date_from,
                    func.date(WhatsAppConversation.created_at) <= date_to
                )
            )
        
        results = query.group_by(
            func.date(WhatsAppConversation.created_at)
        ).order_by(
            func.date(WhatsAppConversation.created_at)
        ).all()
        
        return [
            {
                'date': result.date.isoformat(),
                'conversations': result.count
            }
            for result in results
        ]
    
    def _get_messages_by_type(self, db: Session, instance_id: int = None,
                            date_from: date = None, date_to: date = None) -> Dict[str, int]:
        """Obter mensagens por tipo"""
        query = db.query(
            WhatsAppMessage.message_type,
            func.count(WhatsAppMessage.id).label('count')
        ).join(WhatsAppConversation)
        
        if instance_id:
            query = query.filter(WhatsAppConversation.instance_id == instance_id)
        
        if date_from and date_to:
            query = query.filter(
                and_(
                    func.date(WhatsAppMessage.timestamp) >= date_from,
                    func.date(WhatsAppMessage.timestamp) <= date_to
                )
            )
        
        results = query.group_by(WhatsAppMessage.message_type).all()
        
        return {
            result.message_type.value: result.count
            for result in results
        }
    
    def _get_top_customers(self, db: Session, instance_id: int = None,
                         date_from: date = None, date_to: date = None,
                         limit: int = 10) -> List[Dict]:
        """Obter top clientes por número de mensagens"""
        query = db.query(
            WhatsAppConversation.customer_name,
            WhatsAppConversation.customer_number,
            func.count(WhatsAppMessage.id).label('message_count')
        ).join(WhatsAppMessage)
        
        if instance_id:
            query = query.filter(WhatsAppConversation.instance_id == instance_id)
        
        if date_from and date_to:
            query = query.filter(
                and_(
                    func.date(WhatsAppMessage.timestamp) >= date_from,
                    func.date(WhatsAppMessage.timestamp) <= date_to
                )
            )
        
        results = query.group_by(
            WhatsAppConversation.customer_name,
            WhatsAppConversation.customer_number
        ).order_by(
            desc('message_count')
        ).limit(limit).all()
        
        return [
            {
                'customer_name': result.customer_name,
                'customer_number': result.customer_number,
                'message_count': result.message_count
            }
            for result in results
        ]
    
    def get_agent_performance(self, db: Session, user_id: int = None,
                            date_from: date = None, date_to: date = None) -> Dict[str, Any]:
        """Obter performance dos agentes"""
        
        query = db.query(
            WhatsAppConversation.assigned_user_id,
            func.count(WhatsAppConversation.id).label('conversations_handled'),
            func.count(
                func.nullif(WhatsAppConversation.status, ConversationStatus.OPEN)
            ).label('conversations_closed')
        )
        
        if user_id:
            query = query.filter(WhatsAppConversation.assigned_user_id == user_id)
        
        if date_from and date_to:
            query = query.filter(
                and_(
                    func.date(WhatsAppConversation.created_at) >= date_from,
                    func.date(WhatsAppConversation.created_at) <= date_to
                )
            )
        
        results = query.group_by(
            WhatsAppConversation.assigned_user_id
        ).all()
        
        return [
            {
                'user_id': result.assigned_user_id,
                'conversations_handled': result.conversations_handled,
                'conversations_closed': result.conversations_closed,
                'close_rate': round(
                    (result.conversations_closed / result.conversations_handled * 100)
                    if result.conversations_handled > 0 else 0, 2
                )
            }
            for result in results
            if result.assigned_user_id is not None
        ]


# Instância global do serviço
analytics_service = WhatsAppAnalyticsService()
