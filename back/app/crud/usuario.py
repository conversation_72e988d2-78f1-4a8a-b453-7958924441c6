"""
CRUD operations para usuários.
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime

from app.database.models import <PERSON><PERSON><PERSON>, NivelUsuario
from app.schemas.usuario import UsuarioCreate, UsuarioUpdate
from app.utils.security import hash_password, verify_password, encrypt_email_password

class UsuarioCRUD:
    """Classe para operações CRUD de usuários."""

    @staticmethod
    def get_by_id(db: Session, user_id: int) -> Optional[Usuario]:
        """Buscar usuário por ID."""
        return db.query(Usuario).filter(Usuario.id == user_id).first()

    @staticmethod
    def get_by_login(db: Session, login: str) -> Optional[Usuario]:
        """Buscar usuário por login."""
        return db.query(Usuario).filter(Usuario.login == login.lower()).first()

    @staticmethod
    def get_by_email(db: Session, email: str) -> Optional[Usuario]:
        """Buscar usuário por email corporativo."""
        return db.query(Usuario).filter(Usuario.email_corporativo == email).first()

    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100, ativo_apenas: bool = True) -> List[Usuario]:
        """Listar todos os usuários."""
        query = db.query(Usuario)
        if ativo_apenas:
            query = query.filter(Usuario.ativo == True)
        return query.offset(skip).limit(limit).all()

    @staticmethod
    def create(db: Session, usuario_data: UsuarioCreate) -> Usuario:
        """Criar novo usuário."""
        # Criptografar senhas
        senha_hash = hash_password(usuario_data.senha)
        senha_email_hash = encrypt_email_password(usuario_data.senha_email_corporativo)

        # Criar usuário
        db_usuario = Usuario(
            nome=usuario_data.nome,
            sobrenome=usuario_data.sobrenome,
            login=usuario_data.login.lower(),
            senha=senha_hash,
            nivel_usuario=NivelUsuario(usuario_data.nivel_usuario),
            email_corporativo=usuario_data.email_corporativo,
            senha_email_corporativo=senha_email_hash,
            ativo=usuario_data.ativo
        )

        db.add(db_usuario)
        db.commit()
        db.refresh(db_usuario)
        return db_usuario

    @staticmethod
    def update(db: Session, user_id: int, usuario_data: UsuarioUpdate) -> Optional[Usuario]:
        """Atualizar usuário."""
        db_usuario = UsuarioCRUD.get_by_id(db, user_id)
        if not db_usuario:
            return None

        # Atualizar campos fornecidos
        update_data = usuario_data.dict(exclude_unset=True)

        # Criptografar senha se fornecida
        if "senha" in update_data:
            update_data["senha"] = hash_password(update_data["senha"])

        # Criptografar senha do email se fornecida
        if "senha_email_corporativo" in update_data:
            update_data["senha_email_corporativo"] = encrypt_email_password(
                update_data["senha_email_corporativo"]
            )

        # Converter enum se necessário
        if "nivel_usuario" in update_data:
            update_data["nivel_usuario"] = NivelUsuario(update_data["nivel_usuario"])

        # Converter login para minúsculo
        if "login" in update_data:
            update_data["login"] = update_data["login"].lower()

        for field, value in update_data.items():
            setattr(db_usuario, field, value)

        db.commit()
        db.refresh(db_usuario)
        return db_usuario

    @staticmethod
    def delete(db: Session, user_id: int) -> bool:
        """Deletar usuário (soft delete)."""
        db_usuario = UsuarioCRUD.get_by_id(db, user_id)
        if not db_usuario:
            return False

        db_usuario.ativo = False
        db.commit()
        return True

    @staticmethod
    def authenticate(db: Session, login: str, senha: str) -> Optional[Usuario]:
        """Autenticar usuário."""
        usuario = UsuarioCRUD.get_by_login(db, login)
        if not usuario:
            return None

        if not usuario.ativo:
            return None

        if not verify_password(senha, usuario.senha):
            return None

        # Atualizar último login
        usuario.ultimo_login = datetime.utcnow()
        db.commit()

        return usuario

    @staticmethod
    def change_password(db: Session, user_id: int, senha_atual: str, nova_senha: str) -> bool:
        """Alterar senha do usuário."""
        usuario = UsuarioCRUD.get_by_id(db, user_id)
        if not usuario:
            return False

        if not verify_password(senha_atual, usuario.senha):
            return False

        usuario.senha = hash_password(nova_senha)
        db.commit()
        return True

    @staticmethod
    def get_admins(db: Session) -> List[Usuario]:
        """Buscar todos os administradores."""
        return db.query(Usuario).filter(
            and_(
                Usuario.nivel_usuario == NivelUsuario.ADMINISTRADOR,
                Usuario.ativo == True
            )
        ).all()

    @staticmethod
    def get_agentes(db: Session) -> List[Usuario]:
        """Buscar todos os agentes."""
        return db.query(Usuario).filter(
            and_(
                Usuario.nivel_usuario == NivelUsuario.AGENTE,
                Usuario.ativo == True
            )
        ).all()

    @staticmethod
    def get_by_email(db: Session, email: str) -> Optional[Usuario]:
        """Buscar usuário por email."""
        return db.query(Usuario).filter(Usuario.email_corporativo == email).first()

    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100) -> List[Usuario]:
        """Buscar todos os usuários com paginação."""
        return db.query(Usuario).filter(Usuario.ativo == True).offset(skip).limit(limit).all()

    @staticmethod
    def count_active_users(db: Session) -> int:
        """Contar usuários ativos."""
        return db.query(Usuario).filter(Usuario.ativo == True).count()

    @staticmethod
    def count_admins(db: Session) -> int:
        """Contar administradores ativos."""
        return db.query(Usuario).filter(
            and_(
                Usuario.nivel_usuario == NivelUsuario.ADMINISTRADOR,
                Usuario.ativo == True
            )
        ).count()

    @staticmethod
    def count_agentes(db: Session) -> int:
        """Contar agentes ativos."""
        return db.query(Usuario).filter(
            and_(
                Usuario.nivel_usuario == NivelUsuario.AGENTE,
                Usuario.ativo == True
            )
        ).count()

    @staticmethod
    def update_fields(db: Session, user_id: int, fields: dict) -> Optional[Usuario]:
        """Atualizar campos específicos do usuário."""
        db_usuario = UsuarioCRUD.get_by_id(db, user_id)
        if not db_usuario:
            return None

        # Atualizar campos fornecidos
        for field, value in fields.items():
            if hasattr(db_usuario, field):
                setattr(db_usuario, field, value)

        db.commit()
        db.refresh(db_usuario)
        return db_usuario

# Instância global do CRUD
usuario_crud = UsuarioCRUD()
