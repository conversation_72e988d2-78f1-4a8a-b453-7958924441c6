"""
CRUD operations para WhatsApp
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from typing import List, Optional
from datetime import datetime

from app.database.models.whatsapp import (
    WhatsAppInstance, WhatsAppConversation, WhatsAppMessage,
    WhatsAppTemplate, WhatsAppAutoReply,
    InstanceStatus, ConversationStatus, MessageType, MessageStatus
)
from app.schemas.whatsapp import (
    CreateInstanceRequest, SendMessageRequest,
    WhatsAppConversation as ConversationSchema,
    WhatsAppMessage as MessageSchema
)


class WhatsAppInstanceCRUD:
    """CRUD para instâncias WhatsApp"""
    
    def create_instance(self, db: Session, name: str, webhook_url: str = None) -> WhatsAppInstance:
        """Criar nova instância"""
        instance = WhatsAppInstance(
            name=name,
            webhook_url=webhook_url,
            status=InstanceStatus.DISCONNECTED
        )
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance
    
    def get_instance_by_name(self, db: Session, name: str) -> Optional[WhatsAppInstance]:
        """Obter instância por nome"""
        return db.query(WhatsAppInstance).filter(WhatsAppInstance.name == name).first()
    
    def update_instance_status(self, db: Session, instance_id: int, status: InstanceStatus, 
                              phone_number: str = None, qr_code: str = None) -> WhatsAppInstance:
        """Atualizar status da instância"""
        instance = db.query(WhatsAppInstance).filter(WhatsAppInstance.id == instance_id).first()
        if instance:
            instance.status = status
            if phone_number:
                instance.phone_number = phone_number
            if qr_code:
                instance.qr_code = qr_code
            db.commit()
            db.refresh(instance)
        return instance
    
    def delete_instance(self, db: Session, instance_id: int) -> bool:
        """Deletar instância"""
        instance = db.query(WhatsAppInstance).filter(WhatsAppInstance.id == instance_id).first()
        if instance:
            db.delete(instance)
            db.commit()
            return True
        return False


class WhatsAppConversationCRUD:
    """CRUD para conversas WhatsApp"""
    
    def create_or_get_conversation(self, db: Session, external_id: str, customer_name: str,
                                  customer_number: str, instance_id: int) -> WhatsAppConversation:
        """Criar ou obter conversa existente"""
        conversation = db.query(WhatsAppConversation).filter(
            WhatsAppConversation.external_id == external_id
        ).first()
        
        if not conversation:
            conversation = WhatsAppConversation(
                external_id=external_id,
                customer_name=customer_name,
                customer_number=customer_number,
                instance_id=instance_id,
                status=ConversationStatus.WAITING
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
        
        return conversation
    
    def get_conversations(self, db: Session, status: ConversationStatus = None,
                         assigned_user_id: int = None, limit: int = 50) -> List[WhatsAppConversation]:
        """Obter lista de conversas"""
        query = db.query(WhatsAppConversation)
        
        if status:
            query = query.filter(WhatsAppConversation.status == status)
        
        if assigned_user_id:
            query = query.filter(WhatsAppConversation.assigned_user_id == assigned_user_id)
        
        return query.order_by(desc(WhatsAppConversation.last_message_time)).limit(limit).all()
    
    def assign_conversation(self, db: Session, conversation_id: int, user_id: int) -> WhatsAppConversation:
        """Atribuir conversa a um usuário"""
        conversation = db.query(WhatsAppConversation).filter(
            WhatsAppConversation.id == conversation_id
        ).first()
        
        if conversation:
            conversation.assigned_user_id = user_id
            conversation.status = ConversationStatus.ASSIGNED
            db.commit()
            db.refresh(conversation)
        
        return conversation
    
    def update_last_message(self, db: Session, conversation_id: int, message: str,
                           timestamp: datetime, increment_unread: bool = True) -> WhatsAppConversation:
        """Atualizar última mensagem da conversa"""
        conversation = db.query(WhatsAppConversation).filter(
            WhatsAppConversation.id == conversation_id
        ).first()
        
        if conversation:
            conversation.last_message = message
            conversation.last_message_time = timestamp
            if increment_unread:
                conversation.unread_count += 1
            db.commit()
            db.refresh(conversation)
        
        return conversation
    
    def mark_as_read(self, db: Session, conversation_id: int) -> WhatsAppConversation:
        """Marcar conversa como lida"""
        conversation = db.query(WhatsAppConversation).filter(
            WhatsAppConversation.id == conversation_id
        ).first()
        
        if conversation:
            conversation.unread_count = 0
            db.commit()
            db.refresh(conversation)
        
        return conversation


class WhatsAppMessageCRUD:
    """CRUD para mensagens WhatsApp"""
    
    def create_message(self, db: Session, external_id: str, conversation_id: int,
                      sender_type: str, sender_name: str, content: str,
                      message_type: MessageType = MessageType.TEXT,
                      timestamp: datetime = None, sender_number: str = None) -> WhatsAppMessage:
        """Criar nova mensagem"""
        if not timestamp:
            timestamp = datetime.now()
        
        message = WhatsAppMessage(
            external_id=external_id,
            conversation_id=conversation_id,
            sender_type=sender_type,
            sender_name=sender_name,
            sender_number=sender_number,
            content=content,
            message_type=message_type,
            timestamp=timestamp,
            status=MessageStatus.SENT
        )
        
        db.add(message)
        db.commit()
        db.refresh(message)
        return message
    
    def get_conversation_messages(self, db: Session, conversation_id: int,
                                 limit: int = 100) -> List[WhatsAppMessage]:
        """Obter mensagens de uma conversa"""
        return db.query(WhatsAppMessage).filter(
            WhatsAppMessage.conversation_id == conversation_id
        ).order_by(WhatsAppMessage.timestamp).limit(limit).all()
    
    def update_message_status(self, db: Session, message_id: int,
                             status: MessageStatus) -> WhatsAppMessage:
        """Atualizar status da mensagem"""
        message = db.query(WhatsAppMessage).filter(WhatsAppMessage.id == message_id).first()
        if message:
            message.status = status
            db.commit()
            db.refresh(message)
        return message


class WhatsAppTemplateCRUD:
    """CRUD para templates de mensagem"""
    
    def create_template(self, db: Session, name: str, content: str,
                       category: str = None, created_by: int = None) -> WhatsAppTemplate:
        """Criar novo template"""
        template = WhatsAppTemplate(
            name=name,
            content=content,
            category=category,
            created_by=created_by
        )
        db.add(template)
        db.commit()
        db.refresh(template)
        return template
    
    def get_templates(self, db: Session, category: str = None,
                     is_active: bool = True) -> List[WhatsAppTemplate]:
        """Obter templates"""
        query = db.query(WhatsAppTemplate)
        
        if category:
            query = query.filter(WhatsAppTemplate.category == category)
        
        if is_active is not None:
            query = query.filter(WhatsAppTemplate.is_active == is_active)
        
        return query.all()


# Instâncias globais dos CRUDs
instance_crud = WhatsAppInstanceCRUD()
conversation_crud = WhatsAppConversationCRUD()
message_crud = WhatsAppMessageCRUD()
template_crud = WhatsAppTemplateCRUD()
