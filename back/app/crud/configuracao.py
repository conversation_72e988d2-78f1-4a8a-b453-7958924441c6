"""
CRUD operations para configurações do sistema.
"""

from sqlalchemy.orm import Session
from app.database.models import ConfiguracaoSistema
import json
from typing import Optional, Dict, Any


def get_configuracao_by_chave(db: Session, chave: str) -> Optional[ConfiguracaoSistema]:
    """
    Busca uma configuração pela chave.
    
    Args:
        db: Sessão do banco de dados
        chave: Chave da configuração
    
    Returns:
        Configuração encontrada ou None
    """
    return db.query(ConfiguracaoSistema).filter(ConfiguracaoSistema.chave == chave).first()


def get_configuracoes_by_categoria(db: Session, categoria: str) -> list[ConfiguracaoSistema]:
    """
    Busca configurações por categoria.
    
    Args:
        db: Sessão do banco de dados
        categoria: Categoria das configurações
    
    Returns:
        Lista de configurações
    """
    return db.query(ConfiguracaoSistema).filter(ConfiguracaoSistema.categoria == categoria).all()


def create_configuracao(
    db: Session, 
    chave: str, 
    valor: Any, 
    descricao: str = None, 
    categoria: str = "geral"
) -> ConfiguracaoSistema:
    """
    Cria uma nova configuração.
    
    Args:
        db: Sessão do banco de dados
        chave: Chave única da configuração
        valor: Valor da configuração (será convertido para JSON se necessário)
        descricao: Descrição da configuração
        categoria: Categoria da configuração
    
    Returns:
        Configuração criada
    """
    # Converter valor para string JSON se for dict/list
    if isinstance(valor, (dict, list)):
        valor_str = json.dumps(valor)
    else:
        valor_str = str(valor) if valor is not None else None
    
    db_config = ConfiguracaoSistema(
        chave=chave,
        valor=valor_str,
        descricao=descricao,
        categoria=categoria
    )
    db.add(db_config)
    db.commit()
    db.refresh(db_config)
    return db_config


def update_configuracao(
    db: Session, 
    chave: str, 
    valor: Any, 
    descricao: str = None
) -> Optional[ConfiguracaoSistema]:
    """
    Atualiza uma configuração existente.
    
    Args:
        db: Sessão do banco de dados
        chave: Chave da configuração
        valor: Novo valor da configuração
        descricao: Nova descrição (opcional)
    
    Returns:
        Configuração atualizada ou None se não encontrada
    """
    db_config = get_configuracao_by_chave(db, chave)
    if not db_config:
        return None
    
    # Converter valor para string JSON se for dict/list
    if isinstance(valor, (dict, list)):
        valor_str = json.dumps(valor)
    else:
        valor_str = str(valor) if valor is not None else None
    
    db_config.valor = valor_str
    if descricao is not None:
        db_config.descricao = descricao
    
    db.commit()
    db.refresh(db_config)
    return db_config


def upsert_configuracao(
    db: Session, 
    chave: str, 
    valor: Any, 
    descricao: str = None, 
    categoria: str = "geral"
) -> ConfiguracaoSistema:
    """
    Cria ou atualiza uma configuração.
    
    Args:
        db: Sessão do banco de dados
        chave: Chave da configuração
        valor: Valor da configuração
        descricao: Descrição da configuração
        categoria: Categoria da configuração
    
    Returns:
        Configuração criada ou atualizada
    """
    existing = get_configuracao_by_chave(db, chave)
    if existing:
        return update_configuracao(db, chave, valor, descricao)
    else:
        return create_configuracao(db, chave, valor, descricao, categoria)


def delete_configuracao(db: Session, chave: str) -> bool:
    """
    Remove uma configuração.
    
    Args:
        db: Sessão do banco de dados
        chave: Chave da configuração
    
    Returns:
        True se removida, False se não encontrada
    """
    db_config = get_configuracao_by_chave(db, chave)
    if not db_config:
        return False
    
    db.delete(db_config)
    db.commit()
    return True


def get_valor_configuracao(db: Session, chave: str, default: Any = None) -> Any:
    """
    Obtém o valor de uma configuração, com suporte a JSON.
    
    Args:
        db: Sessão do banco de dados
        chave: Chave da configuração
        default: Valor padrão se não encontrada
    
    Returns:
        Valor da configuração (convertido de JSON se aplicável) ou valor padrão
    """
    config = get_configuracao_by_chave(db, chave)
    if not config or config.valor is None:
        return default
    
    # Tentar converter de JSON
    try:
        return json.loads(config.valor)
    except (json.JSONDecodeError, TypeError):
        # Se não for JSON válido, retornar como string
        return config.valor


def get_configuracoes_microsoft_graph(db: Session) -> Dict[str, Any]:
    """
    Obtém todas as configurações do Microsoft Graph.
    
    Args:
        db: Sessão do banco de dados
    
    Returns:
        Dicionário com as configurações do Microsoft Graph
    """
    configs = get_configuracoes_by_categoria(db, "microsoft_graph")
    result = {}
    
    for config in configs:
        try:
            result[config.chave] = json.loads(config.valor) if config.valor else None
        except (json.JSONDecodeError, TypeError):
            result[config.chave] = config.valor
    
    return result


def set_configuracoes_microsoft_graph(
    db: Session, 
    client_id: str, 
    client_secret: str, 
    tenant_id: str
) -> Dict[str, ConfiguracaoSistema]:
    """
    Define as configurações do Microsoft Graph.
    
    Args:
        db: Sessão do banco de dados
        client_id: Client ID do Azure AD
        client_secret: Client Secret do Azure AD
        tenant_id: Tenant ID do Azure AD
    
    Returns:
        Dicionário com as configurações criadas/atualizadas
    """
    configs = {}
    
    configs['client_id'] = upsert_configuracao(
        db, 
        "microsoft_graph_client_id", 
        client_id,
        "Client ID do Microsoft Graph (Azure AD)",
        "microsoft_graph"
    )
    
    configs['client_secret'] = upsert_configuracao(
        db, 
        "microsoft_graph_client_secret", 
        client_secret,
        "Client Secret do Microsoft Graph (Azure AD)",
        "microsoft_graph"
    )
    
    configs['tenant_id'] = upsert_configuracao(
        db, 
        "microsoft_graph_tenant_id", 
        tenant_id,
        "Tenant ID do Microsoft Graph (Azure AD)",
        "microsoft_graph"
    )
    
    return configs


def remove_configuracoes_microsoft_graph(db: Session) -> bool:
    """
    Remove todas as configurações do Microsoft Graph.
    
    Args:
        db: Sessão do banco de dados
    
    Returns:
        True se pelo menos uma configuração foi removida
    """
    removed = False
    
    chaves = [
        "microsoft_graph_client_id",
        "microsoft_graph_client_secret", 
        "microsoft_graph_tenant_id"
    ]
    
    for chave in chaves:
        if delete_configuracao(db, chave):
            removed = True
    
    return removed
