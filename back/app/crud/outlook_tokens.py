"""
CRUD operations para tokens do Outlook.
"""

import json
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.outlook_tokens import OutlookTokenDB
from app.services.integrations.outlook.models import OutlookTokens, OutlookUser

logger = logging.getLogger(__name__)


class OutlookTokensCRUD:
    """CRUD operations para tokens do Outlook."""

    def create_or_update_tokens(
        self,
        db: Session,
        user_id: int,
        tokens: OutlookTokens,
        outlook_user: OutlookUser
    ) -> OutlookTokenDB:
        """
        Cria ou atualiza tokens do usuário.

        Args:
            db: Sessão do banco
            user_id: ID do usuário do sistema
            tokens: Tokens do Outlook
            outlook_user: Informações do usuário Outlook

        Returns:
            Registro de tokens criado/atualizado
        """
        try:
            # Verificar se já existe registro para este usuário
            existing = db.query(OutlookTokenDB).filter(
                OutlookTokenDB.user_id == user_id
            ).first()

            if existing:
                # Atualizar registro existente
                existing.access_token = tokens.access_token
                existing.refresh_token = tokens.refresh_token
                existing.token_type = tokens.token_type
                existing.scope = json.dumps(tokens.scope)
                existing.expires_at = tokens.expires_at
                existing.atualizado_em = datetime.now(timezone.utc)
                existing.last_used = datetime.now(timezone.utc)
                existing.is_active = True

                # Atualizar informações do usuário Outlook
                existing.outlook_user_id = outlook_user.id
                existing.outlook_email = outlook_user.email
                existing.outlook_name = outlook_user.display_name

                db.commit()
                db.refresh(existing)
                logger.info(f"Tokens atualizados para usuário {user_id}")
                return existing
            else:
                # Criar novo registro
                token_record = OutlookTokenDB(
                    user_id=user_id,
                    outlook_user_id=outlook_user.id,
                    outlook_email=outlook_user.email,
                    outlook_name=outlook_user.display_name,
                    access_token=tokens.access_token,
                    refresh_token=tokens.refresh_token,
                    token_type=tokens.token_type,
                    scope=json.dumps(tokens.scope),
                    expires_at=tokens.expires_at,
                    is_active=True,
                    last_used=datetime.now(timezone.utc)
                )

                db.add(token_record)
                db.commit()
                db.refresh(token_record)
                logger.info(f"Novos tokens criados para usuário {user_id}")
                return token_record

        except Exception as e:
            logger.error(f"Erro ao criar/atualizar tokens: {e}")
            db.rollback()
            raise

    def get_tokens_by_user_id(self, db: Session, user_id: int) -> Optional[OutlookTokenDB]:
        """
        Obtém tokens por ID do usuário.

        Args:
            db: Sessão do banco
            user_id: ID do usuário

        Returns:
            Registro de tokens ou None
        """
        return db.query(OutlookTokenDB).filter(
            and_(
                OutlookTokenDB.user_id == user_id,
                OutlookTokenDB.is_active == True
            )
        ).first()

    def get_tokens_by_outlook_user_id(self, db: Session, outlook_user_id: str) -> Optional[OutlookTokenDB]:
        """
        Obtém tokens por ID do usuário Outlook.

        Args:
            db: Sessão do banco
            outlook_user_id: ID do usuário no Outlook

        Returns:
            Registro de tokens ou None
        """
        return db.query(OutlookTokenDB).filter(
            and_(
                OutlookTokenDB.outlook_user_id == outlook_user_id,
                OutlookTokenDB.is_active == True
            )
        ).first()

    def update_tokens(self, db: Session, token_record: OutlookTokenDB, new_tokens: OutlookTokens) -> OutlookTokenDB:
        """
        Atualiza tokens existentes.

        Args:
            db: Sessão do banco
            token_record: Registro existente
            new_tokens: Novos tokens

        Returns:
            Registro atualizado
        """
        try:
            token_record.access_token = new_tokens.access_token
            if new_tokens.refresh_token:
                token_record.refresh_token = new_tokens.refresh_token
            token_record.expires_at = new_tokens.expires_at
            token_record.atualizado_em = datetime.now(timezone.utc)
            token_record.last_used = datetime.now(timezone.utc)

            db.commit()
            db.refresh(token_record)
            logger.info(f"Tokens atualizados para usuário {token_record.user_id}")
            return token_record

        except Exception as e:
            logger.error(f"Erro ao atualizar tokens: {e}")
            db.rollback()
            raise

    def deactivate_tokens(self, db: Session, user_id: int) -> bool:
        """
        Desativa tokens do usuário.

        Args:
            db: Sessão do banco
            user_id: ID do usuário

        Returns:
            True se desativado com sucesso
        """
        try:
            token_record = self.get_tokens_by_user_id(db, user_id)
            if token_record:
                token_record.is_active = False
                token_record.atualizado_em = datetime.now(timezone.utc)
                db.commit()
                logger.info(f"Tokens desativados para usuário {user_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"Erro ao desativar tokens: {e}")
            db.rollback()
            return False

    def cleanup_expired_tokens(self, db: Session) -> int:
        """
        Remove tokens expirados há mais de 30 dias.

        Args:
            db: Sessão do banco

        Returns:
            Número de registros removidos
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)

            expired_tokens = db.query(OutlookTokenDB).filter(
                and_(
                    OutlookTokenDB.expires_at < cutoff_date,
                    OutlookTokenDB.is_active == False
                )
            ).all()

            count = len(expired_tokens)

            for token in expired_tokens:
                db.delete(token)

            db.commit()
            logger.info(f"Removidos {count} tokens expirados")
            return count

        except Exception as e:
            logger.error(f"Erro ao limpar tokens expirados: {e}")
            db.rollback()
            return 0

    def to_outlook_tokens(self, token_record: OutlookTokenDB) -> OutlookTokens:
        """
        Converte registro do banco para modelo OutlookTokens.

        Args:
            token_record: Registro do banco

        Returns:
            Modelo OutlookTokens
        """
        scope = json.loads(token_record.scope) if token_record.scope else []

        return OutlookTokens(
            access_token=token_record.access_token,
            refresh_token=token_record.refresh_token,
            expires_at=token_record.expires_at,
            scope=scope,
            token_type=token_record.token_type
        )

    def to_outlook_user(self, token_record: OutlookTokenDB) -> OutlookUser:
        """
        Converte registro do banco para modelo OutlookUser.

        Args:
            token_record: Registro do banco

        Returns:
            Modelo OutlookUser
        """
        return OutlookUser(
            id=token_record.outlook_user_id,
            display_name=token_record.outlook_name or "",
            email=token_record.outlook_email or "",
            user_principal_name=token_record.outlook_email or ""
        )


# Instância global do CRUD
outlook_tokens_crud = OutlookTokensCRUD()
