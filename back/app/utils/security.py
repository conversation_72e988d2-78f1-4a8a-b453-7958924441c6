"""
Utilitários de segurança para autenticação e criptografia.
"""

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from passlib.hash import bcrypt

# Configuração do contexto de criptografia
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Importar configurações
from app.config.settings import settings

# Configurações JWT usando settings
SECRET_KEY = settings.JWT_SECRET_KEY
ALGORITHM = settings.JWT_ALGORITHM
ACCESS_TOKEN_EXPIRE_MINUTES = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES

def hash_password(password: str) -> str:
    """
    Criptografar senha usando bcrypt.

    Args:
        password: Senha em texto plano

    Returns:
        Senha criptografada
    """
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verificar se a senha está correta.

    Args:
        plain_password: <PERSON>ha em texto plano
        hashed_password: Senha criptografada

    Returns:
        True se a senha estiver correta
    """
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Criar token JWT de acesso.

    Args:
        data: Dados para incluir no token
        expires_delta: Tempo de expiração personalizado

    Returns:
        Token JWT
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """
    Verificar e decodificar token JWT.

    Args:
        token: Token JWT

    Returns:
        Dados do token se válido, None caso contrário
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def generate_password_reset_token(user_id: int) -> str:
    """
    Gerar token para reset de senha.

    Args:
        user_id: ID do usuário

    Returns:
        Token de reset
    """
    data = {"user_id": user_id, "type": "password_reset"}
    expire_delta = timedelta(hours=1)  # Token expira em 1 hora
    return create_access_token(data, expire_delta)

def verify_password_reset_token(token: str) -> Optional[int]:
    """
    Verificar token de reset de senha.

    Args:
        token: Token de reset

    Returns:
        ID do usuário se token válido, None caso contrário
    """
    payload = verify_token(token)
    if payload and payload.get("type") == "password_reset":
        return payload.get("user_id")
    return None

def encrypt_email_password(password: str) -> str:
    """
    Criptografar senha de email (simples para demonstração).
    Em produção, usar criptografia mais robusta.

    Args:
        password: Senha do email

    Returns:
        Senha criptografada
    """
    # Usando hash simples para demonstração
    # Em produção, usar criptografia reversível como AES
    return hashlib.sha256(password.encode()).hexdigest()

def decrypt_email_password(encrypted_password: str) -> str:
    """
    Descriptografar senha de email.
    NOTA: Esta implementação é apenas para demonstração.
    Em produção, implementar criptografia reversível adequada.

    Args:
        encrypted_password: Senha criptografada

    Returns:
        Senha descriptografada (não implementado adequadamente)
    """
    # Esta função precisa ser implementada com criptografia reversível
    # Por enquanto, retorna a senha criptografada
    return encrypted_password
