import os
from pydantic_settings import BaseSettings
from typing import Optional, List
from pydantic import Field

class Settings(BaseSettings):
    """
    Configurações da aplicação.

    Estas configurações podem ser definidas através de variáveis de ambiente
    ou em um arquivo .env na raiz do projeto.
    """

    # Configurações gerais
    APP_NAME: str = "Amvox Omnichannel API"
    APP_VERSION: str = "0.1.0"
    DEBUG: bool = True

    # Configurações do servidor
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # Configurações de CORS
    CORS_ORIGINS: str = "http://localhost:3000,http://localhost:3001"



    # Configurações da API de relatórios
    REPORTS_USER: str = "amvox"
    REPORTS_PASSWORD: str = "super_7894"

    # Configurações do banco de dados
    DATABASE_URL: Optional[str] = None

    # Configurações de segurança JWT
    JWT_SECRET_KEY: str = "amvox_omnichannel_secret_key_2025_super_secure_change_in_production"
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24 horas (1 dia)

    # Configurações do Microsoft Outlook/Graph API
    MICROSOFT_CLIENT_ID: Optional[str] = None
    MICROSOFT_CLIENT_SECRET: Optional[str] = None
    MICROSOFT_TENANT_ID: Optional[str] = None
    MICROSOFT_REDIRECT_URI: str = "http://localhost:3000/api/v1/outlook/callback"
    MICROSOFT_SCOPES: Optional[str] = "https://graph.microsoft.com/Mail.Read,https://graph.microsoft.com/Mail.Send,https://graph.microsoft.com/Mail.ReadWrite,https://graph.microsoft.com/Mail.ReadBasic,https://graph.microsoft.com/Mail.ReadWrite.Shared,https://graph.microsoft.com/User.Read,https://graph.microsoft.com/Contacts.Read,https://graph.microsoft.com/Directory.Read.All"

    @property
    def microsoft_scopes_list(self) -> List[str]:
        """Converte a string de scopes em uma lista."""
        if not self.MICROSOFT_SCOPES:
            return [
                "https://graph.microsoft.com/Mail.Read",
                "https://graph.microsoft.com/Mail.Send",
                "https://graph.microsoft.com/Mail.ReadWrite",
                "https://graph.microsoft.com/Mail.ReadBasic",
                "https://graph.microsoft.com/Mail.ReadWrite.Shared",
                "https://graph.microsoft.com/User.Read",
                "https://graph.microsoft.com/Contacts.Read",
                "https://graph.microsoft.com/Directory.Read.All"
            ]
        return [scope.strip() for scope in self.MICROSOFT_SCOPES.split(",") if scope.strip()]



    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "allow"  # Permitir variáveis extras

# Instância global das configurações
settings = Settings()

# Verificar configurações críticas
def validate_settings():
    """
    Valida as configurações críticas da aplicação.

    Raises:
        ValueError: Se alguma configuração crítica estiver faltando.
    """
    missing_settings = []



    # Verificar configurações da API de relatórios
    if not settings.REPORTS_USER:
        missing_settings.append("REPORTS_USER")
    if not settings.REPORTS_PASSWORD:
        missing_settings.append("REPORTS_PASSWORD")

    # Verificar configurações do banco de dados
    if not settings.DATABASE_URL:
        missing_settings.append("DATABASE_URL")

    if missing_settings:
        raise ValueError(
            f"As seguintes configurações estão faltando: {', '.join(missing_settings)}"
        )
