"""
Endpoints para geração de relatórios em PDF.
"""

import os
from typing import List

from fastapi import APIRouter, HTTPException, Response, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.schemas.reports import ReportRequest
from app.services.pdf_generator import PDFGenerator
from app.api.v1.reports import get_distribution_report_data, get_satisfaction_survey_report_data

class AgentReportRequest(BaseModel):
    agent_name: str
    start_date: str
    end_date: str
    queues: List[int]

router = APIRouter(prefix="/pdf-reports", tags=["PDF Reports"])

# Caminho para o logo - buscar dinamicamente
def get_logo_path():
    """Busca o caminho do logo dinamicamente."""
    possible_paths = [
        # Caminho no diretório assets do app
        os.path.join(os.path.dirname(__file__), "..", "..", "assets", "logo.png"),
        # Caminho no container Docker
        "/app/logo.png",
        "/app/app/assets/logo.png",
        # Caminhos locais
        os.path.join(os.getcwd(), "logo.png"),
        os.path.join(os.getcwd(), "front", "public", "logo.png"),
        os.path.join(os.path.dirname(__file__), "..", "..", "..", "logo.png"),
        os.path.join(os.path.dirname(__file__), "..", "..", "..", "front", "public", "logo.png"),
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    return None  # Retorna None se não encontrar o logo

LOGO_PATH = get_logo_path()

@router.post("/distribution")
async def get_distribution_report_pdf(request: ReportRequest) -> StreamingResponse:
    """
    Gera um relatório de distribuição em PDF.

    Args:
        request: Parâmetros para o relatório.

    Returns:
        PDF do relatório.
    """
    try:
        # Obter dados do relatório
        report_data = await get_distribution_report_data(request)

        # Gerar PDF
        pdf_generator = PDFGenerator()
        pdf_bytes = pdf_generator.generate_distribution_report(report_data, LOGO_PATH)

        # Retornar PDF como resposta
        return StreamingResponse(
            iter([pdf_bytes]),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_distribuicao_{request.start_date}_{request.end_date}.pdf"
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar relatório em PDF: {str(e)}")

@router.post("/satisfaction-survey")
async def get_satisfaction_survey_report_pdf(request: ReportRequest) -> StreamingResponse:
    """
    Gera um relatório de pesquisa de satisfação em PDF.

    Args:
        request: Parâmetros para o relatório.

    Returns:
        PDF do relatório.
    """
    try:
        # Obter dados do relatório
        report_data = await get_satisfaction_survey_report_data(request)

        # Gerar PDF
        pdf_generator = PDFGenerator()
        pdf_bytes = pdf_generator.generate_satisfaction_survey_report(report_data, LOGO_PATH)

        # Retornar PDF como resposta
        return StreamingResponse(
            iter([pdf_bytes]),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_satisfacao_{request.start_date}_{request.end_date}.pdf"
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar relatório em PDF: {str(e)}")

@router.post("/dashboard")
async def get_dashboard_report_pdf(request: ReportRequest) -> StreamingResponse:
    """
    Gera um relatório de dashboard executivo em PDF.

    Args:
        request: Parâmetros para o relatório.

    Returns:
        PDF do relatório.
    """
    try:
        # Obter dados de distribuição e satisfação
        distribution_data = await get_distribution_report_data(request)
        satisfaction_data = await get_satisfaction_survey_report_data(request)

        # Gerar PDF
        pdf_generator = PDFGenerator()
        pdf_bytes = pdf_generator.generate_dashboard_report(distribution_data, satisfaction_data, LOGO_PATH)

        # Retornar PDF como resposta
        return StreamingResponse(
            iter([pdf_bytes]),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=dashboard_executivo_{request.start_date}_{request.end_date}.pdf",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
                "Access-Control-Allow-Headers": "*"
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar relatório de dashboard em PDF: {str(e)}")

@router.post("/agent")
async def get_agent_report_pdf(request: AgentReportRequest) -> StreamingResponse:
    """
    Gera um relatório individual por agente em PDF.

    Args:
        request: Parâmetros para o relatório incluindo nome do agente.

    Returns:
        PDF do relatório.
    """
    try:
        # Criar request para os dados
        report_request = ReportRequest(
            start_date=request.start_date,
            end_date=request.end_date,
            queues=request.queues
        )

        # Obter dados de distribuição e satisfação
        distribution_data = await get_distribution_report_data(report_request)
        satisfaction_data = await get_satisfaction_survey_report_data(report_request)

        # Gerar PDF
        pdf_generator = PDFGenerator()
        pdf_bytes = pdf_generator.generate_agent_report(request.agent_name, distribution_data, satisfaction_data, LOGO_PATH)

        # Retornar PDF como resposta
        return StreamingResponse(
            iter([pdf_bytes]),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=relatorio_agente_{request.agent_name}_{request.start_date}_{request.end_date}.pdf",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
                "Access-Control-Allow-Headers": "*"
            }
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao gerar relatório de agente em PDF: {str(e)}")
