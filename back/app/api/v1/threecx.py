from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional

from app.schemas.threecx import (
    <PERSON><PERSON><PERSON>ord,
    CallRecordCreate,
    CallRecordResponse,
    AgentStatus,
    AgentStatusUpdate
)
from app.services.integrations.threecx.client import ThreeCXClient

router = APIRouter(
    prefix="/threecx",
    tags=["3CX"],
    responses={404: {"description": "Not found"}},
)

# Dependência para obter o cliente 3CX
def get_threecx_client():
    import os
    # Usar variáveis de ambiente para configuração
    base_url = os.getenv("THREECX_REPORTS_SERVER", "http://ticmobilerb.ddns.net")
    api_user = os.getenv("THREECX_API_USER", "amvox")
    api_password = os.getenv("THREECX_API_PASSWORD", "super_7894")

    return ThreeCXClient(
        base_url=base_url,
        api_key=api_password,  # Usando senha como API key
        tenant_id=api_user     # Usando usuário como tenant ID
    )

@router.get("/calls/", response_model=List[CallRecordResponse])
async def get_calls(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    agent_id: Optional[str] = None,
    client_number: Optional[str] = None,
    threecx_client: ThreeCXClient = Depends(get_threecx_client)
):
    """
    Recupera o histórico de chamadas com base nos filtros fornecidos.
    """
    try:
        calls = await threecx_client.get_calls(
            start_date=start_date,
            end_date=end_date,
            agent_id=agent_id,
            client_number=client_number
        )
        return calls
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao recuperar chamadas do 3CX: {str(e)}"
        )

@router.get("/calls/{call_id}", response_model=CallRecordResponse)
async def get_call(
    call_id: str,
    threecx_client: ThreeCXClient = Depends(get_threecx_client)
):
    """
    Recupera os detalhes de uma chamada específica pelo ID.
    """
    try:
        call = await threecx_client.get_call(call_id)
        if not call:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chamada com ID {call_id} não encontrada"
            )
        return call
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao recuperar chamada do 3CX: {str(e)}"
        )

@router.get("/agents/", response_model=List[AgentStatus])
async def get_agents_status(
    threecx_client: ThreeCXClient = Depends(get_threecx_client)
):
    """
    Recupera o status de todos os agentes.
    """
    try:
        agents = await threecx_client.get_agents_status()
        return agents
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao recuperar status dos agentes do 3CX: {str(e)}"
        )

@router.put("/agents/{agent_id}/status", response_model=AgentStatus)
async def update_agent_status(
    agent_id: str,
    status_update: AgentStatusUpdate,
    threecx_client: ThreeCXClient = Depends(get_threecx_client)
):
    """
    Atualiza o status de um agente específico.
    """
    try:
        updated_status = await threecx_client.update_agent_status(
            agent_id=agent_id,
            status=status_update.status
        )
        return updated_status
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao atualizar status do agente no 3CX: {str(e)}"
        )

@router.post("/webhook", status_code=status.HTTP_200_OK)
async def threecx_webhook(
    payload: dict,
    threecx_client: ThreeCXClient = Depends(get_threecx_client)
):
    """
    Endpoint para receber webhooks do 3CX.
    """
    try:
        # Processar o webhook recebido do 3CX
        await threecx_client.process_webhook(payload)
        return {"status": "success", "message": "Webhook processado com sucesso"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao processar webhook do 3CX: {str(e)}"
        )
