# Arquivo de inicialização do pacote api.v1

from fastapi import APIRouter
from .reports import router as reports_router
from .pdf_reports import router as pdf_reports_router
from .outlook import router as outlook_router
from .mock_data import router as mock_data_router

api_router = APIRouter()
api_router.include_router(reports_router, prefix="/reports", tags=["reports"])
api_router.include_router(pdf_reports_router, prefix="/pdf-reports", tags=["pdf-reports"])
api_router.include_router(outlook_router, prefix="/outlook", tags=["outlook"])
api_router.include_router(mock_data_router, prefix="/mock", tags=["mock"])
