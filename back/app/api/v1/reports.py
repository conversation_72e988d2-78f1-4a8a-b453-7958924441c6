from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List
import logging
from datetime import datetime

from app.schemas.reports import (
    DistributionReportRequest,
    SatisfactionSurveyReportRequest,
    ReportResponse,
    ReportRequest,
    TimingMetricsRequest,
    AgentPerformanceRequest,
    SLAMetricsRequest,
    TimingMetrics,
    AgentPerformance,
    QueueMetrics,
    SLAMetrics
)
from app.services.integrations.reports.client import ReportsClient
from app.services.metrics_calculator import MetricsCalculator
from app.services.mock.advanced_metrics_mock import advanced_metrics_mock

logger = logging.getLogger(__name__)

# Instanciar calculadora de métricas
metrics_calculator = MetricsCalculator()

router = APIRouter(
    prefix="/reports",
    tags=["Relatórios"],
    responses={404: {"description": "Not found"}},
)

# Dependência para obter o cliente de relatórios
def get_reports_client():
    return ReportsClient()

async def get_distribution_report_data(request: ReportRequest) -> Dict[str, Any]:
    """
    Obtém os dados do relatório de distribuição.

    Args:
        request: Parâmetros para o relatório.

    Returns:
        Dados do relatório.
    """
    reports_client = ReportsClient()
    return await reports_client.get_distribution_report(
        start_date=request.start_date,
        end_date=request.end_date,
        queues=request.queues
    )

@router.post("/distribution", response_model=ReportResponse)
async def get_distribution_report(
    request: DistributionReportRequest,
    reports_client: ReportsClient = Depends(get_reports_client)
):
    """
    Obtém o relatório de distribuição.

    Este endpoint faz uma requisição para a API externa de relatórios
    e retorna os dados do relatório de distribuição.
    """
    try:
        report_data = await get_distribution_report_data(request)
        return {"data": report_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao obter relatório de distribuição: {str(e)}"
        )

async def get_satisfaction_survey_report_data(request: ReportRequest) -> Dict[str, Any]:
    """
    Obtém os dados do relatório de pesquisa de satisfação.

    Args:
        request: Parâmetros para o relatório.

    Returns:
        Dados do relatório.
    """
    reports_client = ReportsClient()
    return await reports_client.get_satisfaction_survey_report(
        start_date=request.start_date,
        end_date=request.end_date,
        queues=request.queues
    )

@router.post("/satisfaction-survey", response_model=ReportResponse)
async def get_satisfaction_survey_report(
    request: SatisfactionSurveyReportRequest,
    reports_client: ReportsClient = Depends(get_reports_client)
):
    """
    Obtém o relatório de pesquisa de satisfação.

    Este endpoint faz uma requisição para a API externa de relatórios
    e retorna os dados do relatório de pesquisa de satisfação.
    """
    try:
        report_data = await get_satisfaction_survey_report_data(request)
        return {"data": report_data}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao obter relatório de pesquisa de satisfação: {str(e)}"
        )

# ==================== NOVOS ENDPOINTS DE MÉTRICAS AVANÇADAS ====================

@router.post("/metrics/timing", response_model=TimingMetrics)
async def get_timing_metrics(
    request: TimingMetricsRequest,
    reports_client: ReportsClient = Depends(get_reports_client)
):
    """
    Obtém métricas de tempo de atendimento (ASA, AHT, Service Level, etc.).

    Este endpoint calcula métricas essenciais de call center:
    - ASA (Average Speed of Answer)
    - AHT (Average Handle Time)
    - Service Level
    - Abandonment Rate
    """
    try:
        logger.info(f"Calculando métricas de timing para período {request.start_date} a {request.end_date}")

        # Gerar dados mock realistas para desenvolvimento
        start_datetime = datetime.combine(request.start_date, datetime.min.time())
        end_datetime = datetime.combine(request.end_date, datetime.max.time())

        calls_data = advanced_metrics_mock.generate_realistic_calls_data(
            start_datetime, end_datetime, request.queue_ids
        )

        # Calcular métricas usando o calculador
        timing_metrics = metrics_calculator.calculate_timing_metrics(
            calls_data, request.service_level_threshold
        )

        logger.info(f"Métricas calculadas: ASA={timing_metrics.asa}s, AHT={timing_metrics.aht}s")
        return timing_metrics

    except Exception as e:
        logger.error(f"Erro ao calcular métricas de timing: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao calcular métricas de timing: {str(e)}"
        )

@router.post("/metrics/agents", response_model=List[AgentPerformance])
async def get_agent_performance_metrics(
    request: AgentPerformanceRequest,
    reports_client: ReportsClient = Depends(get_reports_client)
):
    """
    Obtém métricas de performance dos agentes.

    Retorna métricas detalhadas por agente:
    - Produtividade
    - Tempo de trabalho
    - Calls per hour
    - Occupancy rate
    - First Call Resolution
    """
    try:
        logger.info(f"Calculando performance de agentes para período {request.start_date} a {request.end_date}")

        start_datetime = datetime.combine(request.start_date, datetime.min.time())
        end_datetime = datetime.combine(request.end_date, datetime.max.time())

        # Gerar dados de atividade dos agentes
        agent_activity = advanced_metrics_mock.generate_agent_activity_data(
            start_datetime, end_datetime, request.agent_ids
        )

        # Gerar dados de chamadas
        calls_data = advanced_metrics_mock.generate_realistic_calls_data(
            start_datetime, end_datetime
        )

        agent_performances = []

        for agent_id, activity in agent_activity.items():
            # Filtrar chamadas do agente
            agent_calls = [call for call in calls_data if call.get('agent_id') == agent_id]

            # Calcular performance do agente
            performance = metrics_calculator.calculate_agent_performance(
                agent_id, activity['agent_name'], agent_calls, activity
            )
            agent_performances.append(performance)

        logger.info(f"Performance calculada para {len(agent_performances)} agentes")
        return agent_performances

    except Exception as e:
        logger.error(f"Erro ao calcular performance de agentes: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao calcular performance de agentes: {str(e)}"
        )

@router.post("/metrics/sla", response_model=SLAMetrics)
async def get_sla_metrics(
    request: SLAMetricsRequest,
    reports_client: ReportsClient = Depends(get_reports_client)
):
    """
    Obtém métricas de SLA e compliance.

    Calcula métricas de conformidade com SLA:
    - Service Level achieved
    - SLA compliance
    - Calls within/outside SLA
    - Best/worst performance hours
    """
    try:
        logger.info(f"Calculando métricas de SLA para período {request.start_date} a {request.end_date}")

        start_datetime = datetime.combine(request.start_date, datetime.min.time())
        end_datetime = datetime.combine(request.end_date, datetime.max.time())

        calls_data = advanced_metrics_mock.generate_realistic_calls_data(
            start_datetime, end_datetime, request.queue_ids
        )

        # Calcular métricas de SLA
        sla_metrics = metrics_calculator.calculate_sla_metrics(
            calls_data, request.service_level_threshold
        )

        logger.info(f"SLA calculado: {sla_metrics.service_level_achieved}% de service level")
        return sla_metrics

    except Exception as e:
        logger.error(f"Erro ao calcular métricas de SLA: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao calcular métricas de SLA: {str(e)}"
        )

@router.post("/metrics/queues", response_model=List[QueueMetrics])
async def get_queue_metrics(
    request: TimingMetricsRequest,
    reports_client: ReportsClient = Depends(get_reports_client)
):
    """
    Obtém métricas detalhadas por fila.

    Retorna análise por fila/departamento:
    - Volume de chamadas
    - Performance por fila
    - Picos de tráfego
    - Service level por fila
    """
    try:
        logger.info(f"Calculando métricas por fila para período {request.start_date} a {request.end_date}")

        start_datetime = datetime.combine(request.start_date, datetime.min.time())
        end_datetime = datetime.combine(request.end_date, datetime.max.time())

        calls_data = advanced_metrics_mock.generate_realistic_calls_data(
            start_datetime, end_datetime, request.queue_ids
        )

        # Agrupar chamadas por fila
        queues_data = {}
        for call in calls_data:
            queue_id = call['queue_id']
            if queue_id not in queues_data:
                queues_data[queue_id] = {
                    'queue_name': call['queue_name'],
                    'calls': []
                }
            queues_data[queue_id]['calls'].append(call)

        queue_metrics_list = []
        for queue_id, data in queues_data.items():
            metrics = metrics_calculator.calculate_queue_metrics(
                queue_id, data['queue_name'], data['calls'], request.service_level_threshold
            )
            queue_metrics_list.append(metrics)

        logger.info(f"Métricas calculadas para {len(queue_metrics_list)} filas")
        return queue_metrics_list

    except Exception as e:
        logger.error(f"Erro ao calcular métricas por fila: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erro ao calcular métricas por fila: {str(e)}"
        )
