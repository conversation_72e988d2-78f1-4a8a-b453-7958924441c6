"""
Endpoints da API para integração com Microsoft Outlook.
"""

import logging
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends, Request
from fastapi.responses import RedirectResponse, HTMLResponse
from sqlalchemy.orm import Session

from app.services.integrations.outlook import (
    OutlookAuthService,
    OutlookGraphClient,
    OutlookSyncService,
    OutlookTokens,
    OutlookUser,
    EmailMessage,
    EmailFolder,
    OutlookIntegrationStatus,
    SendEmailRequest,
    EmailSearchRequest,
    EmailSearchResponse,
    OutlookConfig,
    SyncResult
)
from app.services.integrations.outlook.user_tokens import user_outlook_service
from app.services.integrations.outlook.token_manager import outlook_token_manager
from app.api.v1.auth import get_current_user
from app.schemas.usuario import UsuarioResponse
from app.crud.usuario import usuario_crud
from app.crud.configuracao import (
    get_configuracoes_microsoft_graph,
    set_configuracoes_microsoft_graph,
    remove_configuracoes_microsoft_graph,
    get_valor_configuracao
)
from app.database.connection import get_db
from app.utils.security import encrypt_email_password
from app.database.connection import get_db
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/outlook", tags=["Outlook Integration"])

# Instâncias dos serviços
auth_service = OutlookAuthService()
# user_outlook_service já foi importado da linha 27
sync_service = OutlookSyncService()

# Log das configurações na inicialização
from app.config.settings import settings
logger.info(f"Microsoft Graph configurado: {auth_service.is_configured}")
if auth_service.is_configured:
    logger.info(f"Client ID: {settings.MICROSOFT_CLIENT_ID}")
    logger.info(f"Tenant ID: {settings.MICROSOFT_TENANT_ID}")
    logger.info(f"Redirect URI: {settings.MICROSOFT_REDIRECT_URI}")
else:
    logger.warning("Microsoft Graph não configurado - verificar variáveis de ambiente")

# Armazenamento temporário de tokens (em produção, usar banco de dados)
_user_tokens: dict = {}
_user_configs: dict = {}
# Mapeamento entre Outlook User ID (UUID) e System User ID (int)
_outlook_user_mapping: dict = {}


# Removido: OutlookCredentialsRequest e OutlookCredentialsResponse
# Não são mais necessários com OAuth2


class GraphConfigRequest(BaseModel):
    """Modelo para requisição de configuração do Microsoft Graph."""
    client_id: str
    client_secret: str
    tenant_id: str


class GraphConfigResponse(BaseModel):
    """Modelo para resposta de configuração do Microsoft Graph."""
    success: bool
    message: str
    is_configured: bool = False


@router.get("/auth/login")
async def login(force_account_selection: bool = Query(False, description="Forçar seleção de conta")):
    """
    Inicia o processo de autenticação OAuth2 com Microsoft.

    Args:
        force_account_selection: Se True, força a seleção de conta

    Returns:
        URL de redirecionamento para autenticação
    """
    try:
        if not auth_service.is_configured:
            raise HTTPException(
                status_code=503,
                detail="Integração Outlook não configurada. Configure as credenciais do Microsoft Azure primeiro."
            )

        logger.info(f"Parâmetro force_account_selection recebido: {force_account_selection} (tipo: {type(force_account_selection)})")

        # Forçar True para teste
        if force_account_selection or str(force_account_selection).lower() in ['true', '1', 'yes']:
            logger.info("Forçando seleção de conta...")
            auth_url = auth_service.get_authorization_url(force_account_selection=True)
        else:
            logger.info("Usando autenticação normal...")
            auth_url = auth_service.get_authorization_url(force_account_selection=False)
        logger.info(f"URL de autorização gerada: {auth_url}")
        return {"auth_url": auth_url}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao iniciar autenticação: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test")
async def test_endpoint():
    """Endpoint de teste."""
    print("🔍 PRINT - Test endpoint executado!")
    logger.info("🔍 LOG - Test endpoint executado!")
    return {"message": "Endpoint funcionando", "timestamp": datetime.now().isoformat()}

@router.get("/test-messages")
async def test_messages_endpoint():
    """Endpoint de teste para mensagens."""
    print("🔍 PRINT - Test messages endpoint executado!")
    logger.info("🔍 LOG - Test messages endpoint executado!")
    return {"message": "Test messages funcionando", "timestamp": datetime.now().isoformat()}


@router.post("/connect")
async def connect_user_account(
    current_user: UsuarioResponse = Depends(get_current_user)
):
    """
    Conecta a conta Outlook do usuário atual.
    Gera URL de autenticação personalizada para o usuário.

    Returns:
        URL de autenticação
    """
    try:
        if not auth_service.is_configured:
            raise HTTPException(
                status_code=503,
                detail="Integração Outlook não configurada. Configure as credenciais do Microsoft Azure primeiro."
            )

        logger.info(f"Gerando URL de autenticação para usuário: {current_user.login}")

        # Gerar URL com state personalizado para identificar o usuário
        state = f"user_{current_user.id}"
        auth_url = auth_service.get_authorization_url(
            force_account_selection=True,
            state=state
        )

        logger.info(f"URL de autenticação gerada para {current_user.login}: {auth_url}")

        return {
            "auth_url": auth_url,
            "user_id": current_user.id,
            "message": f"URL de autenticação gerada para {current_user.nome}"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao gerar URL de autenticação: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/auth/disconnect")
async def disconnect_account(
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Desconecta a conta Outlook atual do usuário.

    Returns:
        Status da operação
    """
    try:
        # Usar o novo gerenciador de tokens
        success = outlook_token_manager.revoke_tokens(db, current_user.id)

        if success:
            logger.info(f"Conta Outlook desconectada para usuário {current_user.login}")
            return {
                "success": True,
                "message": "Conta Outlook desconectada com sucesso"
            }
        else:
            return {
                "success": False,
                "message": "Nenhuma conta Outlook estava conectada"
            }

    except Exception as e:
        logger.error(f"Erro ao desconectar conta: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/status")
async def get_user_status(
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Verifica o status de autenticação do usuário atual no Outlook.
    Usa o novo sistema de persistência de tokens.

    Returns:
        Status de autenticação e informações do usuário
    """
    try:
        logger.info(f"Verificando status Outlook para usuário: {current_user.login}")

        # Usar o novo gerenciador de tokens
        status = outlook_token_manager.get_connection_status(db, current_user.id)

        if status["authenticated"]:
            logger.info(f"Usuário {current_user.login} autenticado no Outlook: {status['user_info']['email']}")

            return {
                "authenticated": True,
                "user_id": current_user.id,
                "user_info": status["user_info"],
                "status": "connected",
                "expires_at": status["expires_at"],
                "expires_soon": status["expires_soon"],
                "last_used": status["last_used"],
                "system_user": {
                    "id": current_user.id,
                    "login": current_user.login,
                    "nome": current_user.nome,
                    "email_corporativo": current_user.email_corporativo
                }
            }
        else:
            logger.info(f"Usuário {current_user.login} não autenticado no Outlook")

            return {
                "authenticated": False,
                "user_id": current_user.id,
                "user_info": None,
                "status": "disconnected",
                "system_user": {
                    "id": current_user.id,
                    "login": current_user.login,
                    "nome": current_user.nome,
                    "email_corporativo": current_user.email_corporativo
                },
                "message": "Clique em 'Conectar Outlook' para autenticar"
            }

    except Exception as e:
        logger.error(f"Erro ao verificar status do usuário: {e}")
        return {
            "authenticated": False,
            "user_id": current_user.id,
            "user_info": None,
            "status": "error",
            "error": str(e),
            "system_user": {
                "id": current_user.id,
                "login": current_user.login,
                "nome": current_user.nome
            }
        }


async def attempt_auto_authentication(current_user: UsuarioResponse) -> Optional[OutlookUser]:
    """
    Tenta autenticação automática usando as credenciais do usuário do sistema.

    Args:
        current_user: Usuário do sistema

    Returns:
        Informações do usuário Outlook se bem-sucedido, None caso contrário
    """
    try:
        # Por enquanto, retornar None pois OAuth2 requer interação do usuário
        # Em uma implementação futura, poderia usar credenciais armazenadas
        # ou tokens de refresh válidos
        logger.info(f"Autenticação automática não disponível para OAuth2 - usuário: {current_user.login}")
        return None

    except Exception as e:
        logger.error(f"Erro na tentativa de autenticação automática: {e}")
        return None





@router.get("/auth/callback")
async def auth_callback(
    code: str = Query(..., description="Código de autorização"),
    state: Optional[str] = Query(None, description="Estado da requisição"),
    error: Optional[str] = Query(None, description="Erro de autenticação"),
    db: Session = Depends(get_db)
):
    """
    Callback de autenticação OAuth2.

    Args:
        code: Código de autorização
        state: Estado da requisição
        error: Erro de autenticação
        db: Sessão do banco de dados

    Returns:
        Resultado da autenticação
    """
    if error:
        logger.error(f"Erro na autenticação: {error}")
        raise HTTPException(status_code=400, detail=f"Erro de autenticação: {error}")

    try:
        # Verificar se há um user_id no state
        user_id = None
        if state and state.startswith("user_"):
            try:
                user_id = int(state.replace("user_", ""))
            except ValueError:
                logger.warning(f"State inválido: {state}")

        # Se não há user_id no state, usar um padrão (usuário admin)
        if not user_id:
            user_id = 1  # ID do usuário admin padrão
            logger.info(f"Usando user_id padrão: {user_id}")

        # Trocar código por tokens usando o serviço de autenticação
        auth_service = OutlookAuthService()
        tokens = auth_service.exchange_code_for_tokens(code)

        # Obter informações do usuário
        from app.services.integrations.outlook.client import OutlookGraphClient
        client = OutlookGraphClient(tokens)
        outlook_user = await client.get_user_info()

        # Validar domínio do email
        allowed_domains = ["amvox.com.br", "amvoxdev.onmicrosoft.com"]
        email_domain = outlook_user.email.split("@")[-1].lower()
        if email_domain not in allowed_domains:
            raise ValueError(f"Email {outlook_user.email} não pertence ao domínio permitido (@amvox.com.br)")

        # Armazenar tokens no banco usando o novo gerenciador
        success = outlook_token_manager.store_tokens(db, user_id, tokens, outlook_user)

        if not success:
            raise HTTPException(status_code=500, detail="Erro ao armazenar tokens")

        # Redirecionar diretamente para a aba do Outlook no atendimento
        redirect_url = f"http://localhost:3000/atendimento?tab=email&outlook_auth=success&user_email={outlook_user.email}"

        logger.info(f"Usuário autenticado com sucesso: {outlook_user.email}")

        # Retornar página HTML com JavaScript para salvar no localStorage e redirecionar
        html_content = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Autenticação Realizada - Amvox Omnichannel</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }}
                .container {{
                    background: white;
                    padding: 2rem;
                    border-radius: 10px;
                    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                    text-align: center;
                    max-width: 400px;
                    width: 90%;
                }}
                .success-icon {{
                    width: 60px;
                    height: 60px;
                    background: #10B981;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto 1rem;
                }}
                .checkmark {{
                    width: 30px;
                    height: 30px;
                    stroke: white;
                    stroke-width: 3;
                    fill: none;
                }}
                h1 {{
                    color: #1F2937;
                    margin-bottom: 0.5rem;
                    font-size: 1.5rem;
                }}
                .user-info {{
                    background: #F3F4F6;
                    padding: 1rem;
                    border-radius: 8px;
                    margin: 1rem 0;
                    text-align: left;
                }}
                .user-info p {{
                    margin: 0.25rem 0;
                    color: #374151;
                    font-size: 0.9rem;
                }}
                .loading {{
                    color: #6B7280;
                    font-size: 0.9rem;
                    margin-top: 1rem;
                }}
                .spinner {{
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #3498db;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-right: 0.5rem;
                }}
                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="success-icon">
                    <svg class="checkmark" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4"/>
                    </svg>
                </div>
                <h1>Autenticação Realizada!</h1>
                <p>Sua conta Microsoft foi conectada com sucesso.</p>

                <div class="user-info">
                    <p><strong>Usuário:</strong> {outlook_user.display_name}</p>
                    <p><strong>Email:</strong> {outlook_user.email}</p>
                </div>

                <div class="loading">
                    <span class="spinner"></span>
                    Redirecionando para o sistema...
                </div>
            </div>

            <script>
                // Salvar informações no localStorage
                localStorage.setItem('outlook_user_email', '{outlook_user.email}');
                localStorage.setItem('outlook_user_name', '{outlook_user.display_name}');
                localStorage.setItem('outlook_authenticated', 'true');

                console.log('Dados salvos no localStorage:', {{
                    email: '{outlook_user.email}',
                    name: '{outlook_user.display_name}',
                    authenticated: true
                }});

                // Redirecionar imediatamente
                setTimeout(function() {{
                    window.location.href = '{redirect_url}';
                }}, 1000);

                // Permitir redirecionamento manual
                document.addEventListener('click', function() {{
                    window.location.href = '{redirect_url}';
                }});
            </script>
        </body>
        </html>
        """

        return HTMLResponse(content=html_content, status_code=200)

    except Exception as e:
        logger.error(f"Erro no callback de autenticação: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config/status")
async def get_config_status():
    """
    Verifica se a integração Outlook está configurada.

    Returns:
        Status da configuração
    """
    from app.config.settings import settings

    return {
        "is_configured": auth_service.is_configured,
        "message": "Integração configurada" if auth_service.is_configured else
                  "Configure as credenciais do Microsoft Azure para habilitar a integração",
        "required_env_vars": [
            "MICROSOFT_CLIENT_ID",
            "MICROSOFT_CLIENT_SECRET",
            "MICROSOFT_TENANT_ID"
        ],
        "redirect_uri": settings.MICROSOFT_REDIRECT_URI,
        "auth_service_redirect_uri": getattr(auth_service, 'redirect_uri', 'N/A'),
        "client_id": settings.MICROSOFT_CLIENT_ID[:10] + "..." if settings.MICROSOFT_CLIENT_ID else None,
        "tenant_id": settings.MICROSOFT_TENANT_ID[:10] + "..." if settings.MICROSOFT_TENANT_ID else None
    }


@router.post("/config/reload")
async def reload_config(
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Força o recarregamento das configurações do banco de dados.

    Returns:
        Status da operação
    """
    try:
        # Verificar se o usuário é administrador
        if current_user.nivel_usuario != "administrador":
            raise HTTPException(
                status_code=403,
                detail="Apenas administradores podem recarregar configurações"
            )

        # Carregar configurações do banco
        configs = get_configuracoes_microsoft_graph(db)

        client_id = configs.get("microsoft_graph_client_id")
        client_secret = configs.get("microsoft_graph_client_secret")
        tenant_id = configs.get("microsoft_graph_tenant_id")

        if all([client_id, client_secret, tenant_id]):
            # Atualizar settings
            from app.config.settings import settings
            settings.MICROSOFT_CLIENT_ID = client_id
            settings.MICROSOFT_CLIENT_SECRET = client_secret
            settings.MICROSOFT_TENANT_ID = tenant_id
            settings.MICROSOFT_REDIRECT_URI = "http://localhost:3000/api/v1/outlook/callback"

            # Reinicializar serviço
            global auth_service
            auth_service = OutlookAuthService()

            logger.info(f"Configurações recarregadas por {current_user.login}")
            logger.info(f"Redirect URI atualizado para: {settings.MICROSOFT_REDIRECT_URI}")

            return {
                "success": True,
                "message": "Configurações recarregadas com sucesso",
                "redirect_uri": settings.MICROSOFT_REDIRECT_URI,
                "is_configured": auth_service.is_configured
            }
        else:
            return {
                "success": False,
                "message": "Configurações incompletas no banco de dados"
            }

    except Exception as e:
        logger.error(f"Erro ao recarregar configurações: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def _get_system_user_id(user_id_param: str) -> int:
    """
    Converte user_id (que pode ser UUID do Outlook ou ID do sistema) para ID do sistema.

    Args:
        user_id_param: ID do usuário (UUID ou int)

    Returns:
        ID do sistema (int)
    """
    # Se é um UUID do Outlook, mapear para ID do sistema
    if user_id_param in _outlook_user_mapping:
        return _outlook_user_mapping[user_id_param]

    # Se é um número, tentar converter para int
    try:
        return int(user_id_param)
    except ValueError:
        # Se não conseguir converter, assumir que é UUID não mapeado
        raise HTTPException(status_code=401, detail="Usuário não autenticado")


@router.get("/status")
async def get_integration_status(
    user_id: str = Query(..., description="ID do usuário")
) -> OutlookIntegrationStatus:
    """
    Obtém status da integração para um usuário.

    Args:
        user_id: ID do usuário

    Returns:
        Status da integração
    """
    try:
        # Converter para ID do sistema
        system_user_id = _get_system_user_id(user_id)

        # Verificar se o usuário tem tokens armazenados
        tokens = user_outlook_service.get_user_tokens(system_user_id)
        outlook_user = user_outlook_service.get_user_outlook_info(system_user_id)

        if not tokens or not outlook_user:
            return OutlookIntegrationStatus(
                is_connected=False,
                error_message="Usuário não autenticado"
            )

        # Retornar status com informações do usuário
        return OutlookIntegrationStatus(
            is_connected=True,
            user_info=outlook_user,
            last_sync=None,  # TODO: implementar tracking de sync
            sync_status="idle",
            folders_count=0,  # TODO: implementar contagem
            messages_count=0  # TODO: implementar contagem
        )

    except Exception as e:
        logger.error(f"Erro ao obter status: {e}")
        return OutlookIntegrationStatus(
            is_connected=False,
            error_message=str(e)
        )


@router.get("/user")
async def get_user_info(
    user_id: str = Query(..., description="ID do usuário")
) -> OutlookUser:
    """
    Obtém informações do usuário autenticado.

    Args:
        user_id: ID do usuário

    Returns:
        Informações do usuário
    """
    try:
        tokens = _user_tokens.get(user_id)
        if not tokens:
            raise HTTPException(status_code=401, detail="Usuário não autenticado")

        client = OutlookGraphClient(tokens)
        user_info = await client.get_user_info()
        return user_info

    except Exception as e:
        logger.error(f"Erro ao obter informações do usuário: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/folders")
async def get_folders(
    user_id: str = Query(..., description="ID do usuário"),
    db: Session = Depends(get_db)
) -> List[EmailFolder]:
    """
    Obtém pastas de email do usuário.

    Args:
        user_id: ID do usuário
        db: Sessão do banco de dados

    Returns:
        Lista de pastas
    """
    try:
        # Converter para ID do sistema
        system_user_id = _get_system_user_id(user_id)

        # Obter tokens válidos usando o novo gerenciador
        tokens_result = outlook_token_manager.get_valid_tokens(db, system_user_id)
        if not tokens_result:
            raise HTTPException(status_code=401, detail="Usuário não autenticado")

        tokens, user_info = tokens_result
        client = OutlookGraphClient(tokens)
        folders = await client.get_folders()
        return folders

    except Exception as e:
        logger.error(f"Erro ao obter pastas: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/messages")
async def get_messages(
    user_id: str = Query(..., description="ID do usuário"),
    folder_id: str = Query("inbox", description="ID da pasta"),
    limit: int = Query(50, description="Limite de mensagens"),
    db: Session = Depends(get_db)
) -> List[dict]:
    """
    Obtém mensagens de uma pasta.

    Args:
        user_id: ID do usuário
        folder_id: ID da pasta
        limit: Limite de mensagens
        db: Sessão do banco de dados

    Returns:
        Lista de mensagens
    """
    try:
        print(f"🔍 PRINT - Iniciando get_messages para user_id={user_id}, folder_id={folder_id}, limit={limit}")
        logger.info(f"🔍 ENDPOINT - Iniciando get_messages para user_id={user_id}, folder_id={folder_id}, limit={limit}")

        # Converter para ID do sistema
        system_user_id = _get_system_user_id(user_id)
        logger.info(f"🔍 ENDPOINT - system_user_id={system_user_id}")

        # Obter tokens válidos usando o novo gerenciador
        tokens_result = outlook_token_manager.get_valid_tokens(db, system_user_id)
        if not tokens_result:
            logger.warning(f"🔍 ENDPOINT - Tokens não encontrados para user_id={system_user_id}")
            raise HTTPException(status_code=401, detail="Usuário não autenticado")

        tokens, user_info = tokens_result
        logger.info(f"🔍 ENDPOINT - Tokens obtidos, criando cliente...")

        client = OutlookGraphClient(tokens)
        logger.info(f"🔍 ENDPOINT - Cliente criado, chamando get_messages...")

        messages = await client.get_messages(folder_id, limit)
        logger.info(f"🔍 ENDPOINT - get_messages retornou {len(messages)} mensagens")

        # Converter para dict para garantir serialização correta
        result = []
        debug_info = []

        # Obter dados brutos da API para usar como fallback
        raw_messages = await outlook_client.get_messages_raw(folder_id, limit)

        for i, message in enumerate(messages):
            message_dict = message.dict(by_alias=True)

            # CORREÇÃO PRINCIPAL: Se o campo 'from' estiver None, usar dados brutos da API
            if not message_dict.get('from') and i < len(raw_messages):
                raw_message = raw_messages[i]
                if raw_message.get('from'):
                    logger.info(f"🔧 API-FIX - Usando dados brutos para corrigir campo 'from'")
                    message_dict['from'] = raw_message['from']
                elif raw_message.get('sender'):
                    logger.info(f"🔧 API-FIX - Usando dados brutos do sender para corrigir campo 'from'")
                    message_dict['from'] = raw_message['sender']

            # Debug: Adicionar informações de debug diretamente no response
            debug_entry = {
                "message_id": message.id[:20] + "...",
                "from_recipient": str(message.from_),
                "serialized_from": message_dict.get('from'),
                "has_from": message.from_ is not None,
                "from_type": type(message.from_).__name__
            }
            debug_info.append(debug_entry)

            result.append(message_dict)

        # Para debug, adicionar informações nos logs
        if limit == 1 and debug_info:
            logger.info(f"🎉 DEBUG INFO: {debug_info[0]}")
            logger.info(f"🎉 SCOPES APLICADOS: Mail.Read,Mail.Send,Mail.ReadWrite,Mail.ReadBasic,Mail.ReadWrite.Shared,User.Read,Contacts.Read,Directory.Read.All")

        return result

    except Exception as e:
        logger.error(f"Erro ao obter mensagens: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/send")
async def send_email(
    email_request: SendEmailRequest,
    user_id: str = Query(..., description="ID do usuário"),
    db: Session = Depends(get_db)
) -> dict:
    """
    Envia um email.

    Args:
        email_request: Dados do email
        user_id: ID do usuário
        db: Sessão do banco de dados

    Returns:
        Resultado do envio
    """
    try:
        # Converter para ID do sistema
        system_user_id = _get_system_user_id(user_id)

        # Obter tokens válidos usando o novo gerenciador
        tokens_result = outlook_token_manager.get_valid_tokens(db, system_user_id)
        if not tokens_result:
            raise HTTPException(status_code=401, detail="Usuário não autenticado")

        tokens, user_info = tokens_result
        client = OutlookGraphClient(tokens)
        message_id = await client.send_email(email_request)

        return {
            "success": True,
            "message_id": message_id,
            "message": "Email enviado com sucesso"
        }

    except Exception as e:
        logger.error(f"Erro ao enviar email: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search")
async def search_messages(
    search_request: EmailSearchRequest,
    user_id: str = Query(..., description="ID do usuário")
) -> EmailSearchResponse:
    """
    Busca mensagens com filtros.

    Args:
        search_request: Parâmetros de busca
        user_id: ID do usuário

    Returns:
        Resultado da busca
    """
    try:
        tokens = _user_tokens.get(user_id)
        if not tokens:
            raise HTTPException(status_code=401, detail="Usuário não autenticado")

        client = OutlookGraphClient(tokens)
        results = await client.search_messages(search_request)
        return results

    except Exception as e:
        logger.error(f"Erro ao buscar mensagens: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync")
async def sync_data(
    user_id: str = Query(..., description="ID do usuário")
) -> SyncResult:
    """
    Inicia sincronização manual dos dados.

    Args:
        user_id: ID do usuário

    Returns:
        Resultado da sincronização
    """
    try:
        tokens = _user_tokens.get(user_id)
        config = _user_configs.get(user_id)

        if not tokens or not config:
            raise HTTPException(status_code=401, detail="Usuário não autenticado")

        result = await sync_service.sync_user_data(tokens, config)
        return result

    except Exception as e:
        logger.error(f"Erro na sincronização: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/disconnect")
async def disconnect_user(
    user_id: str = Query(..., description="ID do usuário")
) -> dict:
    """
    Desconecta usuário da integração.

    Args:
        user_id: ID do usuário

    Returns:
        Resultado da desconexão
    """
    try:
        tokens = _user_tokens.get(user_id)
        if tokens:
            auth_service.revoke_tokens(tokens)
            del _user_tokens[user_id]

        if user_id in _user_configs:
            del _user_configs[user_id]

        return {
            "success": True,
            "message": "Usuário desconectado com sucesso"
        }

    except Exception as e:
        logger.error(f"Erro ao desconectar usuário: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Removido: endpoint /credentials POST
# Não é mais necessário com OAuth2


# Removido: endpoint /credentials GET
# Não é mais necessário com OAuth2


# Removido: endpoint /credentials DELETE
# Não é mais necessário com OAuth2


@router.post("/graph-config")
async def save_graph_config(
    config: GraphConfigRequest,
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> GraphConfigResponse:
    """
    Salva a configuração do Microsoft Graph API.

    Args:
        config: Configuração do Microsoft Graph
        current_user: Usuário atual (deve ser administrador)

    Returns:
        Resultado da operação
    """
    try:
        # Verificar se o usuário é administrador
        if current_user.nivel_usuario != "administrador":
            raise HTTPException(
                status_code=403,
                detail="Apenas administradores podem configurar o Microsoft Graph"
            )

        # Validar formato UUID
        import re
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'

        if not re.match(uuid_pattern, config.client_id, re.IGNORECASE):
            raise HTTPException(
                status_code=400,
                detail="Client ID deve estar no formato UUID válido"
            )

        if not re.match(uuid_pattern, config.tenant_id, re.IGNORECASE):
            raise HTTPException(
                status_code=400,
                detail="Tenant ID deve estar no formato UUID válido"
            )

        # Salvar configuração no banco de dados
        set_configuracoes_microsoft_graph(
            db,
            config.client_id,
            config.client_secret,
            config.tenant_id
        )

        # Atualizar configurações em memória para uso imediato
        from app.config.settings import settings
        settings.MICROSOFT_CLIENT_ID = config.client_id
        settings.MICROSOFT_CLIENT_SECRET = config.client_secret
        settings.MICROSOFT_TENANT_ID = config.tenant_id
        # Garantir que o redirect URI está correto
        settings.MICROSOFT_REDIRECT_URI = "http://localhost:3000/api/v1/outlook/callback"

        # Reinicializar o serviço de autenticação
        global auth_service
        auth_service = OutlookAuthService()

        logger.info(f"Serviço reinicializado com redirect URI: {settings.MICROSOFT_REDIRECT_URI}")

        logger.info(f"Configuração Microsoft Graph salva no banco por {current_user.login}")

        return GraphConfigResponse(
            success=True,
            message="Configuração do Microsoft Graph salva com sucesso!",
            is_configured=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao salvar configuração Microsoft Graph: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/graph-config")
async def get_graph_config_status(
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> GraphConfigResponse:
    """
    Verifica se o Microsoft Graph API está configurado.

    Args:
        current_user: Usuário atual

    Returns:
        Status da configuração
    """
    try:
        # Verificar configuração no banco de dados
        configs = get_configuracoes_microsoft_graph(db)

        client_id = configs.get("microsoft_graph_client_id")
        client_secret = configs.get("microsoft_graph_client_secret")
        tenant_id = configs.get("microsoft_graph_tenant_id")

        is_configured = all([
            client_id,
            client_secret,
            tenant_id,
            client_id != "your_client_id_here",
            client_secret != "your_client_secret_here",
            tenant_id != "your_tenant_id_here"
        ])

        # Se configurado no banco, atualizar settings em memória
        if is_configured:
            from app.config.settings import settings
            settings.MICROSOFT_CLIENT_ID = client_id
            settings.MICROSOFT_CLIENT_SECRET = client_secret
            settings.MICROSOFT_TENANT_ID = tenant_id
            # Garantir que o redirect URI está correto
            settings.MICROSOFT_REDIRECT_URI = "http://localhost:3000/api/v1/outlook/callback"

            # Reinicializar o serviço de autenticação se necessário
            global auth_service
            if not auth_service.is_configured:
                auth_service = OutlookAuthService()
                logger.info(f"Serviço reinicializado com redirect URI: {settings.MICROSOFT_REDIRECT_URI}")

        return GraphConfigResponse(
            success=True,
            message="Microsoft Graph configurado" if is_configured else "Microsoft Graph não configurado",
            is_configured=is_configured
        )

    except Exception as e:
        logger.error(f"Erro ao verificar configuração Microsoft Graph: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/graph-config")
async def remove_graph_config(
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> GraphConfigResponse:
    """
    Remove a configuração do Microsoft Graph API.

    Args:
        current_user: Usuário atual (deve ser administrador)

    Returns:
        Resultado da operação
    """
    try:
        # Verificar se o usuário é administrador
        if current_user.nivel_usuario != "administrador":
            raise HTTPException(
                status_code=403,
                detail="Apenas administradores podem remover a configuração do Microsoft Graph"
            )

        # Remover configurações do banco de dados
        removed = remove_configuracoes_microsoft_graph(db)

        if not removed:
            return GraphConfigResponse(
                success=False,
                message="Nenhuma configuração encontrada para remover",
                is_configured=False
            )

        # Limpar configurações da memória
        from app.config.settings import settings
        settings.MICROSOFT_CLIENT_ID = None
        settings.MICROSOFT_CLIENT_SECRET = None
        settings.MICROSOFT_TENANT_ID = None

        # Reinicializar o serviço de autenticação
        global auth_service
        auth_service = OutlookAuthService()

        logger.info(f"Configuração Microsoft Graph removida do banco por {current_user.login}")

        return GraphConfigResponse(
            success=True,
            message="Configuração do Microsoft Graph removida com sucesso!",
            is_configured=False
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao remover configuração Microsoft Graph: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== OPERAÇÕES DE EMAIL ====================

class EmailActionRequest(BaseModel):
    """Request para ações em emails."""
    message_id: str

class MoveEmailRequest(BaseModel):
    """Request para mover email."""
    message_id: str
    destination_folder_id: str

class MarkAsReadRequest(BaseModel):
    """Request para marcar email como lido/não lido."""
    message_id: str
    is_read: bool = True

@router.delete("/messages/{message_id}")
async def delete_email(
    message_id: str,
    user_id: int = Query(..., description="ID do usuário"),
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Exclui um email.
    """
    try:
        # Verificar se o usuário tem acesso
        if current_user.id != user_id and current_user.nivel_usuario != "administrador":
            raise HTTPException(status_code=403, detail="Acesso negado")

        # Obter tokens válidos do usuário
        tokens_and_user = outlook_token_manager.get_valid_tokens(db, user_id)
        if not tokens_and_user:
            raise HTTPException(status_code=401, detail="Usuário não autenticado no Outlook")

        tokens, user_info = tokens_and_user

        # Criar cliente do Outlook
        from app.services.integrations.outlook.client import OutlookGraphClient
        client = OutlookGraphClient(tokens)

        # Excluir mensagem
        success = await client.delete_message(message_id)

        if success:
            return {"success": True, "message": "Email excluído com sucesso"}
        else:
            raise HTTPException(status_code=400, detail="Falha ao excluir email")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao excluir email {message_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/messages/move")
async def move_email(
    request: MoveEmailRequest,
    user_id: int = Query(..., description="ID do usuário"),
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Move um email para outra pasta.
    """
    try:
        # Verificar se o usuário tem acesso
        if current_user.id != user_id and current_user.nivel_usuario != "administrador":
            raise HTTPException(status_code=403, detail="Acesso negado")

        # Obter tokens válidos do usuário
        tokens_and_user = outlook_token_manager.get_valid_tokens(db, user_id)
        if not tokens_and_user:
            raise HTTPException(status_code=401, detail="Usuário não autenticado no Outlook")

        tokens, user_info = tokens_and_user

        # Criar cliente do Outlook
        from app.services.integrations.outlook.client import OutlookGraphClient
        client = OutlookGraphClient(tokens)

        # Mover mensagem
        success = await client.move_message(request.message_id, request.destination_folder_id)

        if success:
            return {"success": True, "message": "Email movido com sucesso"}
        else:
            raise HTTPException(status_code=400, detail="Falha ao mover email")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao mover email {request.message_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/messages/read")
async def mark_email_as_read(
    request: MarkAsReadRequest,
    user_id: int = Query(..., description="ID do usuário"),
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Marca um email como lido ou não lido.
    """
    try:
        # Verificar se o usuário tem acesso
        if current_user.id != user_id and current_user.nivel_usuario != "administrador":
            raise HTTPException(status_code=403, detail="Acesso negado")

        # Obter tokens válidos do usuário
        tokens_and_user = outlook_token_manager.get_valid_tokens(db, user_id)
        if not tokens_and_user:
            raise HTTPException(status_code=401, detail="Usuário não autenticado no Outlook")

        tokens, user_info = tokens_and_user

        # Criar cliente do Outlook
        from app.services.integrations.outlook.client import OutlookGraphClient
        client = OutlookGraphClient(tokens)

        # Marcar como lido/não lido
        success = await client.mark_as_read(request.message_id, request.is_read)

        if success:
            status = "lido" if request.is_read else "não lido"
            return {"success": True, "message": f"Email marcado como {status}"}
        else:
            raise HTTPException(status_code=400, detail="Falha ao atualizar status do email")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao marcar email {request.message_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/debug/raw-message/{message_id}")
async def debug_raw_message(
    message_id: str,
    user_id: int = Query(..., description="ID do usuário"),
    current_user: UsuarioResponse = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Endpoint de debug para ver dados brutos de uma mensagem da API do Microsoft Graph.
    """
    try:
        # Verificar se o usuário tem acesso
        if current_user.id != user_id and current_user.nivel_usuario != "administrador":
            raise HTTPException(status_code=403, detail="Acesso negado")

        # Obter tokens válidos do usuário
        tokens_and_user = outlook_token_manager.get_valid_tokens(db, user_id)
        if not tokens_and_user:
            raise HTTPException(status_code=401, detail="Usuário não autenticado no Outlook")

        tokens, user_info = tokens_and_user

        # Criar cliente do Outlook
        from app.services.integrations.outlook.client import OutlookGraphClient
        client = OutlookGraphClient(tokens)

        # Fazer requisição direta à API do Microsoft Graph
        import httpx
        url = f"https://graph.microsoft.com/v1.0/me/messages/{message_id}"
        params = {
            "$select": "id,subject,body,from,toRecipients,ccRecipients,bccRecipients,importance,isRead,receivedDateTime,sentDateTime,conversationId,internetMessageId,webLink,sender,replyTo"
        }

        async with httpx.AsyncClient() as http_client:
            response = await http_client.get(
                url,
                headers=client._get_headers(),
                params=params
            )
            response.raise_for_status()
            raw_data = response.json()

        return {
            "message_id": message_id,
            "raw_data": raw_data,
            "from_field": raw_data.get("from"),
            "sender_field": raw_data.get("sender"),
            "reply_to_field": raw_data.get("replyTo"),
            "analysis": {
                "has_from": raw_data.get("from") is not None,
                "has_sender": raw_data.get("sender") is not None,
                "has_reply_to": raw_data.get("replyTo") is not None,
                "from_type": type(raw_data.get("from")).__name__,
                "sender_type": type(raw_data.get("sender")).__name__
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao obter dados brutos da mensagem {message_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
