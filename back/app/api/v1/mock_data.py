"""
API endpoints para dados mock do sistema de atendimento unificado
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from datetime import datetime

from app.services.mock.threecx_mock import threecx_mock_service
from app.services.mock.whatsapp_mock import whatsapp_mock_service, ChatStatus
from app.schemas.threecx import CallRecordResponse, AgentStatus, AgentStatusEnum

router = APIRouter(
    prefix="/mock",
    tags=["Mock Data"],
    responses={404: {"description": "Not found"}},
)

# ==================== 3CX Mock Endpoints ====================

@router.get("/threecx/calls", response_model=List[CallRecordResponse])
async def get_mock_calls(
    start_date: Optional[str] = Query(None, description="Data de início (ISO format)"),
    end_date: Optional[str] = Query(None, description="Data de fim (ISO format)"),
    agent_id: Optional[str] = Query(None, description="ID do agente"),
    client_number: Optional[str] = Query(None, description="Número do cliente"),
    limit: int = Query(50, description="Limite de resultados")
):
    """
    Retorna lista de chamadas mock do 3CX
    """
    try:
        calls = await threecx_mock_service.get_calls(
            start_date=start_date,
            end_date=end_date,
            agent_id=agent_id,
            client_number=client_number,
            limit=limit
        )
        return calls
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter chamadas mock: {str(e)}")

@router.get("/threecx/calls/{call_id}", response_model=CallRecordResponse)
async def get_mock_call(call_id: str):
    """
    Retorna uma chamada específica
    """
    call = await threecx_mock_service.get_call(call_id)
    if not call:
        raise HTTPException(status_code=404, detail="Chamada não encontrada")
    return call

@router.get("/threecx/agents", response_model=List[AgentStatus])
async def get_mock_agents():
    """
    Retorna lista de agentes mock
    """
    try:
        agents = await threecx_mock_service.get_agents()
        return agents
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter agentes mock: {str(e)}")

@router.get("/threecx/agents/{agent_id}", response_model=AgentStatus)
async def get_mock_agent(agent_id: str):
    """
    Retorna um agente específico
    """
    agent = await threecx_mock_service.get_agent(agent_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agente não encontrado")
    return agent

@router.put("/threecx/agents/{agent_id}/status", response_model=AgentStatus)
async def update_mock_agent_status(agent_id: str, status: AgentStatusEnum):
    """
    Atualiza o status de um agente
    """
    agent = await threecx_mock_service.update_agent_status(agent_id, status)
    if not agent:
        raise HTTPException(status_code=404, detail="Agente não encontrado")
    return agent

@router.post("/threecx/calls/make", response_model=CallRecordResponse)
async def make_mock_call(agent_id: str, number: str):
    """
    Simula fazer uma chamada
    """
    call = await threecx_mock_service.make_call(agent_id, number)
    if not call:
        raise HTTPException(status_code=400, detail="Erro ao fazer chamada")
    return call

@router.post("/threecx/calls/{call_id}/end")
async def end_mock_call(call_id: str):
    """
    Simula encerrar uma chamada
    """
    success = await threecx_mock_service.end_call(call_id)
    if not success:
        raise HTTPException(status_code=404, detail="Chamada não encontrada")
    return {"message": "Chamada encerrada com sucesso"}

@router.get("/threecx/statistics")
async def get_mock_call_statistics():
    """
    Retorna estatísticas das chamadas
    """
    try:
        stats = await threecx_mock_service.get_call_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter estatísticas: {str(e)}")

# ==================== WhatsApp Mock Endpoints ====================

@router.get("/whatsapp/chats")
async def get_mock_whatsapp_chats(
    status: Optional[ChatStatus] = Query(None, description="Status do chat")
):
    """
    Retorna lista de chats do WhatsApp
    """
    try:
        chats = await whatsapp_mock_service.get_chats(status=status)
        return chats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter chats mock: {str(e)}")

@router.get("/whatsapp/chats/{chat_id}")
async def get_mock_whatsapp_chat(chat_id: str):
    """
    Retorna um chat específico com suas mensagens
    """
    chat = await whatsapp_mock_service.get_chat(chat_id)
    if not chat:
        raise HTTPException(status_code=404, detail="Chat não encontrado")
    return chat

@router.post("/whatsapp/chats/{chat_id}/messages")
async def send_mock_whatsapp_message(chat_id: str, message: str):
    """
    Simula envio de mensagem no WhatsApp
    """
    sent_message = await whatsapp_mock_service.send_message(chat_id, message)
    if not sent_message:
        raise HTTPException(status_code=404, detail="Chat não encontrado")
    return sent_message

@router.put("/whatsapp/chats/{chat_id}/read")
async def mark_mock_whatsapp_as_read(chat_id: str):
    """
    Marca mensagens como lidas
    """
    success = await whatsapp_mock_service.mark_as_read(chat_id)
    if not success:
        raise HTTPException(status_code=404, detail="Chat não encontrado")
    return {"message": "Mensagens marcadas como lidas"}

@router.get("/whatsapp/statistics")
async def get_mock_whatsapp_statistics():
    """
    Retorna estatísticas do WhatsApp
    """
    try:
        stats = await whatsapp_mock_service.get_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter estatísticas: {str(e)}")

# ==================== Endpoints Unificados ====================

@router.get("/unified/dashboard")
async def get_unified_dashboard():
    """
    Retorna dados consolidados para o dashboard unificado
    """
    try:
        # Obter estatísticas de todos os canais
        threecx_stats = await threecx_mock_service.get_call_statistics()
        whatsapp_stats = await whatsapp_mock_service.get_statistics()

        # Obter dados recentes
        recent_calls = await threecx_mock_service.get_calls(limit=5)
        recent_chats = await whatsapp_mock_service.get_chats()

        # Filtrar apenas chats com mensagens não lidas
        unread_chats = [chat for chat in recent_chats if chat.unread_count > 0]

        return {
            "statistics": {
                "threecx": threecx_stats,
                "whatsapp": whatsapp_stats,
                "total_active_interactions": threecx_stats["active_calls"] + whatsapp_stats["active_chats"],
                "total_pending": threecx_stats["active_calls"] + whatsapp_stats["waiting_chats"]
            },
            "recent_activity": {
                "calls": recent_calls[:3],  # Últimas 3 chamadas
                "whatsapp_chats": unread_chats[:3]  # 3 chats com mensagens não lidas
            },
            "agent_status": await threecx_mock_service.get_agents()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter dados do dashboard: {str(e)}")

@router.get("/unified/notifications")
async def get_unified_notifications():
    """
    Retorna notificações em tempo real de todos os canais
    """
    try:
        notifications = []

        # Chamadas tocando
        calls = await threecx_mock_service.get_calls(limit=10)
        ringing_calls = [call for call in calls if call.end_time is None and call.duration < 30]

        for call in ringing_calls:
            notifications.append({
                "id": f"call_{call.id}",
                "type": "incoming_call",
                "channel": "3cx",
                "title": "Chamada Recebida",
                "message": f"Chamada de {call.caller_name} ({call.caller_number})",
                "timestamp": call.start_time,
                "priority": "high",
                "data": call
            })

        # Mensagens não lidas do WhatsApp
        chats = await whatsapp_mock_service.get_chats()
        unread_chats = [chat for chat in chats if chat.unread_count > 0]

        for chat in unread_chats:
            notifications.append({
                "id": f"whatsapp_{chat.id}",
                "type": "unread_message",
                "channel": "whatsapp",
                "title": "Nova Mensagem WhatsApp",
                "message": f"{chat.contact_name}: {chat.last_message}",
                "timestamp": chat.last_message_time,
                "priority": "medium",
                "data": chat
            })

        # Ordenar por timestamp (mais recente primeiro)
        notifications.sort(key=lambda x: x["timestamp"], reverse=True)

        return notifications[:10]  # Retornar apenas as 10 mais recentes

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao obter notificações: {str(e)}")

@router.get("/health")
async def mock_health_check():
    """
    Health check para os serviços mock
    """
    return {
        "status": "healthy",
        "services": {
            "threecx_mock": "operational",
            "whatsapp_mock": "operational"
        },
        "timestamp": datetime.now()
    }

# ==================== NOVOS ENDPOINTS DE MÉTRICAS AVANÇADAS ====================

@router.get("/threecx/advanced-statistics")
async def get_advanced_threecx_statistics():
    """
    Retorna estatísticas avançadas do 3CX com métricas profissionais de call center.
    """
    try:
        from datetime import datetime, timedelta
        from app.services.mock.advanced_metrics_mock import advanced_metrics_mock
        from app.services.metrics_calculator import MetricsCalculator

        # Período: últimos 7 dias
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)

        # Gerar dados realistas
        calls_data = advanced_metrics_mock.generate_realistic_calls_data(start_date, end_date)
        agent_activity = advanced_metrics_mock.generate_agent_activity_data(start_date, end_date)

        # Calcular métricas
        calculator = MetricsCalculator()
        timing_metrics = calculator.calculate_timing_metrics(calls_data, 20)
        sla_metrics = calculator.calculate_sla_metrics(calls_data, 20)

        # Calcular performance dos agentes
        agent_performances = []
        for agent_id, activity in agent_activity.items():
            agent_calls = [call for call in calls_data if call.get('agent_id') == agent_id]
            if agent_calls:
                performance = calculator.calculate_agent_performance(
                    agent_id, activity['agent_name'], agent_calls, activity
                )
                agent_performances.append({
                    "agent_id": performance.agent_id,
                    "agent_name": performance.agent_name,
                    "calls_handled": performance.calls_handled,
                    "occupancy_rate": performance.occupancy_rate,
                    "productivity_score": performance.productivity_score,
                    "calls_per_hour": performance.calls_per_hour
                })

        return {
            "status": "success",
            "data": {
                "period": {
                    "start_date": start_date.strftime("%Y-%m-%d"),
                    "end_date": end_date.strftime("%Y-%m-%d")
                },
                "timing_metrics": {
                    "asa": timing_metrics.asa,
                    "aht": timing_metrics.aht,
                    "service_level": timing_metrics.service_level,
                    "abandonment_rate": timing_metrics.abandonment_rate,
                    "total_calls": timing_metrics.total_calls,
                    "answered_calls": timing_metrics.answered_calls
                },
                "sla_metrics": {
                    "service_level_target": sla_metrics.service_level_target,
                    "service_level_achieved": sla_metrics.service_level_achieved,
                    "sla_compliance": sla_metrics.sla_compliance,
                    "calls_within_sla": sla_metrics.calls_within_sla,
                    "average_response_time": sla_metrics.average_response_time
                },
                "agent_performance": agent_performances,
                "summary": {
                    "total_agents": len(agent_performances),
                    "overall_performance": "Excelente" if sla_metrics.sla_compliance > 90 else "Bom" if sla_metrics.sla_compliance > 80 else "Precisa Melhorar"
                }
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao obter estatísticas avançadas: {str(e)}"
        )

@router.get("/threecx/real-time-metrics")
async def get_real_time_metrics():
    """
    Retorna métricas em tempo real do call center.
    """
    try:
        import random
        from datetime import datetime

        # Simular métricas em tempo real
        current_time = datetime.now()

        return {
            "status": "success",
            "timestamp": current_time.isoformat(),
            "data": {
                "live_metrics": {
                    "calls_in_queue": random.randint(0, 15),
                    "agents_available": random.randint(3, 8),
                    "agents_busy": random.randint(2, 7),
                    "agents_on_break": random.randint(0, 3),
                    "longest_wait_time": random.randint(0, 180),
                    "current_service_level": round(random.uniform(75, 95), 1)
                },
                "hourly_trends": {
                    "calls_this_hour": random.randint(20, 50),
                    "avg_wait_this_hour": round(random.uniform(10, 45), 1),
                    "abandonment_this_hour": round(random.uniform(2, 8), 1)
                },
                "alerts": [
                    {
                        "type": "warning" if random.random() > 0.7 else "info",
                        "message": "Service level abaixo da meta" if random.random() > 0.8 else "Operação normal",
                        "timestamp": current_time.isoformat()
                    }
                ]
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao obter métricas em tempo real: {str(e)}"
        )
