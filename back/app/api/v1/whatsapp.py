"""
API endpoints para integração com WhatsApp via Evolution API
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from typing import List, Optional
import logging
import os

from app.schemas.whatsapp import (
    WhatsAppConversation,
    WhatsAppMessage,
    WhatsAppInstance,
    WhatsAppStats,
    SendMessageRequest,
    CreateInstanceRequest,
    AssignConversationRequest,
    InstanceStatus
)
from app.services.integrations.evolution.client import EvolutionAPIClient
from app.services.integrations.evolution.webhook_handler import webhook_handler

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/whatsapp",
    tags=["WhatsApp"],
    responses={404: {"description": "Not found"}},
)

# Configuração da Evolution API
EVOLUTION_API_URL = os.getenv("EVOLUTION_API_URL", "http://localhost:8080")
EVOLUTION_API_KEY = os.getenv("EVOLUTION_API_KEY", "B6D711FCDE4D4FD5936544120E713976")

def get_evolution_client() -> EvolutionAPIClient:
    """Dependência para obter cliente Evolution API"""
    return EvolutionAPIClient(EVOLUTION_API_URL, EVOLUTION_API_KEY)


# ==================== Endpoints de Instâncias ====================

@router.post("/instances", response_model=dict)
async def create_instance(
    request: CreateInstanceRequest,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Criar nova instância WhatsApp"""
    try:
        result = await client.create_instance(request.instance_name)
        
        # Configurar webhook se fornecido
        if request.webhook_url:
            await client.set_webhook(request.instance_name, request.webhook_url)
        
        return {
            "success": True,
            "message": "Instância criada com sucesso",
            "data": result
        }
    except Exception as e:
        logger.error(f"Erro ao criar instância: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/instances/{instance_name}/qr")
async def get_qr_code(
    instance_name: str,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Obter QR Code para conectar WhatsApp"""
    try:
        qr_code = await client.get_qr_code(instance_name)
        if not qr_code:
            raise HTTPException(status_code=404, detail="QR Code não disponível")
        
        return {
            "success": True,
            "qr_code": qr_code
        }
    except Exception as e:
        logger.error(f"Erro ao obter QR code: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/instances/{instance_name}/status")
async def get_instance_status(
    instance_name: str,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Obter status da instância"""
    try:
        status = await client.get_instance_status(instance_name)
        info = await client.get_instance_info(instance_name)
        
        return {
            "success": True,
            "status": status,
            "info": info
        }
    except Exception as e:
        logger.error(f"Erro ao obter status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/instances/{instance_name}")
async def delete_instance(
    instance_name: str,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Deletar instância"""
    try:
        success = await client.delete_instance(instance_name)
        if not success:
            raise HTTPException(status_code=500, detail="Falha ao deletar instância")
        
        return {
            "success": True,
            "message": "Instância deletada com sucesso"
        }
    except Exception as e:
        logger.error(f"Erro ao deletar instância: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== Endpoints de Mensagens ====================

@router.post("/messages/send")
async def send_message(
    request: SendMessageRequest,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Enviar mensagem de texto"""
    try:
        result = await client.send_text_message(
            request.instance_name,
            request.number,
            request.text
        )
        
        # Adicionar mensagem ao handler local
        conversation_id = f"{request.instance_name}_{request.number.replace('+', '').replace('-', '').replace(' ', '')}"
        await webhook_handler.add_agent_message(
            conversation_id,
            request.text,
            "Agente"
        )
        
        return {
            "success": True,
            "message": "Mensagem enviada com sucesso",
            "data": result
        }
    except Exception as e:
        logger.error(f"Erro ao enviar mensagem: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== Endpoints de Conversas ====================

@router.get("/conversations", response_model=List[WhatsAppConversation])
async def get_conversations(
    status: Optional[str] = None
):
    """Obter lista de conversas"""
    try:
        conversations = await webhook_handler.get_conversations(status)
        return conversations
    except Exception as e:
        logger.error(f"Erro ao obter conversas: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/conversations/{conversation_id}/messages", response_model=List[WhatsAppMessage])
async def get_conversation_messages(conversation_id: str):
    """Obter mensagens de uma conversa"""
    try:
        messages = await webhook_handler.get_conversation_messages(conversation_id)
        return messages
    except Exception as e:
        logger.error(f"Erro ao obter mensagens: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/conversations/{conversation_id}/assign")
async def assign_conversation(
    conversation_id: str,
    request: AssignConversationRequest
):
    """Atribuir conversa a um atendente"""
    try:
        # Em uma implementação real, isso seria salvo no banco de dados
        if conversation_id in webhook_handler.conversations:
            conversation = webhook_handler.conversations[conversation_id]
            conversation.assigned_user_id = request.user_id
            conversation.status = "assigned"
            
            return {
                "success": True,
                "message": "Conversa atribuída com sucesso"
            }
        else:
            raise HTTPException(status_code=404, detail="Conversa não encontrada")
    except Exception as e:
        logger.error(f"Erro ao atribuir conversa: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== Endpoints de Estatísticas ====================

@router.get("/stats", response_model=WhatsAppStats)
async def get_whatsapp_stats():
    """Obter estatísticas do WhatsApp"""
    try:
        conversations = await webhook_handler.get_conversations()
        
        stats = WhatsAppStats(
            total_conversations=len(conversations),
            active_conversations=len([c for c in conversations if c.status == "open"]),
            waiting_conversations=len([c for c in conversations if c.status == "waiting"]),
            unread_messages=sum(c.unread_count for c in conversations),
            connected_instances=1  # Simplificado para o MVP
        )
        
        return stats
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== Webhook Endpoint ====================

@router.post("/webhook")
async def evolution_webhook(
    request: Request,
    background_tasks: BackgroundTasks
):
    """Receber webhooks da Evolution API"""
    try:
        payload = await request.json()
        logger.info(f"Webhook recebido: {payload}")
        
        # Processar webhook em background
        background_tasks.add_task(webhook_handler.process_webhook, payload)
        
        return {"status": "ok"}
    except Exception as e:
        logger.error(f"Erro ao processar webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ==================== Endpoints de Configuração ====================

@router.post("/instances/{instance_name}/webhook")
async def set_webhook(
    instance_name: str,
    webhook_url: str,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Configurar webhook para instância"""
    try:
        success = await client.set_webhook(instance_name, webhook_url)
        if not success:
            raise HTTPException(status_code=500, detail="Falha ao configurar webhook")
        
        return {
            "success": True,
            "message": "Webhook configurado com sucesso"
        }
    except Exception as e:
        logger.error(f"Erro ao configurar webhook: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/instances/{instance_name}/logout")
async def logout_instance(
    instance_name: str,
    client: EvolutionAPIClient = Depends(get_evolution_client)
):
    """Fazer logout da instância"""
    try:
        success = await client.logout_instance(instance_name)
        if not success:
            raise HTTPException(status_code=500, detail="Falha ao fazer logout")
        
        return {
            "success": True,
            "message": "Logout realizado com sucesso"
        }
    except Exception as e:
        logger.error(f"Erro ao fazer logout: {e}")
        raise HTTPException(status_code=500, detail=str(e))
