"""
API endpoints para autenticação de usuários.
"""

import logging
from datetime import timed<PERSON><PERSON>
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.database.connection import get_db
from app.crud.usuario import usuario_crud
from app.schemas.usuario import (
    UsuarioResponse,
    UsuarioLoginResponse,
    UsuarioCreate,
    UsuarioUpdate,
    ChangePasswordRequest
)
from app.utils.security import (
    create_access_token,
    verify_token,
    ACCESS_TOKEN_EXPIRE_MINUTES
)

logger = logging.getLogger(__name__)
router = APIRouter()

# OAuth2 scheme para autenticação JWT
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
    db: Session = Depends(get_db)
) -> UsuarioResponse:
    """
    Obter usuário atual a partir do token JWT.
    """
    logger.info(f"🔐 Validando token JWT: {token[:50]}...")

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    payload = verify_token(token)
    logger.info(f"🔐 Payload do token: {payload}")

    if payload is None:
        logger.error("❌ Token JWT inválido ou expirado")
        raise credentials_exception

    user_id: int = payload.get("sub")
    logger.info(f"🔐 User ID do token: {user_id}")

    if user_id is None:
        logger.error("❌ User ID não encontrado no token")
        raise credentials_exception

    # Converter user_id para int se for string
    try:
        user_id = int(user_id)
    except (ValueError, TypeError):
        logger.error(f"❌ User ID inválido: {user_id}")
        raise credentials_exception

    user = usuario_crud.get_by_id(db, user_id=user_id)
    logger.info(f"🔐 Usuário encontrado: {user.login if user else 'None'}")

    if user is None:
        logger.error(f"❌ Usuário com ID {user_id} não encontrado no banco")
        raise credentials_exception

    if not user.ativo:
        logger.error(f"❌ Usuário {user.login} está inativo")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Usuário inativo"
        )

    logger.info(f"✅ Usuário {user.login} autenticado com sucesso")
    return UsuarioResponse.from_orm(user)

async def get_current_admin_user(
    current_user: Annotated[UsuarioResponse, Depends(get_current_user)]
) -> UsuarioResponse:
    """
    Verificar se o usuário atual é administrador.
    """
    if current_user.nivel_usuario != "administrador":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Acesso negado. Apenas administradores podem acessar este recurso."
        )
    return current_user

@router.post("/auth/login", response_model=UsuarioLoginResponse)
async def login(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    db: Session = Depends(get_db)
):
    """
    Endpoint para login de usuário.
    """
    user = usuario_crud.authenticate(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Login ou senha incorretos",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.ativo:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Usuário inativo"
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    return UsuarioLoginResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # em segundos
        usuario=UsuarioResponse.from_orm(user)
    )

@router.get("/auth/me", response_model=UsuarioResponse)
async def read_users_me(
    current_user: Annotated[UsuarioResponse, Depends(get_current_user)]
):
    """
    Obter informações do usuário atual.
    """
    return current_user

@router.post("/auth/logout")
async def logout(
    current_user: Annotated[UsuarioResponse, Depends(get_current_user)]
):
    """
    Endpoint para logout (invalidar token).
    Nota: Em uma implementação real, você manteria uma blacklist de tokens.
    """
    return {"message": "Logout realizado com sucesso"}



@router.post("/auth/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: Annotated[UsuarioResponse, Depends(get_current_user)],
    db: Session = Depends(get_db)
):
    """
    Alterar senha do usuário atual.
    """
    success = usuario_crud.change_password(
        db,
        current_user.id,
        password_data.senha_atual,
        password_data.nova_senha
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Senha atual incorreta"
        )

    return {"message": "Senha alterada com sucesso"}

@router.get("/auth/verify-admin")
async def verify_admin(
    current_admin: Annotated[UsuarioResponse, Depends(get_current_admin_user)]
):
    """
    Verificar se o usuário é administrador.
    """
    return {
        "is_admin": True,
        "user": current_admin
    }

@router.post("/auth/debug-token")
async def debug_token(request: dict):
    """
    Endpoint para debugar problemas de token JWT.
    """
    token = request.get("token", "")

    if not token:
        return {"error": "Token não fornecido"}

    try:
        from app.utils.security import SECRET_KEY, ALGORITHM
        from jose import jwt

        # Tentar decodificar sem verificar assinatura
        payload_unverified = jwt.decode(token, options={"verify_signature": False})

        # Tentar decodificar com verificação
        try:
            payload_verified = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            verification_status = "✅ Token válido"
        except Exception as e:
            payload_verified = None
            verification_status = f"❌ Token inválido: {str(e)}"

        return {
            "secret_key_length": len(SECRET_KEY),
            "algorithm": ALGORITHM,
            "payload_unverified": payload_unverified,
            "payload_verified": payload_verified,
            "verification_status": verification_status
        }

    except Exception as e:
        return {"error": f"Erro ao processar token: {str(e)}"}

@router.get("/usuarios", response_model=list[UsuarioResponse])
async def list_usuarios(
    current_admin: Annotated[UsuarioResponse, Depends(get_current_admin_user)],
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100
):
    """
    Listar todos os usuários (apenas para administradores).
    """
    usuarios = usuario_crud.get_all(db, skip=skip, limit=limit)
    return [UsuarioResponse.from_orm(user) for user in usuarios]

@router.post("/usuarios", response_model=UsuarioResponse)
async def create_usuario(
    usuario_data: UsuarioCreate,
    current_admin: Annotated[UsuarioResponse, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Criar novo usuário (apenas para administradores).
    """
    # Verificar se login já existe
    existing_user = usuario_crud.get_by_login(db, usuario_data.login)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Login já está em uso"
        )

    # Verificar se email já existe
    existing_email = usuario_crud.get_by_email(db, usuario_data.email_corporativo)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email já está em uso"
        )

    user = usuario_crud.create(db, usuario_data)
    return UsuarioResponse.from_orm(user)

@router.delete("/usuarios/{user_id}")
async def delete_usuario(
    user_id: int,
    current_admin: Annotated[UsuarioResponse, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Excluir usuário (soft delete - apenas para administradores).
    """
    # Verificar se o usuário existe
    user_to_delete = usuario_crud.get_by_id(db, user_id)
    if not user_to_delete:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )

    # Não permitir que o admin exclua a si mesmo
    if user_id == current_admin.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Você não pode excluir seu próprio usuário"
        )

    # Realizar soft delete
    success = usuario_crud.delete(db, user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro ao excluir usuário"
        )

    return {"message": "Usuário excluído com sucesso"}

@router.put("/usuarios/{user_id}", response_model=UsuarioResponse)
async def update_usuario(
    user_id: int,
    usuario_data: UsuarioUpdate,
    current_admin: Annotated[UsuarioResponse, Depends(get_current_admin_user)],
    db: Session = Depends(get_db)
):
    """
    Atualizar usuário (apenas para administradores).
    """
    # Verificar se o usuário existe
    existing_user = usuario_crud.get_by_id(db, user_id)
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuário não encontrado"
        )

    # Se está alterando login, verificar se já existe
    if usuario_data.login and usuario_data.login != existing_user.login:
        login_exists = usuario_crud.get_by_login(db, usuario_data.login)
        if login_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Login já está em uso"
            )

    # Se está alterando email, verificar se já existe
    if usuario_data.email_corporativo and usuario_data.email_corporativo != existing_user.email_corporativo:
        email_exists = usuario_crud.get_by_email(db, usuario_data.email_corporativo)
        if email_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email já está em uso"
            )

    # Atualizar usuário
    updated_user = usuario_crud.update(db, user_id, usuario_data)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro ao atualizar usuário"
        )

    return UsuarioResponse.from_orm(updated_user)
