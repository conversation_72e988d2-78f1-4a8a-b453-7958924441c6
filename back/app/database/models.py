"""
Modelos SQLAlchemy para o banco de dados.
"""

from sqlalchemy import Column, Integer, String, DateTime, Enum, Boolean, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.database.connection import Base

class NivelUsuario(enum.Enum):
    """Enum para níveis de usuário."""
    ADMINISTRADOR = "administrador"
    AGENTE = "agente"

class Usuario(Base):
    """
    Modelo da tabela de usuários.
    """
    __tablename__ = "usuarios"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    nome = Column(String(100), nullable=False, comment="Nome do usuário")
    sobrenome = Column(String(100), nullable=False, comment="Sobrenome do usuário")
    login = Column(String(50), unique=True, nullable=False, index=True, comment="Login único do usuário")
    senha = Column(String(255), nullable=False, comment="Senha criptografada do usuário")
    nivel_usuario = Column(
        Enum(NivelUsuario),
        nullable=False,
        default=NivelUsuario.AGENTE,
        comment="Nível de acesso do usuário"
    )
    email_corporativo = Column(
        String(255),
        nullable=False,
        comment="Email corporativo para integração Outlook"
    )
    senha_email_corporativo = Column(
        String(255),
        nullable=False,
        comment="Senha do email corporativo para integração Outlook"
    )

    # Campos de controle
    ativo = Column(Boolean, default=True, nullable=False, comment="Se o usuário está ativo")
    criado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação"
    )
    atualizado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Data e hora da última atualização"
    )
    ultimo_login = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Data e hora do último login"
    )

    # Relacionamentos
    # outlook_tokens = relationship("OutlookTokenDB", back_populates="user", uselist=False)

    def __repr__(self):
        return f"<Usuario(id={self.id}, login='{self.login}', nome='{self.nome}', nivel='{self.nivel_usuario.value}')>"

    def to_dict(self):
        """Converte o modelo para dicionário (sem senha)."""
        return {
            "id": self.id,
            "nome": self.nome,
            "sobrenome": self.sobrenome,
            "login": self.login,
            "nivel_usuario": self.nivel_usuario.value,
            "email_corporativo": self.email_corporativo,
            "ativo": self.ativo,
            "criado_em": self.criado_em.isoformat() if self.criado_em else None,
            "atualizado_em": self.atualizado_em.isoformat() if self.atualizado_em else None,
            "ultimo_login": self.ultimo_login.isoformat() if self.ultimo_login else None
        }

    @property
    def nome_completo(self):
        """Retorna o nome completo do usuário."""
        return f"{self.nome} {self.sobrenome}"

    def is_admin(self):
        """Verifica se o usuário é administrador."""
        return self.nivel_usuario == NivelUsuario.ADMINISTRADOR

    def is_agente(self):
        """Verifica se o usuário é agente."""
        return self.nivel_usuario == NivelUsuario.AGENTE


class ConfiguracaoSistema(Base):
    """
    Modelo para armazenar configurações do sistema.
    """
    __tablename__ = "configuracoes_sistema"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    chave = Column(String(100), unique=True, nullable=False, index=True, comment="Chave única da configuração")
    valor = Column(Text, nullable=True, comment="Valor da configuração (pode ser JSON)")
    descricao = Column(String(255), nullable=True, comment="Descrição da configuração")
    categoria = Column(String(50), nullable=False, default="geral", comment="Categoria da configuração")

    # Campos de controle
    criado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação"
    )
    atualizado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Data e hora da última atualização"
    )

    def __repr__(self):
        return f"<ConfiguracaoSistema(id={self.id}, chave='{self.chave}', categoria='{self.categoria}')>"

    def to_dict(self):
        """Converte o modelo para dicionário."""
        return {
            "id": self.id,
            "chave": self.chave,
            "valor": self.valor,
            "descricao": self.descricao,
            "categoria": self.categoria,
            "criado_em": self.criado_em.isoformat() if self.criado_em else None,
            "atualizado_em": self.atualizado_em.isoformat() if self.atualizado_em else None
        }
