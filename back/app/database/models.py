"""
Modelos SQLAlchemy para o banco de dados.
"""

from sqlalchemy import Column, Integer, String, DateTime, Enum, Boolean, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.database.connection import Base

class NivelUsuario(enum.Enum):
    """Enum para níveis de usuário."""
    ADMINISTRADOR = "administrador"
    AGENTE = "agente"

class Usuario(Base):
    """
    Modelo da tabela de usuários.
    """
    __tablename__ = "usuarios"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    nome = Column(String(100), nullable=False, comment="Nome do usuário")
    sobrenome = Column(String(100), nullable=False, comment="Sobrenome do usuário")
    login = Column(String(50), unique=True, nullable=False, index=True, comment="Login único do usuário")
    senha = Column(String(255), nullable=False, comment="Senha criptografada do usuário")
    nivel_usuario = Column(
        Enum(NivelUsuario),
        nullable=False,
        default=NivelUsuario.AGENTE,
        comment="Nível de acesso do usuário"
    )
    email_corporativo = Column(
        String(255),
        nullable=False,
        comment="Email corporativo para integração Outlook"
    )
    senha_email_corporativo = Column(
        String(255),
        nullable=False,
        comment="Senha do email corporativo para integração Outlook"
    )

    # Campos de controle
    ativo = Column(Boolean, default=True, nullable=False, comment="Se o usuário está ativo")
    criado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação"
    )
    atualizado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Data e hora da última atualização"
    )
    ultimo_login = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Data e hora do último login"
    )

    # Relacionamentos
    # outlook_tokens = relationship("OutlookTokenDB", back_populates="user", uselist=False)

    def __repr__(self):
        return f"<Usuario(id={self.id}, login='{self.login}', nome='{self.nome}', nivel='{self.nivel_usuario.value}')>"

    def to_dict(self):
        """Converte o modelo para dicionário (sem senha)."""
        return {
            "id": self.id,
            "nome": self.nome,
            "sobrenome": self.sobrenome,
            "login": self.login,
            "nivel_usuario": self.nivel_usuario.value,
            "email_corporativo": self.email_corporativo,
            "ativo": self.ativo,
            "criado_em": self.criado_em.isoformat() if self.criado_em else None,
            "atualizado_em": self.atualizado_em.isoformat() if self.atualizado_em else None,
            "ultimo_login": self.ultimo_login.isoformat() if self.ultimo_login else None
        }

    @property
    def nome_completo(self):
        """Retorna o nome completo do usuário."""
        return f"{self.nome} {self.sobrenome}"

    def is_admin(self):
        """Verifica se o usuário é administrador."""
        return self.nivel_usuario == NivelUsuario.ADMINISTRADOR

    def is_agente(self):
        """Verifica se o usuário é agente."""
        return self.nivel_usuario == NivelUsuario.AGENTE


class ConfiguracaoSistema(Base):
    """
    Modelo para armazenar configurações do sistema.
    """
    __tablename__ = "configuracoes_sistema"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    chave = Column(String(100), unique=True, nullable=False, index=True, comment="Chave única da configuração")
    valor = Column(Text, nullable=True, comment="Valor da configuração (pode ser JSON)")
    descricao = Column(String(255), nullable=True, comment="Descrição da configuração")
    categoria = Column(String(50), nullable=False, default="geral", comment="Categoria da configuração")

    # Campos de controle
    criado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Data e hora de criação"
    )
    atualizado_em = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Data e hora da última atualização"
    )

    def __repr__(self):
        return f"<ConfiguracaoSistema(id={self.id}, chave='{self.chave}', categoria='{self.categoria}')>"

    def to_dict(self):
        """Converte o modelo para dicionário."""
        return {
            "id": self.id,
            "chave": self.chave,
            "valor": self.valor,
            "descricao": self.descricao,
            "categoria": self.categoria,
            "criado_em": self.criado_em.isoformat() if self.criado_em else None,
            "atualizado_em": self.atualizado_em.isoformat() if self.atualizado_em else None
        }


# ============================================================================
# MODELOS WHATSAPP
# ============================================================================

from sqlalchemy import ForeignKey
from sqlalchemy.dialects.postgresql import JSONB

class InstanceStatus(enum.Enum):
    """Status da instância WhatsApp"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    QR_CODE = "qr_code"

class ConversationStatus(enum.Enum):
    """Status da conversa"""
    OPEN = "open"
    CLOSED = "closed"
    WAITING = "waiting"
    ASSIGNED = "assigned"

class MessageType(enum.Enum):
    """Tipo de mensagem"""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    STICKER = "sticker"
    LOCATION = "location"
    CONTACT = "contact"

class MessageStatus(enum.Enum):
    """Status da mensagem"""
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"

class WhatsAppInstance(Base):
    """Instância do WhatsApp"""
    __tablename__ = "whatsapp_instances"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    status = Column(Enum(InstanceStatus), default=InstanceStatus.DISCONNECTED)
    qr_code = Column(Text, nullable=True)
    webhook_url = Column(String(500), nullable=True)
    phone_number = Column(String(20), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    conversations = relationship("WhatsAppConversation", back_populates="instance", cascade="all, delete-orphan")

class WhatsAppConversation(Base):
    """Conversa do WhatsApp"""
    __tablename__ = "whatsapp_conversations"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String(200), unique=True, nullable=False, index=True)
    customer_name = Column(String(200), nullable=False)
    customer_number = Column(String(50), nullable=False)
    assigned_user_id = Column(Integer, nullable=True)  # Referência para Usuario quando existir
    instance_id = Column(Integer, ForeignKey("whatsapp_instances.id"), nullable=False)
    status = Column(Enum(ConversationStatus), default=ConversationStatus.WAITING, index=True)
    last_message = Column(Text, nullable=True)
    last_message_time = Column(DateTime(timezone=True), nullable=True)
    unread_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    instance = relationship("WhatsAppInstance", back_populates="conversations")
    messages = relationship("WhatsAppMessage", back_populates="conversation", cascade="all, delete-orphan")

class WhatsAppMessage(Base):
    """Mensagem do WhatsApp"""
    __tablename__ = "whatsapp_messages"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String(200), unique=True, nullable=False)
    conversation_id = Column(Integer, ForeignKey("whatsapp_conversations.id"), nullable=False, index=True)
    sender_type = Column(String(20), nullable=False)  # customer, agent
    sender_name = Column(String(200), nullable=False)
    sender_number = Column(String(50), nullable=True)
    content = Column(Text, nullable=False)
    message_type = Column(Enum(MessageType), default=MessageType.TEXT)
    status = Column(Enum(MessageStatus), default=MessageStatus.SENT)
    media_url = Column(String(500), nullable=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relacionamentos
    conversation = relationship("WhatsAppConversation", back_populates="messages")

class WhatsAppAutoReply(Base):
    """Respostas automáticas do WhatsApp"""
    __tablename__ = "whatsapp_auto_replies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    trigger_type = Column(String(50), nullable=False)  # keyword, time, first_message
    trigger_value = Column(String(500), nullable=True)
    response_message = Column(Text, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())