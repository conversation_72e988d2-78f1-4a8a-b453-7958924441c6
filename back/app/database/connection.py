"""
Configuração de conexão com o banco de dados PostgreSQL.
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from app.config.settings import settings

# URL de conexão com o banco
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "************************************************/amvox_db"
)

# Criar engine do SQLAlchemy
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False  # Mudar para True para debug SQL
)

# Criar sessionmaker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base para os modelos
Base = declarative_base()

def get_db():
    """
    Dependency para obter sessão do banco de dados.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    """
    Criar todas as tabelas no banco de dados.
    """
    Base.metadata.create_all(bind=engine)

def drop_tables():
    """
    Remover todas as tabelas do banco de dados.
    """
    Base.metadata.drop_all(bind=engine)
