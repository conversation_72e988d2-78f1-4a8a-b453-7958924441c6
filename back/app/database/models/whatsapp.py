"""
Modelos de banco de dados para WhatsApp
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.database.connection import Base


class InstanceStatus(enum.Enum):
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    QR_CODE = "qr_code"


class ConversationStatus(enum.Enum):
    OPEN = "open"
    CLOSED = "closed"
    WAITING = "waiting"
    ASSIGNED = "assigned"


class MessageType(enum.Enum):
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    STICKER = "sticker"
    LOCATION = "location"
    CONTACT = "contact"


class MessageStatus(enum.Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"


class WhatsAppInstance(Base):
    """Instância WhatsApp"""
    __tablename__ = "whatsapp_instances"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    status = Column(Enum(InstanceStatus), default=InstanceStatus.DISCONNECTED)
    qr_code = Column(Text, nullable=True)
    webhook_url = Column(String(500), nullable=True)
    phone_number = Column(String(20), nullable=True)
    evolution_instance_id = Column(String(100), nullable=True)
    api_key = Column(String(100), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    conversations = relationship("WhatsAppConversation", back_populates="instance")


class WhatsAppConversation(Base):
    """Conversa WhatsApp"""
    __tablename__ = "whatsapp_conversations"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String(200), unique=True, nullable=False, index=True)  # instance_customer_number
    customer_name = Column(String(200), nullable=False)
    customer_number = Column(String(50), nullable=False, index=True)
    assigned_user_id = Column(Integer, ForeignKey("usuarios.id"), nullable=True)
    instance_id = Column(Integer, ForeignKey("whatsapp_instances.id"), nullable=False)
    status = Column(Enum(ConversationStatus), default=ConversationStatus.WAITING)
    last_message = Column(Text, nullable=True)
    last_message_time = Column(DateTime(timezone=True), nullable=True)
    unread_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    instance = relationship("WhatsAppInstance", back_populates="conversations")
    assigned_user = relationship("Usuario", foreign_keys=[assigned_user_id])
    messages = relationship("WhatsAppMessage", back_populates="conversation")


class WhatsAppMessage(Base):
    """Mensagem WhatsApp"""
    __tablename__ = "whatsapp_messages"

    id = Column(Integer, primary_key=True, index=True)
    external_id = Column(String(200), unique=True, nullable=False, index=True)  # ID da Evolution API
    conversation_id = Column(Integer, ForeignKey("whatsapp_conversations.id"), nullable=False)
    sender_type = Column(String(20), nullable=False)  # 'customer' ou 'agent'
    sender_name = Column(String(200), nullable=False)
    sender_number = Column(String(50), nullable=True)
    content = Column(Text, nullable=False)
    message_type = Column(Enum(MessageType), default=MessageType.TEXT)
    status = Column(Enum(MessageStatus), default=MessageStatus.SENT)
    media_url = Column(String(500), nullable=True)
    media_filename = Column(String(200), nullable=True)
    media_mimetype = Column(String(100), nullable=True)
    timestamp = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relacionamentos
    conversation = relationship("WhatsAppConversation", back_populates="messages")


class WhatsAppTemplate(Base):
    """Templates de mensagem WhatsApp"""
    __tablename__ = "whatsapp_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    content = Column(Text, nullable=False)
    category = Column(String(50), nullable=True)  # 'greeting', 'closing', 'info', etc.
    is_active = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("usuarios.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    creator = relationship("Usuario", foreign_keys=[created_by])


class WhatsAppAutoReply(Base):
    """Respostas automáticas WhatsApp"""
    __tablename__ = "whatsapp_auto_replies"

    id = Column(Integer, primary_key=True, index=True)
    trigger_keywords = Column(Text, nullable=False)  # JSON array de palavras-chave
    response_message = Column(Text, nullable=False)
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=0)  # Ordem de prioridade
    instance_id = Column(Integer, ForeignKey("whatsapp_instances.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    instance = relationship("WhatsAppInstance")
