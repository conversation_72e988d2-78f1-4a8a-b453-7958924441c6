"""
Script para migrar/criar a tabela de configurações do sistema.
"""

from app.database.connection import engine, SessionLocal
from app.database.models import ConfiguracaoSistema
from sqlalchemy import text
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)

def create_configuracoes_table():
    """
    Cria a tabela de configurações se ela não existir.
    """
    try:
        # Verificar se a tabela já existe antes de criar
        if check_table_exists():
            print("ℹ️ Tabela 'configuracoes_sistema' já existe")
            return True

        # Criar a tabela usando SQLAlchemy
        ConfiguracaoSistema.metadata.create_all(bind=engine)
        print("✅ Tabela 'configuracoes_sistema' criada com sucesso!")
        return True
    except Exception as e:
        print(f"❌ Erro ao criar tabela 'configuracoes_sistema': {e}")
        return False

def check_table_exists():
    """
    Verifica se a tabela de configurações existe.
    """
    try:
        db = SessionLocal()
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'configuracoes_sistema'
            );
        """))
        exists = result.scalar()
        db.close()
        return exists
    except Exception as e:
        print(f"❌ Erro ao verificar existência da tabela: {e}")
        return False

def migrate_existing_env_configs():
    """
    Migra configurações existentes das variáveis de ambiente para o banco.
    """
    try:
        from app.config.settings import settings
        from app.crud.configuracao import set_configuracoes_microsoft_graph

        db = SessionLocal()

        # Verificar se já existem configurações no banco
        from app.crud.configuracao import get_configuracoes_microsoft_graph
        existing_configs = get_configuracoes_microsoft_graph(db)

        if existing_configs.get("microsoft_graph_client_id"):
            print("ℹ️ Configurações Microsoft Graph já existem no banco")
            db.close()
            return True

        # Migrar das variáveis de ambiente se existirem
        if (settings.MICROSOFT_CLIENT_ID and
            settings.MICROSOFT_CLIENT_SECRET and
            settings.MICROSOFT_TENANT_ID and
            settings.MICROSOFT_CLIENT_ID != "your_client_id_here"):

            set_configuracoes_microsoft_graph(
                db,
                settings.MICROSOFT_CLIENT_ID,
                settings.MICROSOFT_CLIENT_SECRET,
                settings.MICROSOFT_TENANT_ID
            )
            print("✅ Configurações Microsoft Graph migradas das variáveis de ambiente para o banco!")
        else:
            print("ℹ️ Nenhuma configuração Microsoft Graph válida encontrada nas variáveis de ambiente")

        db.close()
        return True

    except Exception as e:
        print(f"❌ Erro ao migrar configurações: {e}")
        return False

def run_migration():
    """
    Executa a migração completa.
    """
    print("🔄 Iniciando migração da tabela de configurações...")

    # 1. Verificar se a tabela existe
    if check_table_exists():
        print("ℹ️ Tabela 'configuracoes_sistema' já existe")
    else:
        print("🔄 Criando tabela 'configuracoes_sistema'...")
        if not create_configuracoes_table():
            return False

    # 2. Migrar configurações existentes
    print("🔄 Migrando configurações existentes...")
    if not migrate_existing_env_configs():
        return False

    print("✅ Migração concluída com sucesso!")
    return True

if __name__ == "__main__":
    run_migration()
