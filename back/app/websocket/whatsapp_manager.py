"""
WebSocket Manager para WhatsApp em tempo real
"""

import json
import logging
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

logger = logging.getLogger(__name__)


class WhatsAppWebSocketManager:
    """Gerenciador de WebSocket para WhatsApp"""
    
    def __init__(self):
        # Conexões ativas: {user_id: [websockets]}
        self.active_connections: Dict[int, List[WebSocket]] = {}
        # Usuários online
        self.online_users: Set[int] = set()
    
    async def connect(self, websocket: WebSocket, user_id: int):
        """Conectar usuário"""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        
        self.active_connections[user_id].append(websocket)
        self.online_users.add(user_id)
        
        logger.info(f"Usuário {user_id} conectado ao WebSocket WhatsApp")
        
        # Notificar outros usuários que este usuário ficou online
        await self.broadcast_user_status(user_id, "online")
    
    def disconnect(self, websocket: WebSocket, user_id: int):
        """Desconectar usuário"""
        if user_id in self.active_connections:
            if websocket in self.active_connections[user_id]:
                self.active_connections[user_id].remove(websocket)
            
            # Se não há mais conexões, remover usuário
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
                self.online_users.discard(user_id)
                
                logger.info(f"Usuário {user_id} desconectado do WebSocket WhatsApp")
    
    async def send_personal_message(self, message: dict, user_id: int):
        """Enviar mensagem para usuário específico"""
        if user_id in self.active_connections:
            disconnected_websockets = []
            
            for websocket in self.active_connections[user_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except WebSocketDisconnect:
                    disconnected_websockets.append(websocket)
                except Exception as e:
                    logger.error(f"Erro ao enviar mensagem para usuário {user_id}: {e}")
                    disconnected_websockets.append(websocket)
            
            # Remover conexões desconectadas
            for ws in disconnected_websockets:
                self.disconnect(ws, user_id)
    
    async def broadcast_to_all(self, message: dict):
        """Broadcast para todos os usuários conectados"""
        for user_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, user_id)
    
    async def broadcast_user_status(self, user_id: int, status: str):
        """Notificar status do usuário para todos"""
        message = {
            "type": "user_status",
            "user_id": user_id,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        await self.broadcast_to_all(message)
    
    async def notify_new_message(self, conversation_id: str, message_data: dict, 
                                assigned_user_id: int = None):
        """Notificar nova mensagem"""
        notification = {
            "type": "new_message",
            "conversation_id": conversation_id,
            "message": message_data,
            "timestamp": datetime.now().isoformat()
        }
        
        if assigned_user_id:
            # Enviar para usuário específico
            await self.send_personal_message(notification, assigned_user_id)
        else:
            # Broadcast para todos (conversa não atribuída)
            await self.broadcast_to_all(notification)
    
    async def notify_conversation_assigned(self, conversation_id: str, 
                                         assigned_user_id: int, assigned_by: int):
        """Notificar atribuição de conversa"""
        notification = {
            "type": "conversation_assigned",
            "conversation_id": conversation_id,
            "assigned_user_id": assigned_user_id,
            "assigned_by": assigned_by,
            "timestamp": datetime.now().isoformat()
        }
        
        # Enviar para o usuário atribuído
        await self.send_personal_message(notification, assigned_user_id)
        
        # Notificar outros usuários
        await self.broadcast_to_all({
            "type": "conversation_status_changed",
            "conversation_id": conversation_id,
            "status": "assigned",
            "assigned_user_id": assigned_user_id
        })
    
    async def notify_typing(self, conversation_id: str, user_id: int, is_typing: bool):
        """Notificar que usuário está digitando"""
        notification = {
            "type": "typing",
            "conversation_id": conversation_id,
            "user_id": user_id,
            "is_typing": is_typing,
            "timestamp": datetime.now().isoformat()
        }
        
        # Enviar para todos exceto o próprio usuário
        for uid in self.active_connections:
            if uid != user_id:
                await self.send_personal_message(notification, uid)
    
    async def notify_instance_status(self, instance_name: str, status: str, qr_code: str = None):
        """Notificar mudança de status da instância"""
        notification = {
            "type": "instance_status",
            "instance_name": instance_name,
            "status": status,
            "qr_code": qr_code,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_to_all(notification)
    
    def get_online_users(self) -> List[int]:
        """Obter lista de usuários online"""
        return list(self.online_users)
    
    def is_user_online(self, user_id: int) -> bool:
        """Verificar se usuário está online"""
        return user_id in self.online_users


# Instância global do manager
whatsapp_ws_manager = WhatsAppWebSocketManager()
