"""
Modelo de banco de dados para armazenar tokens do Outlook.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.database.connection import Base


class OutlookTokenDB(Base):
    """Modelo para armazenar tokens do Outlook no banco de dados."""

    __tablename__ = "outlook_tokens"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("usuarios.id"), unique=True, index=True)
    outlook_user_id = Column(String(255), unique=True, index=True)
    outlook_email = Column(String(255), index=True)
    outlook_name = Column(String(255))

    access_token = Column(Text, nullable=False)
    refresh_token = Column(Text)
    token_type = Column(String(50), default="Bearer")
    scope = Column(Text)  # JSON string of scopes

    expires_at = Column(DateTime(timezone=True))
    criado_em = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    atualizado_em = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    is_active = Column(Boolean, default=True)
    last_used = Column(DateTime, default=datetime.utcnow)

    # Relacionamento com usuário (comentado para evitar importação circular)
    # user = relationship("Usuario", back_populates="outlook_tokens")

    def __repr__(self):
        return f"<OutlookTokenDB(user_id={self.user_id}, outlook_email={self.outlook_email})>"

    def is_expired(self) -> bool:
        """Verifica se o token está expirado."""
        if not self.expires_at:
            return True
        from datetime import timezone
        now = datetime.now(timezone.utc)
        expires_at = self.expires_at.replace(tzinfo=timezone.utc) if self.expires_at.tzinfo is None else self.expires_at
        return now >= expires_at

    def expires_soon(self, minutes: int = 5) -> bool:
        """Verifica se o token expira em breve."""
        if not self.expires_at:
            return True
        from datetime import timedelta, timezone
        now = datetime.now(timezone.utc)
        expires_at = self.expires_at.replace(tzinfo=timezone.utc) if self.expires_at.tzinfo is None else self.expires_at
        return now + timedelta(minutes=minutes) >= expires_at
