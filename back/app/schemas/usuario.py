"""
Schemas Pydantic para validação de dados de usuários.
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime
from enum import Enum

class NivelUsuarioEnum(str, Enum):
    """Enum para níveis de usuário."""
    ADMINISTRADOR = "administrador"
    AGENTE = "agente"

class UsuarioBase(BaseModel):
    """Schema base para usuário."""
    nome: str = Field(..., min_length=2, max_length=100, description="Nome do usuário")
    sobrenome: str = Field(..., min_length=2, max_length=100, description="Sobrenome do usuário")
    login: str = Field(..., min_length=3, max_length=50, description="Login único do usuário")
    nivel_usuario: NivelUsuarioEnum = Field(..., description="Nível de acesso do usuário")
    email_corporativo: EmailStr = Field(..., description="Email corporativo para integração Outlook")
    ativo: bool = Field(True, description="Se o usuário está ativo")

    @validator('login')
    def validate_login(cls, v):
        """Validar formato do login."""
        if not v.isalnum():
            raise ValueError('Login deve conter apenas letras e números')
        return v.lower()

class UsuarioCreate(UsuarioBase):
    """Schema para criação de usuário."""
    senha: str = Field(..., min_length=6, max_length=100, description="Senha do usuário")
    senha_email_corporativo: str = Field(..., min_length=1, max_length=255, description="Senha do email corporativo")

    @validator('senha')
    def validate_senha(cls, v):
        """Validar força da senha."""
        if len(v) < 6:
            raise ValueError('Senha deve ter pelo menos 6 caracteres')
        return v

class UsuarioUpdate(BaseModel):
    """Schema para atualização de usuário."""
    nome: Optional[str] = Field(None, min_length=2, max_length=100)
    sobrenome: Optional[str] = Field(None, min_length=2, max_length=100)
    login: Optional[str] = Field(None, min_length=3, max_length=50)
    senha: Optional[str] = Field(None, min_length=6, max_length=100)
    nivel_usuario: Optional[NivelUsuarioEnum] = None
    email_corporativo: Optional[EmailStr] = None
    senha_email_corporativo: Optional[str] = Field(None, min_length=1, max_length=255)
    ativo: Optional[bool] = None

    @validator('login')
    def validate_login(cls, v):
        """Validar formato do login."""
        if v and not v.isalnum():
            raise ValueError('Login deve conter apenas letras e números')
        return v.lower() if v else v

class UsuarioResponse(UsuarioBase):
    """Schema para resposta de usuário (sem senhas)."""
    id: int
    criado_em: datetime
    atualizado_em: datetime
    ultimo_login: Optional[datetime] = None

    class Config:
        from_attributes = True

class UsuarioLogin(BaseModel):
    """Schema para login de usuário."""
    login: str = Field(..., description="Login do usuário")
    senha: str = Field(..., description="Senha do usuário")

class UsuarioLoginResponse(BaseModel):
    """Schema para resposta de login."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    usuario: UsuarioResponse

class ChangePasswordRequest(BaseModel):
    """Schema para mudança de senha."""
    senha_atual: str = Field(..., description="Senha atual")
    nova_senha: str = Field(..., min_length=6, max_length=100, description="Nova senha")
    confirmar_senha: str = Field(..., description="Confirmação da nova senha")

    @validator('confirmar_senha')
    def passwords_match(cls, v, values):
        """Validar se as senhas coincidem."""
        if 'nova_senha' in values and v != values['nova_senha']:
            raise ValueError('Senhas não coincidem')
        return v
