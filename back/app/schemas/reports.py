from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import date, datetime

class ReportRequestBase(BaseModel):
    """
    Modelo base para solicitações de relatórios.
    """
    start_date: date = Field(..., description="Data inicial do relatório")
    end_date: date = Field(..., description="Data final do relatório")
    queues: List[int] = Field(..., description="Lista de IDs das filas")

class DistributionReportRequest(ReportRequestBase):
    """
    Modelo para solicitação de relatório de distribuição.
    """
    pass

class SatisfactionSurveyReportRequest(ReportRequestBase):
    """
    Modelo para solicitação de relatório de pesquisa de satisfação.
    """
    pass

class ReportRequest(ReportRequestBase):
    """
    Modelo genérico para solicitação de relatórios.
    """
    pass

# ==================== NOVOS MODELOS AVANÇADOS ====================

class TimingMetricsRequest(BaseModel):
    """
    Solicitação para métricas de tempo de atendimento.
    """
    start_date: date = Field(..., description="Data inicial")
    end_date: date = Field(..., description="Data final")
    queue_ids: Optional[List[int]] = Field(None, description="IDs das filas específicas")
    service_level_threshold: int = Field(20, description="Limite em segundos para service level")

class AgentPerformanceRequest(BaseModel):
    """
    Solicitação para métricas de performance de agentes.
    """
    start_date: date = Field(..., description="Data inicial")
    end_date: date = Field(..., description="Data final")
    agent_ids: Optional[List[str]] = Field(None, description="IDs dos agentes específicos")

class SLAMetricsRequest(BaseModel):
    """
    Solicitação para métricas de SLA.
    """
    start_date: date = Field(..., description="Data inicial")
    end_date: date = Field(..., description="Data final")
    service_level_threshold: int = Field(20, description="Limite em segundos para service level")
    queue_ids: Optional[List[int]] = Field(None, description="IDs das filas")

# ==================== MODELOS DE RESPOSTA ====================

class TimingMetrics(BaseModel):
    """
    Métricas de tempo de atendimento.
    """
    asa: float = Field(..., description="Average Speed of Answer (segundos)")
    aht: float = Field(..., description="Average Handle Time (segundos)")
    att: float = Field(..., description="Average Talk Time (segundos)")
    acw: float = Field(..., description="After Call Work (segundos)")
    service_level: float = Field(..., description="Service Level (%)")
    abandonment_rate: float = Field(..., description="Taxa de abandono (%)")
    total_calls: int = Field(..., description="Total de chamadas")
    answered_calls: int = Field(..., description="Chamadas atendidas")
    abandoned_calls: int = Field(..., description="Chamadas abandonadas")

class AgentPerformance(BaseModel):
    """
    Performance individual de agente.
    """
    agent_id: str = Field(..., description="ID do agente")
    agent_name: str = Field(..., description="Nome do agente")
    calls_handled: int = Field(..., description="Chamadas atendidas")
    talk_time: float = Field(..., description="Tempo de conversa (segundos)")
    break_time: float = Field(..., description="Tempo em pausa (segundos)")
    login_time: float = Field(..., description="Tempo logado (segundos)")
    occupancy_rate: float = Field(..., description="Taxa de ocupação (%)")
    productivity_score: float = Field(..., description="Score de produtividade")
    calls_per_hour: float = Field(..., description="Chamadas por hora")
    average_call_duration: float = Field(..., description="Duração média das chamadas")
    first_call_resolution: float = Field(..., description="Resolução na primeira chamada (%)")

class QueueMetrics(BaseModel):
    """
    Métricas por fila.
    """
    queue_id: int = Field(..., description="ID da fila")
    queue_name: str = Field(..., description="Nome da fila")
    total_calls: int = Field(..., description="Total de chamadas")
    answered_calls: int = Field(..., description="Chamadas atendidas")
    abandoned_calls: int = Field(..., description="Chamadas abandonadas")
    average_wait_time: float = Field(..., description="Tempo médio de espera")
    service_level: float = Field(..., description="Nível de serviço (%)")
    peak_hour: str = Field(..., description="Hora de pico")
    peak_calls: int = Field(..., description="Chamadas no pico")

class SLAMetrics(BaseModel):
    """
    Métricas de SLA e compliance.
    """
    service_level_target: int = Field(..., description="Meta de service level (segundos)")
    service_level_achieved: float = Field(..., description="Service level alcançado (%)")
    sla_compliance: float = Field(..., description="Compliance com SLA (%)")
    calls_within_sla: int = Field(..., description="Chamadas dentro do SLA")
    calls_outside_sla: int = Field(..., description="Chamadas fora do SLA")
    average_response_time: float = Field(..., description="Tempo médio de resposta")
    worst_performance_hour: str = Field(..., description="Pior hora de performance")
    best_performance_hour: str = Field(..., description="Melhor hora de performance")

class ReportResponse(BaseModel):
    """
    Modelo para resposta de relatórios.
    """
    data: Dict[str, Any] = Field(..., description="Dados do relatório")

    class Config:
        json_schema_extra = {
            "example": {
                "data": {
                    "status": "success",
                    "message": "Relatório gerado com sucesso",
                    "report_data": {}
                }
            }
        }
