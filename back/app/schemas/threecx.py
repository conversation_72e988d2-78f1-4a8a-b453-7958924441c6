from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class CallDirection(str, Enum):
    INBOUND = "inbound"
    OUTBOUND = "outbound"
    INTERNAL = "internal"

class CallStatus(str, Enum):
    ANSWERED = "answered"
    MISSED = "missed"
    VOICEMAIL = "voicemail"
    REJECTED = "rejected"
    BUSY = "busy"
    FAILED = "failed"

class AgentStatusEnum(str, Enum):
    AVAILABLE = "available"
    BUSY = "busy"
    AWAY = "away"
    OFFLINE = "offline"
    ON_CALL = "on_call"
    AFTER_CALL_WORK = "after_call_work"

class CallRecordBase(BaseModel):
    caller_number: str
    caller_name: Optional[str] = None
    called_number: str
    called_name: Optional[str] = None
    direction: CallDirection
    duration: int = Field(..., description="Duração da chamada em segundos")
    status: CallStatus
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None
    recording_url: Optional[str] = None
    notes: Optional[str] = None

class CallRecordCreate(CallRecordBase):
    start_time: datetime
    end_time: Optional[datetime] = None

class CallRecord(CallRecordBase):
    id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class CallRecordResponse(CallRecord):
    pass

class AgentStatus(BaseModel):
    id: str
    name: str
    extension: str
    status: AgentStatusEnum
    current_call_id: Optional[str] = None
    last_activity: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class AgentStatusUpdate(BaseModel):
    status: AgentStatusEnum

class WebhookEvent(str, Enum):
    CALL_STARTED = "call_started"
    CALL_ENDED = "call_ended"
    CALL_ANSWERED = "call_answered"
    CALL_MISSED = "call_missed"
    AGENT_STATUS_CHANGED = "agent_status_changed"

class WebhookPayload(BaseModel):
    event: WebhookEvent
    timestamp: datetime
    data: dict
