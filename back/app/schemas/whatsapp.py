"""
Schemas para integração com WhatsApp via Evolution API
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class MessageType(str, Enum):
    """Tipos de mensagem WhatsApp"""
    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"
    STICKER = "sticker"
    LOCATION = "location"
    CONTACT = "contact"


class MessageStatus(str, Enum):
    """Status da mensagem"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"


class ConversationStatus(str, Enum):
    """Status da conversa"""
    OPEN = "open"
    CLOSED = "closed"
    WAITING = "waiting"
    ASSIGNED = "assigned"


class InstanceStatus(str, Enum):
    """Status da instância WhatsApp"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    QR_CODE = "qr_code"


# ==================== Schemas de Request ====================

class SendMessageRequest(BaseModel):
    """Request para enviar mensagem"""
    number: str = Field(..., description="Número do destinatário")
    text: str = Field(..., description="Texto da mensagem")
    instance_name: str = Field(..., description="Nome da instância")


class CreateInstanceRequest(BaseModel):
    """Request para criar instância"""
    instance_name: str = Field(..., description="Nome da instância")
    webhook_url: Optional[str] = Field(None, description="URL do webhook")


class AssignConversationRequest(BaseModel):
    """Request para atribuir conversa"""
    user_id: int = Field(..., description="ID do usuário")


# ==================== Schemas de Response ====================

class WhatsAppMessage(BaseModel):
    """Mensagem do WhatsApp"""
    id: str
    conversation_id: str
    sender_type: str  # 'customer' ou 'agent'
    sender_name: str
    sender_number: str
    content: str
    message_type: MessageType = MessageType.TEXT
    status: MessageStatus = MessageStatus.SENT
    timestamp: datetime
    evolution_message_id: Optional[str] = None
    
    class Config:
        from_attributes = True


class WhatsAppConversation(BaseModel):
    """Conversa do WhatsApp"""
    id: str
    customer_name: str
    customer_number: str
    assigned_user_id: Optional[int] = None
    assigned_user_name: Optional[str] = None
    instance_id: str
    status: ConversationStatus = ConversationStatus.OPEN
    last_message: Optional[str] = None
    last_message_time: Optional[datetime] = None
    unread_count: int = 0
    created_at: datetime
    updated_at: datetime
    messages: List[WhatsAppMessage] = []
    
    class Config:
        from_attributes = True


class WhatsAppInstance(BaseModel):
    """Instância WhatsApp"""
    id: str
    name: str
    status: InstanceStatus = InstanceStatus.DISCONNECTED
    qr_code: Optional[str] = None
    webhook_url: Optional[str] = None
    phone_number: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class WhatsAppStats(BaseModel):
    """Estatísticas do WhatsApp"""
    total_conversations: int = 0
    active_conversations: int = 0
    waiting_conversations: int = 0
    total_messages_today: int = 0
    unread_messages: int = 0
    connected_instances: int = 0


# ==================== Schemas de Webhook ====================

class EvolutionWebhookMessage(BaseModel):
    """Webhook de mensagem da Evolution API"""
    key: Dict[str, Any]
    message: Dict[str, Any]
    messageTimestamp: int
    status: Optional[str] = None


class EvolutionWebhookData(BaseModel):
    """Dados do webhook da Evolution API"""
    instance: str
    data: EvolutionWebhookMessage


class EvolutionConnectionUpdate(BaseModel):
    """Atualização de conexão da Evolution API"""
    instance: str
    state: str
    statusReason: Optional[str] = None


# ==================== Schemas de Evolution API ====================

class EvolutionInstanceInfo(BaseModel):
    """Informações da instância Evolution API"""
    instance_name: str
    status: str
    serverUrl: Optional[str] = None
    apikey: Optional[str] = None


class EvolutionSendMessageResponse(BaseModel):
    """Resposta do envio de mensagem Evolution API"""
    key: Dict[str, Any]
    message: Dict[str, Any]
    messageTimestamp: int
    status: str


class EvolutionQRCodeResponse(BaseModel):
    """Resposta do QR Code Evolution API"""
    base64: str
    code: str
