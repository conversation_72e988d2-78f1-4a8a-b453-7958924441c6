-- Migração para adicionar tabela de tokens do Outlook
-- Data: 2025-06-23
-- Descrição: Adiciona persistência de tokens OAuth2 do Microsoft Graph

-- Criar tabela outlook_tokens
CREATE TABLE IF NOT EXISTS outlook_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES usuarios(id) ON DELETE CASCADE,
    outlook_user_id VARCHAR(255) UNIQUE NOT NULL,
    outlook_email VARCHAR(255) NOT NULL,
    outlook_name VARCHAR(255),
    
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    token_type VARCHAR(50) DEFAULT 'Bearer',
    scope TEXT, -- JSON string of scopes
    
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    is_active BOOLEAN DEFAULT TRUE,
    last_used TIMESTAMP DEFAULT CURRENT_TIMES<PERSON>MP,
    
    CONSTRAINT unique_user_outlook UNIQUE(user_id)
);

-- <PERSON><PERSON><PERSON> índices para performance
CREATE INDEX IF NOT EXISTS idx_outlook_tokens_user_id ON outlook_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_outlook_tokens_outlook_user_id ON outlook_tokens(outlook_user_id);
CREATE INDEX IF NOT EXISTS idx_outlook_tokens_outlook_email ON outlook_tokens(outlook_email);
CREATE INDEX IF NOT EXISTS idx_outlook_tokens_is_active ON outlook_tokens(is_active);
CREATE INDEX IF NOT EXISTS idx_outlook_tokens_expires_at ON outlook_tokens(expires_at);

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_outlook_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_outlook_tokens_updated_at
    BEFORE UPDATE ON outlook_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_outlook_tokens_updated_at();

-- Comentários na tabela
COMMENT ON TABLE outlook_tokens IS 'Armazena tokens OAuth2 do Microsoft Graph para integração com Outlook';
COMMENT ON COLUMN outlook_tokens.user_id IS 'ID do usuário do sistema';
COMMENT ON COLUMN outlook_tokens.outlook_user_id IS 'ID único do usuário no Microsoft Graph';
COMMENT ON COLUMN outlook_tokens.outlook_email IS 'Email do usuário no Outlook';
COMMENT ON COLUMN outlook_tokens.outlook_name IS 'Nome de exibição do usuário no Outlook';
COMMENT ON COLUMN outlook_tokens.access_token IS 'Token de acesso OAuth2';
COMMENT ON COLUMN outlook_tokens.refresh_token IS 'Token de renovação OAuth2';
COMMENT ON COLUMN outlook_tokens.scope IS 'Escopos de permissão em formato JSON';
COMMENT ON COLUMN outlook_tokens.expires_at IS 'Data/hora de expiração do access_token';
COMMENT ON COLUMN outlook_tokens.is_active IS 'Indica se o token está ativo';
COMMENT ON COLUMN outlook_tokens.last_used IS 'Última vez que o token foi usado';

-- Inserir configuração padrão se não existir
INSERT INTO configuracoes (chave, valor, descricao, categoria, tipo_valor, created_at, updated_at)
VALUES 
    ('outlook_token_cleanup_days', '30', 'Dias para manter tokens expirados antes da limpeza automática', 'outlook', 'integer', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (chave) DO NOTHING;

-- Log da migração
INSERT INTO configuracoes (chave, valor, descricao, categoria, tipo_valor, created_at, updated_at)
VALUES 
    ('migration_outlook_tokens', '2025-06-23', 'Data da migração da tabela outlook_tokens', 'system', 'string', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (chave) DO UPDATE SET 
    valor = EXCLUDED.valor,
    updated_at = CURRENT_TIMESTAMP;
