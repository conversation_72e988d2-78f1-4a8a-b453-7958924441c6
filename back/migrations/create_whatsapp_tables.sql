-- Migração para criar tabelas do WhatsApp
-- Execute este SQL no PostgreSQL

-- Criar tipos ENUM
CREATE TYPE instance_status AS ENUM ('connected', 'disconnected', 'connecting', 'qr_code');
CREATE TYPE conversation_status AS ENUM ('open', 'closed', 'waiting', 'assigned');
CREATE TYPE message_type AS ENUM ('text', 'image', 'audio', 'video', 'document', 'sticker', 'location', 'contact');
CREATE TYPE message_status AS ENUM ('pending', 'sent', 'delivered', 'read', 'failed');

-- Tabela de instâncias WhatsApp
CREATE TABLE whatsapp_instances (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    status instance_status DEFAULT 'disconnected',
    qr_code TEXT,
    webhook_url VARCHAR(500),
    phone_number VARCHAR(20),
    evolution_instance_id VARCHAR(100),
    api_key VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para instâncias
CREATE INDEX idx_whatsapp_instances_name ON whatsapp_instances(name);
CREATE INDEX idx_whatsapp_instances_status ON whatsapp_instances(status);

-- Tabela de conversas WhatsApp
CREATE TABLE whatsapp_conversations (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(200) UNIQUE NOT NULL,
    customer_name VARCHAR(200) NOT NULL,
    customer_number VARCHAR(50) NOT NULL,
    assigned_user_id INTEGER REFERENCES usuarios(id),
    instance_id INTEGER REFERENCES whatsapp_instances(id) NOT NULL,
    status conversation_status DEFAULT 'waiting',
    last_message TEXT,
    last_message_time TIMESTAMP WITH TIME ZONE,
    unread_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para conversas
CREATE INDEX idx_whatsapp_conversations_external_id ON whatsapp_conversations(external_id);
CREATE INDEX idx_whatsapp_conversations_customer_number ON whatsapp_conversations(customer_number);
CREATE INDEX idx_whatsapp_conversations_status ON whatsapp_conversations(status);
CREATE INDEX idx_whatsapp_conversations_instance_id ON whatsapp_conversations(instance_id);
CREATE INDEX idx_whatsapp_conversations_assigned_user_id ON whatsapp_conversations(assigned_user_id);

-- Tabela de mensagens WhatsApp
CREATE TABLE whatsapp_messages (
    id SERIAL PRIMARY KEY,
    external_id VARCHAR(200) UNIQUE NOT NULL,
    conversation_id INTEGER REFERENCES whatsapp_conversations(id) NOT NULL,
    sender_type VARCHAR(20) NOT NULL,
    sender_name VARCHAR(200) NOT NULL,
    sender_number VARCHAR(50),
    content TEXT NOT NULL,
    message_type message_type DEFAULT 'text',
    status message_status DEFAULT 'sent',
    media_url VARCHAR(500),
    media_filename VARCHAR(200),
    media_mimetype VARCHAR(100),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para mensagens
CREATE INDEX idx_whatsapp_messages_external_id ON whatsapp_messages(external_id);
CREATE INDEX idx_whatsapp_messages_conversation_id ON whatsapp_messages(conversation_id);
CREATE INDEX idx_whatsapp_messages_timestamp ON whatsapp_messages(timestamp);
CREATE INDEX idx_whatsapp_messages_sender_type ON whatsapp_messages(sender_type);

-- Tabela de templates de mensagem
CREATE TABLE whatsapp_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES usuarios(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para templates
CREATE INDEX idx_whatsapp_templates_category ON whatsapp_templates(category);
CREATE INDEX idx_whatsapp_templates_is_active ON whatsapp_templates(is_active);

-- Tabela de respostas automáticas
CREATE TABLE whatsapp_auto_replies (
    id SERIAL PRIMARY KEY,
    trigger_keywords TEXT NOT NULL,
    response_message TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    instance_id INTEGER REFERENCES whatsapp_instances(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para respostas automáticas
CREATE INDEX idx_whatsapp_auto_replies_is_active ON whatsapp_auto_replies(is_active);
CREATE INDEX idx_whatsapp_auto_replies_priority ON whatsapp_auto_replies(priority);
CREATE INDEX idx_whatsapp_auto_replies_instance_id ON whatsapp_auto_replies(instance_id);

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_whatsapp_instances_updated_at BEFORE UPDATE ON whatsapp_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_conversations_updated_at BEFORE UPDATE ON whatsapp_conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_templates_updated_at BEFORE UPDATE ON whatsapp_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_whatsapp_auto_replies_updated_at BEFORE UPDATE ON whatsapp_auto_replies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
