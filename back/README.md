# Backend do Sistema Omnichannel Amvox

Este é o backend do sistema Omnichannel Amvox, desenvolvido em Python com FastAPI.

O sistema integra diferentes canais de comunicação (3CX, Metasix/WhatsApp, Outlook) e sistemas internos (Telecontrol, Gestão Web) para atendimento automatizado e humano.

## Requisitos

- Python 3.8+
- Dependências listadas em `requirements.txt`

## Instalação

1. Crie um ambiente virtual:
   ```bash
   python -m venv venv
   ```

2. Ative o ambiente virtual:
   - No Windows:
     ```bash
     venv\Scripts\activate
     ```
   - No Linux/Mac:
     ```bash
     source venv/bin/activate
     ```

3. Instale as dependências:
   ```bash
   pip install -r requirements.txt
   ```

4. Crie um arquivo `.env` baseado no `.env.example`:
   ```bash
   cp .env.example .env
   ```

5. Edite o arquivo `.env` com suas configurações específicas.

## Executando o servidor

```bash
uvicorn app.main:app --reload
```

O servidor estará disponível em `http://localhost:8000`.

## Documentação da API

A documentação da API estará disponível em:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Estrutura do Projeto

```
back/
├── app/                        # Código principal da aplicação
│   ├── __init__.py
│   ├── main.py                 # Ponto de entrada da aplicação
│   ├── api/                    # Endpoints da API
│   │   └── v1/                 # API versão 1
│   │       └── threecx.py      # Endpoints para integração com 3CX
│   ├── config/                 # Configurações da aplicação
│   │   └── settings.py         # Configurações baseadas em variáveis de ambiente
│   ├── schemas/                # Esquemas Pydantic
│   │   └── threecx.py          # Modelos de dados para integração com 3CX
│   └── services/               # Lógica de negócios
│       └── integrations/       # Integrações com sistemas externos
│           └── threecx/        # Integração com 3CX
│               └── client.py   # Cliente para API do 3CX
├── tests/                      # Testes automatizados
├── .env.example                # Exemplo de variáveis de ambiente
└── requirements.txt            # Dependências do projeto
```

## Integrações

### 3CX (Sistema Telefônico)

A integração com o 3CX permite:

- Consultar histórico de chamadas
- Obter detalhes de chamadas específicas
- Verificar status dos agentes
- Atualizar status dos agentes
- Receber webhooks para eventos em tempo real (chamadas iniciadas, finalizadas, etc.)

Para configurar a integração com o 3CX, defina as seguintes variáveis de ambiente:

```
THREECX_BASE_URL=https://pbx.example.com
THREECX_API_KEY=your-api-key
THREECX_TENANT_ID=your-tenant-id
```

### API de Relatórios

A integração com a API de relatórios permite:

- Obter relatórios de distribuição
- Obter relatórios de pesquisa de satisfação

#### Endpoints disponíveis:

1. **Relatório de Distribuição**
   - **URL**: `/api/v1/reports/distribution`
   - **Método**: POST
   - **Corpo da requisição**:
     ```json
     {
       "start_date": "2025-04-01",
       "end_date": "2025-04-01",
       "queues": [803]
     }
     ```

2. **Relatório de Pesquisa de Satisfação**
   - **URL**: `/api/v1/reports/satisfaction-survey`
   - **Método**: POST
   - **Corpo da requisição**:
     ```json
     {
       "start_date": "2025-04-01",
       "end_date": "2025-04-01",
       "queues": [802]
     }
     ```

As credenciais para acesso à API de relatórios são configuradas através das seguintes variáveis de ambiente:

```
REPORTS_USER=amvox
REPORTS_PASSWORD=super_7894
```
