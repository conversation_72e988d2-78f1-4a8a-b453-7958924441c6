# Configurações gerais
APP_NAME=Amvox Omnichannel API
APP_VERSION=0.1.0
DEBUG=True

# Configurações do servidor
HOST=0.0.0.0
PORT=8000

# Configurações de CORS
CORS_ORIGINS=http://localhost:3000

# Configurações do 3CX
THREECX_REPORTS_URL=http://ticmobilerb.ddns.net/gdacip/apijson.php
THREECX_REPORTS_SERVER=http://ticmobilerb.ddns.net
THREECX_API_USER=amvox
THREECX_API_PASSWORD=super_7894
THREECX_ATTENDANCE_URL=https://callcentermobile.ddns.net/gdacx/gmag
THREECX_USER_LOGIN=teste_amvox
THREECX_USER_PASSWORD=1111

# Configurações da API de relatórios (integradas ao 3CX)
REPORTS_USER=amvox
REPORTS_PASSWORD=super_7894

# Configurações do banco de dados
DATABASE_URL=**********************************************************/amvox_omnichannel

# Configurações Microsoft Azure OAuth2 (Integração com Outlook)
MICROSOFT_CLIENT_ID=your_client_id_here
MICROSOFT_CLIENT_SECRET=your_client_secret_here
MICROSOFT_TENANT_ID=your_tenant_id_here
MICROSOFT_REDIRECT_URI=http://localhost:8001/api/v1/outlook/callback

# Configurações da Evolution API (WhatsApp)
EVOLUTION_API_URL=http://localhost:8080
EVOLUTION_API_KEY=B6D711FCDE4D4FD5936544120E713976
EVOLUTION_WEBHOOK_URL=http://localhost:8001/api/v1/whatsapp/webhook
