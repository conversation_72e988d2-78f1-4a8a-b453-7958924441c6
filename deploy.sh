#!/bin/bash

# Script de Deploy Completo - Amvox Omnichannel
# Suporta múltiplos ambientes: development, staging, production
# Uso: ./deploy.sh [environment] [options]

set -e

# Configurações
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="amvox_omnichannel"
DEFAULT_ENV="development"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

# Mostrar banner
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    AMVOX OMNICHANNEL                         ║"
    echo "║                     Deploy Script v2.0                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Mostrar ajuda
show_help() {
    echo "Uso: $0 [environment] [options]"
    echo ""
    echo "Ambientes:"
    echo "  development  - Ambiente de desenvolvimento (padrão)"
    echo "  staging      - Ambiente de homologação"
    echo "  production   - Ambiente de produção"
    echo ""
    echo "Opções:"
    echo "  --no-backup     - Pular backup do banco de dados"
    echo "  --no-build      - Pular build das imagens"
    echo "  --force         - Forçar deploy mesmo com avisos"
    echo "  --health-check  - Apenas verificar saúde dos serviços"
    echo "  --logs          - Mostrar logs após deploy"
    echo "  --help          - Mostrar esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0                           # Deploy development"
    echo "  $0 staging                   # Deploy staging"
    echo "  $0 production --no-backup    # Deploy production sem backup"
    echo "  $0 --health-check            # Verificar saúde"
}

# Configuração do ambiente
ENVIRONMENT=${1:-$DEFAULT_ENV}
SKIP_BACKUP=false
SKIP_BUILD=false
FORCE_DEPLOY=false
SHOW_LOGS=false
HEALTH_CHECK_ONLY=false

# Parse argumentos
while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENVIRONMENT="$1"
            shift
            ;;
        --no-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --no-build)
            SKIP_BUILD=true
            shift
            ;;
        --force)
            FORCE_DEPLOY=true
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --health-check)
            HEALTH_CHECK_ONLY=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Opção desconhecida: $1"
            show_help
            exit 1
            ;;
    esac
done

# Verificar pré-requisitos
check_prerequisites() {
    log "Verificando pré-requisitos..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker não está instalado"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose não está instalado"
    fi
    
    if [[ ! -f "docker-compose.yml" ]]; then
        error "Execute o script no diretório raiz do projeto"
    fi
    
    success "Pré-requisitos verificados"
}

# Verificar configuração do ambiente
check_environment_config() {
    log "Verificando configuração do ambiente: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        development)
            if [[ ! -f "back/.env" ]]; then
                warning "Arquivo back/.env não encontrado, usando padrões"
            fi
            ;;
        staging)
            if [[ ! -f "back/.env.staging" ]]; then
                error "Arquivo back/.env.staging não encontrado. Execute: ./setup-secrets.sh setup staging"
            fi
            if [[ ! -f "docker-compose.staging.yml" ]]; then
                error "Arquivo docker-compose.staging.yml não encontrado"
            fi
            ;;
        production)
            if [[ ! -f "back/.env.production" ]]; then
                error "Arquivo back/.env.production não encontrado"
            fi
            if [[ ! -f "docker-compose.prod.yml" ]]; then
                error "Arquivo docker-compose.prod.yml não encontrado"
            fi
            if [[ ! -f "secrets/postgres_password.txt" ]]; then
                error "Secrets não configurados. Execute: ./setup-secrets.sh setup production"
            fi
            ;;
    esac
    
    success "Configuração do ambiente verificada"
}

# Fazer backup do banco
backup_database() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        warning "Backup do banco de dados pulado"
        return
    fi
    
    log "Fazendo backup do banco de dados..."
    mkdir -p backups
    
    local backup_name="backup_${ENVIRONMENT}_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="backups/$backup_name"
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose exec postgres pg_dump -U amvox_user amvox_db > "$backup_path" 2>/dev/null || {
            warning "Não foi possível fazer backup do banco em execução"
            return
        }
        success "Backup criado: $backup_path"
    else
        warning "Nenhum container rodando, pulando backup"
    fi
    
    # Limpar backups antigos (manter últimos 10)
    find backups/ -name "backup_${ENVIRONMENT}_*.sql" -type f | sort -r | tail -n +11 | xargs rm -f 2>/dev/null || true
}

# Parar containers
stop_containers() {
    log "Parando containers do ambiente: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        staging)
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml down || true
            ;;
        production)
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml down || true
            ;;
        *)
            docker-compose down || true
            ;;
    esac
    
    success "Containers parados"
}

# Construir imagens
build_images() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        warning "Build das imagens pulado"
        return
    fi
    
    log "Construindo imagens para: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        staging)
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml build --no-cache
            ;;
        production)
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml build --no-cache
            ;;
        *)
            docker-compose build
            ;;
    esac
    
    success "Imagens construídas"
}

# Iniciar containers
start_containers() {
    log "Iniciando containers para: $ENVIRONMENT"
    
    case $ENVIRONMENT in
        staging)
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
            ;;
        production)
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
            ;;
        *)
            docker-compose up -d
            ;;
    esac
    
    success "Containers iniciados"
}

# Verificar saúde dos serviços
health_check() {
    local max_attempts=30
    local attempt=1
    
    log "Verificando saúde dos serviços..."
    
    while [[ $attempt -le $max_attempts ]]; do
        local healthy=true
        
        if ! curl -f -s http://localhost:8001/health > /dev/null 2>&1; then
            healthy=false
        fi
        
        if ! curl -f -s http://localhost:3000 > /dev/null 2>&1; then
            healthy=false
        fi
        
        if [[ "$healthy" == "true" ]]; then
            success "Todos os serviços estão saudáveis"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    error "Alguns serviços não estão respondendo após $max_attempts tentativas"
}

# Mostrar status final
show_status() {
    log "Status final do deploy:"
    echo ""
    
    case $ENVIRONMENT in
        staging)
            docker-compose -f docker-compose.yml -f docker-compose.staging.yml ps
            ;;
        production)
            docker-compose -f docker-compose.yml -f docker-compose.prod.yml ps
            ;;
        *)
            docker-compose ps
            ;;
    esac
    
    echo ""
    info "URLs de acesso:"
    case $ENVIRONMENT in
        staging)
            echo "  Frontend: https://staging.yourdomain.com"
            echo "  API: https://staging-api.yourdomain.com"
            ;;
        production)
            echo "  Frontend: https://yourdomain.com"
            echo "  API: https://api.yourdomain.com"
            ;;
        *)
            echo "  Frontend: http://localhost:3000"
            echo "  API: http://localhost:8001"
            ;;
    esac
}

# Função principal
main() {
    show_banner
    log "Iniciando deploy para ambiente: $ENVIRONMENT"
    
    if [[ "$HEALTH_CHECK_ONLY" == "true" ]]; then
        health_check
        exit 0
    fi
    
    check_prerequisites
    check_environment_config
    backup_database
    stop_containers
    build_images
    start_containers
    health_check
    show_status
    
    if [[ "$SHOW_LOGS" == "true" ]]; then
        echo ""
        log "Mostrando logs dos containers..."
        case $ENVIRONMENT in
            staging)
                docker-compose -f docker-compose.yml -f docker-compose.staging.yml logs --tail=50
                ;;
            production)
                docker-compose -f docker-compose.yml -f docker-compose.prod.yml logs --tail=50
                ;;
            *)
                docker-compose logs --tail=50
                ;;
        esac
    fi
    
    success "Deploy concluído com sucesso para: $ENVIRONMENT"
}

# Executar função principal
main
